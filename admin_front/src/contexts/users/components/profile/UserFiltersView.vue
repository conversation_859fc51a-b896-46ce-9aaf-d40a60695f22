<template>
  <div class="UserFiltersView">
    <div class="buttonContainer">
      <BaseButton>{{ $t('COMMON.EDIT') }}</BaseButton>
    </div>
    <FormGroup
      icon="tag"
      :title="$t(title)"
    >
      {{ $t(description) }}
      <div class="filterListContainer">
        <FilterBadges
          v-for="item in filterList"
          :key="item.key"
          :item="item"
        />
      </div>
    </FormGroup>
  </div>
</template>

<script setup>
import FormGroup from '@/contexts/shared/components/FormGroup.vue'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import FilterBadges from '@/contexts/users/components/profile/FilterBadges.vue'
import { UserProfileFilterModel } from '@/contexts/users/models/userProfileFilter.model.js'

defineProps({
  filterList: {
    type: Array,
    default: () => [
      new UserProfileFilterModel({ id: 1, category: 'Pais', filters: ['lorem'] }),
      new UserProfileFilterModel({ id: 2, category: 'Departamento', filters: ['Departamento 1', 'Departamento 2'] }),
      new UserProfileFilterModel({ id: 3, category: 'Filtro Ipsum Lorem', filters: ['lorem'] }),
      new UserProfileFilterModel({ id: 4, category: 'Filtro Lorem Ipsum', filters: ['lorem'] }),
    ],
  },
  title: { type: String, default: 'ANNOUNCEMENT_OBSERVATION.FILTER_INFO' },
  description: { type: String, default: 'USERS.FORM.FILTER1_DESCRIPTION' },
})
</script>
<style scoped lang="scss">
.UserFiltersView {
  .filterListContainer {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 1rem 0;
  }
}
</style>
