<template>
  <div class="UserInfoView container">
    <div class="avatarContainer">
      <img
        class="userAvatar"
        :src="user.avatar"
        :alt="user.fullName + ' avatar'"
      />
    </div>
    <div>
      <h2>{{ user.fullName }}</h2>
      <p class="link">{{ user.email }}</p>
      <div class="roles">
        <span>{{ $t('USER.ROLES.TITLE') }}:</span>
        <RolesBadges :roles="user.roles" />
      </div>
      <p>
        {{ $t('COMPANY.CODE') }}: <b>{{ user.code }}</b>
      </p>
      <p>
        {{ $t('USER.USER_FIELDS_FUNDAE.DNI') }}: <b>{{ user.dni }}</b>
      </p>
    </div>
    <div>
      <p>
        {{ $t('USERS.FORM.LOCALE') }}: <b>{{ user.localeName }}</b>
      </p>
      <p v-if="user.companyName">
        {{ $t('PAYMENT.BILLING.COMPANY') }}: <b>{{ user.companyName }}</b>
      </p>
      <p>
        {{ $t('TIMEZONE') }}: <b>{{ user.zone }}</b>
      </p>
      <p
        v-for="extra in user.extra"
        :key="extra.key"
      >
        {{ extra.label }}: <b>{{ extra.value }}</b>
      </p>
    </div>
  </div>
</template>

<script setup>
import { UserProfileModel } from '@/contexts/users/models/userProfile.model.js'
import RolesBadges from '@/contexts/users/components/RolesBadges.vue'

defineProps({
  user: { type: [UserProfileModel, Object], default: () => ({}) },
})
</script>

<style scoped lang="scss">
@use '@/contexts/shared/assets/styles/_breakpoints' as breakpoint;
.UserInfoView {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 0.5rem 2rem;
  width: 100%;
  padding: 2rem;

  .avatarContainer {

    img {
      width: 12rem;
      margin: 0;
    }
  }

  h2,
  p {
    margin: 0;
    word-break: break-word;
  }

  .roles,
  p {
    display: flex;
    align-items: center;
    justify-content: start;
    gap: 0.5rem;
  }

  .link {
    color: var(--color-primary);
  }

  .RolesBadges {
    margin: 0;
  }

  & > div {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  @media #{breakpoint.$breakpoint-md} {
    flex-direction: column;
    justify-content: space-between;
    .avatarContainer {
      margin: auto;
    }
  }
}
</style>
