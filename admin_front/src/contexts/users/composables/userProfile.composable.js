import { computed, inject, onMounted, reactive, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserProfileStore } from '@/contexts/users/stores/userProfile.store.js'
import { PageTitleModel } from '@/contexts/shared/models/pageTitle.model.js'
import { useI18n } from 'vue-i18n'
import UserStatsView from '@/contexts/users/components/profile/UserStatsView.vue'
import UserFiltersView from '@/contexts/users/components/profile/UserFiltersView.vue'
import TabModel from '@/contexts/shared/models/tab.model.js'
import { useRoute } from 'vue-router'
import { USER_PERMISSION_LIST } from '@/contexts/users/constants/userPermission.constants.js'

export function useUserProfileComposable() {
  const links = ref([])
  const { isLoading, loadingError, userData } = storeToRefs(useUserProfileStore())
  const { loadProfile } = useUserProfileStore()
  const i18n = useI18n()
  const route = useRoute()

  const tabsConfig = reactive({
    tabs: [],
    current: 'stats',
  })

  const currentTabView = computed(
    () =>
      ({
        stats: UserStatsView,
        filters: UserFiltersView,
        management: UserFiltersView,
      })[tabsConfig.current] || UserStatsView
  )

  const tabContent = computed(
    () =>
      ({
        stats: {},
        filters: {},
        management: {
          title: 'ANNOUNCEMENT_OBSERVATION.MANAGEMENT_INFO',
          description: 'USERS.FORM.FILTER2_DESCRIPTION',
        },
      })[tabsConfig.current] || {}
  )

  const tabEmitsHandler = computed(
    () =>
      ({
        stats: {},
        filters: {},
        management: {},
      })[tabsConfig.current] || {}
  )

  const user = inject('user')
  onMounted(async () => {
    await loadProfile(+route.params?.id || 0)
    links.value = [
      new PageTitleModel({ id: 1, title: i18n.t('USER.LABEL_IN_PLURAL'), name: 'users' }),
      new PageTitleModel({ id: 1, title: i18n.t('ANNOUNCEMENT.ERROR_TITLES.USER_PROFILES') }),
    ]

    tabsConfig.tabs = [
      new TabModel({ key: 'stats', title: i18n.t('ANNOUNCEMENT.STATS') }),
      new TabModel({ key: 'filters', title: i18n.t('USER.LABEL.FILTERS') }),
    ]

    if (user.isGranted(USER_PERMISSION_LIST.PROFILE_MANAGEMENT)) {
      tabsConfig.tabs.push(new TabModel({ key: 'management', title: i18n.t('USER.LABEL.MANAGEMENT') }))
    }
  })

  return { tabsConfig, currentTabView, tabContent, tabEmitsHandler, links, isLoading, loadingError, userData }
}
