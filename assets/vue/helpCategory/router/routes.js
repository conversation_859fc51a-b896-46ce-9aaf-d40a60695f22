import HomeView from "../views/HomeView.vue";
import FormView from "../views/FormView.vue";
import FormInformation from "../components/Form/Information.vue";

export default [
    {
        path: '/admin/apps/help-category',
        component: HomeView,
        name: 'Home'
    },
    {
        path: '/admin/apps/help-category/:id/form',// if id < 0, create
        component: FormView,
        children: [
            {
                path: "/",
                component: FormInformation,
                name: "C<PERSON>View"
            },
        ]
    }
];
