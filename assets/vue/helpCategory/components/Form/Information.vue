<script>
import {sync, get} from "vuex-pathify";
import Translation from "../../../common/components/Translation.vue";
import BaseSwitch from "../../../base/BaseSwitch.vue";


export default {
  name: "Information",
  components: {BaseSwitch, Translation},
  data() {
    return {
      locale: 'es',
      activeIndex: 0,
      warningLocales: {}
    };
  },
  computed: {
    defaultLocale: get('localeModule/defaultLocale'),
    userLocale: get("localeModule/userLocale"),
    name: sync('helpCategoryModule/form@name'),
    translations: sync('helpCategoryModule/form@translations'),
    translationFroalaConfiguration() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        pluginsEnabled: ["align"],
        toolbarButtons: {
          'moreText': {
            buttons: ['bold', 'italic', 'underline']
          },
        },
      };
    },
  },
  watch: {
    locale: {
      immediate: true,
      handler: function () {
        this.activeIndex = this.translations.findIndex(t => t.locale === this.locale);
        this.translations.forEach(t => {
          let warning = t.locale !== this.locale;
          if (t.locale !== this.locale)
            warning = t.name.length < 1;

          this.warningLocales[t.locale] = warning;
        })
      }
    }
  },
  created() {
    this.locale = this.userLocale;
    const actions = [];
    actions.push({
      name: this.$t("SAVE"),
      event: "onSave",
      class: "btn btn-primary",
    });

    if (this.$isGranted("ROLE_ADMIN") && this.$route.params.id != -1)
      actions.push({
        name: this.$t("DELETE"),
        event: "onDelete",
        class: "btn btn-danger",
      });

    this.$store.dispatch("contentTitleModule/setActions", {
      route: this.$route.name,
      actions,
    });
  }
}
</script>

<template>
<div class="Information">
  <div class="col-12">
    <translation v-model="locale" direction="horizontal" :warning="warningLocales">
      <template v-slot:content>
        <div class="col-12" v-for="t in translations" :key="t.locale" v-if="locale === t.locale">
          <div class="form-group col-12 required mb-0">
            <label>{{ $t('NAME') }}</label>
            <input type="text" class="form-control" v-model="t.name">
          </div>
        </div>
      </template>
    </translation>
  </div>
</div>
</template>

<style scoped lang="scss">
.Information {
  h4 {
    font-size: 16px;
    color: var(--color-neutral-darkest);
  }
  .Title {
    font-size: 18px;
    color: var(--color-neutral-darkest);
    &.BorderBottom {
      border-bottom: 1px solid var(--color-neutral-mid);
    }
  }
}
</style>
