<template>
  <div>
    <div
      class="modal fade"
      :id="identifier"
      tabindex="-1"
      aria-labelledby="exampleModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-body">
            <img
              v-if="image"
              :src="image"
              alt="image"
              onerror="this.onerror=null;this.src='assets/chapters/default-image.svg';"
            />
            <img v-else src="assets/chapters/default-image.svg" alt="image" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ModalView",
  props: {
    identifier: {
      type: String,
      required: true,
    },

    image: {
      type: String,
      default: "assets/chapters/default-image.svg",
    },
  },
};
</script>

 <style scoped lang="scss"> 
img {
  width: 100%;
  height: 40rem;
  object-fit: cover;
}
</style>
