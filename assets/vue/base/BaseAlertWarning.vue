<template>
  <div class="base-alert-warning">
    <div
      class="alert alert-warning alert-dismissible fade show text-dark alert-question-game"
      role="alert"
    >
      <img :src="`/assets/chapters/advertencia.svg`" />
      {{ message }}
      <button
        type="button"
        class="btn-close"
        data-bs-dismiss="alert"
        aria-label="Close"
      ></button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    message: {
      type: String,
      default: "",
      required: true,
    },
  },
};
</script>

 <style scoped lang="scss"> 
.base-alert-warning {
  .alert-question-game {
    border-radius: 5px !important;
    font-weight: bold;

    img {
      width: 2rem;
      padding-top: -1rem;
    }
  }
}
</style>