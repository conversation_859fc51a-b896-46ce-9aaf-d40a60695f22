<template>
  <div class="Home">
    <div class="Home--header">
      <div class="Home--header--content">
        <h1>{{ useI18n ? $t(title) : title }}</h1>
        <p v-html="useI18n ? $t(description) : description"/>
      </div>
      <div class="Home--header--banner">
        <img :src="srcThumbnail" alt="">
      </div>
    </div>

    <div class="Home--content">
      <div class="w-100 d-flex align-items-center flex-wrap justify-content-end Home--content--actions gap-2">
        <slot name="content-actions"/>
        <button v-if="orderApplied" @click="$emit('reset')" type="button" class="RefreshFilters btn btn-primary"><i class="fa fa-refresh"></i></button>
        <button v-if="allowFilters" type="button" class="btn btn-primary" @click="showFilters = !showFilters"><i class="fa fa-filter"></i> {{ $t('FILTERS') }}</button>
      </div>
      <div class="Home--content--filters" v-if="showFilters">
        <div class="w-100">
          <slot name="content-filters" />
        </div>
        <div class="actions d-flex flex-row flex-nowrap align-items-center justify-content-center mt-3">
          <button type="button" class="btn btn-primary" @click="$emit('apply-filters')"><i class="fa fa-check"></i> {{ $t('APPLY_FILTERS') }}</button>
          <button type="button" class="btn btn-danger" @click="$emit('clear-filters')"><i class="fa fa-times"></i> {{ $t('CATALOG.SETTING.CLEAR_FILTERS') }}</button>
        </div>
      </div>
      <div class="Home--content--main">
        <slot name="content-main" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Home",
  props: {
    title: {
      type: String,
      default: 'HOME'
    },
    description: {
      type: String,
      default: 'DESCRIPTION'
    },
    useI18n: {
      type: Boolean,
      default: true
    },
    srcThumbnail: {
      type: String,
      default: '/assets/imgs/library_home.svg'
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    orderApplied: {
      type: Boolean,
      default: false
    },
    allowFilters: {
      type: Boolean,
      default: false
    },
    showFilterMessage: {
      type: Boolean,
      default: false
    },
    numberOfFiltersApplied: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showFilters: false
    };
  }
}
</script>

 <style scoped lang="scss"> 
.Home {
  .dropdown-item {
    cursor: pointer;
  }

  .dropdown-item.delete {
    background-color: $color-error !important;
    color: #FFFFFF;
    &:hover {
      background-color: #f87672;
    }
  }

  &--header {
    padding: 3rem 2rem;
    background: #FFFFFF;
    width: 100%;
    gap: 0;
    display: flex;
    flex-flow: row nowrap;

    @media #{small-screen()} {
      display: grid;
      grid-template-columns: 1fr 150px;
      gap: 0.5rem;
    }

    @media #{min-medium-screen()} {
      display: grid;
      grid-template-columns: 1fr 350px;
      gap: 0.5rem;
    }

    &--content {
      width: 100%;
      h1 {
        font-size: 22px;
        color: #1E293B;
      }

      & > p {
        margin-top: 1rem;
        font-size: 1rem;
        max-width: 630px;
        color: #808080;
      }
    }

    &--banner {
      width: 100%;
      position: relative;
      max-height: 250px;
      display: none;
      @media #{min-small-screen()} {
        display: block;
      }
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  &--content {
    background-color: #F6F7F8;
    padding: 2rem 0;

    &--actions {
      padding: 1rem 5rem;
    }

    &--filters {
      background-color: #D9F0FA;
      padding: 1rem 2rem;

      .actions {
        * {
          margin: 0.15rem;
        }
      }
    }

    &--main {
      padding: 1rem;
      @media #{min-medium-screen()} {
        padding: 1rem 5rem;
      }
    }

    th > a {
      &.active {
        color: $color-primary;
      }
    }
  }
}
</style>
