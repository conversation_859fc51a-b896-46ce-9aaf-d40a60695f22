<template>
  <div class="base-description-action--header">
    <div class="base-description-action--header-content">
      <h1>{{ title }}</h1>
      <p v-html="description" />
    </div>

    <div class="base-description-action--header--banner">
      <img :src="image" alt="" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "",
    },
    
    description: {
      type: String,
      default: "",
    },

    image: {
      type: String,
      default: "/assets/imgs/library_home.svg",
    },
  },
};
</script>

 <style scoped lang="scss"> 
.base-description-action--header {
  padding: 2rem 1rem 3rem 1rem;
  background: #ffffff;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-evenly;

  .base-description-action--header-content {
    width: 46%;

    h1 {
      font-size: 22px;
      color: #1e293b;
    }

    p {
      margin-top: 1rem;
      font-size: 17px;
      color: #808080;
    }
  }

  .base-description-action--header--banner {
    width: 375px;
    position: relative;

    img {
      width: 100%;
      position: absolute;
      top: -2rem;
    }
  }
}
</style>
