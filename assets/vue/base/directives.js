import Vue from "vue";

// Max number
Vue.directive('max-number', {
    bind: function (el, binding, vnode) {
        el.addEventListener('input', function () {
            const maxValue = Number(binding.arg);
            const currentValue = Number(el.value);
            if (currentValue > maxValue) {
                el.value = maxValue;
                vnode.elm?.dispatchEvent(new CustomEvent('input'));
            }
        });
    }
});

// Min number

Vue.directive('min-number', {
    bind: function (el, binding, vnode) {
        el.addEventListener('input', function () {
            const minValue = Number(binding.arg);
            const currentValue = Number(el.value);
            if (currentValue < minValue) {
                el.value = minValue;
                vnode.elm?.dispatchEvent(new CustomEvent('input'));
            }
        });
    }
});
