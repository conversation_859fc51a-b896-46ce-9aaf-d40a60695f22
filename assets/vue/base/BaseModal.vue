<template>
  <div
    class="modal fade"
    :id="identifier"
    tabindex="-1"
    aria-labelledby="baseModal"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div :class="`modal-dialog modal-dialog-centered ${size}`">
      <div class="modal-content">
        <div class="modal-header" v-if="isHeader">
          <h5 class="modal-title" id="staticBackdropLabel">
            <i v-if="modalIcon.length" class="fa mr-2" :class="modalIcon"></i
            >{{ title }}
          </h5>
          <button
            :id="identifier + '_close'"
            type="button"
            class="btn-close btn-close-white"
            @click="$emit('close')"
            data-bs-dismiss="modal"
            aria-label="Close"
            :data-modal-id="'#' + identifier"
          ></button>
        </div>
        <div class="modal-body" :style="{ padding: padding }">
          <slot> </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "base-modal",
  props: {
    identifier: {
      type: String,
      required: true,
    },

    modalIcon: {
      type: String,
      default: "",
    },

    title: {
      type: String,
      default: "Modal title",
    },

    size: {
      type: String,
      default: "modal-lg",
    },

    isHeader: {
      type: Boolean,
      default: true,
    },

    padding: {
      type: String,
      default: "1rem",
    },
  },

  methods: {
    closeModal() {
      $(`#${this.identifier}`).modal("hide");
    },
  },
};
</script>

 <style scoped lang="scss"> 
img {
  width: 100%;
  height: 40rem;
}

.modal-fullscreen {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}
</style>
