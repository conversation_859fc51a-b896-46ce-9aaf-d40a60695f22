<template>
  <div :class="['BaseEmpty', `size--${size}`]">
    <div class="panel">
      <h2 class="title" v-html="title ? title : $t('EMPTY.TITLE')"></h2>

      <div
        class="description"
        v-html="description ? description : $t('EMPTY.DESCRIPTION')"
      ></div>

      <div class="actions">
        <slot name="actions" />
      </div>
    </div>

    <div class="image">
      <img src="/img/empty.svg" alt="empty" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: undefined,
    },
    description: {
      type: String,
      default: undefined,
    },
    size: {
      type: String,
      default: "xl",
      validator: (value) => ["sm", "md", "xl"].includes(value),
    },
  },
};
</script>

 <style scoped lang="scss"> 
.BaseEmpty {
  height: 100%;
  width: 100%;
  display: grid;
  align-content: center;
  padding: var(--padding);
  display: grid;
  justify-content: center;
  grid-template-columns: auto 280px;
  max-width: 800px;
  margin: auto;
  padding-top: 7rem;
  padding-bottom: 5rem;

  &.size {
    &--sm {
      --title-size: var(--font-size-m);
      --description-size: var(--font-size-s);
      --padding: var(--spacing-m);
      --image-size: 200px;
    }

    &--md {
      --title-size: var(--font-size-2xl);
      --description-size: var(--font-size-m);
      --padding: var(--spacing-xl);
      --image-size: 300px;
    }

    &--xl {
      --title-size: var(--font-size-3xl);
      --description-size: var(--font-size-l);
      --padding: var(--spacing-3xl);
      --image-size: 400px;
    }
  }

  .panel {
    position: relative;
    z-index: 2;

    .title {
      font-weight: 600;
      font-size: var(--title-size);
      color: var(--color-neutral-dark);
    }

    .description {
      font-size: var(--description-size);
      color: var(--color-neutral-mid-darker);
    }
  }

  .image {
    position: relative;
    z-index: 1;

    img {
      position: absolute;
      width: var(--image-size);
      translate: -30% -50%;
      transform-origin: 0 0;
    }
  }
}
</style>
