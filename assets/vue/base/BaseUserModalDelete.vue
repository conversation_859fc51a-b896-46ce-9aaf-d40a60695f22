<template>
  <div class="modal fade" id="modal-delete" tabindex="-1" aria-labelledby="modal-delete" data-bs-backdrop="static"
    data-bs-keyboard="false">
    <div :class="`modal-dialog`">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body"> 
          <p
            class="question"
            v-html="
              title === '' ? translationsVue.common_areas_confirm_delete : title
            "
          ></p>
          <p
            v-html="
              message === ''
                ? translationsVue.material_course_configureFields_question_decition
                : message
            "
          ></p>          
        </div>

        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            {{ translationsVue.cancelar }}
          </button>
          <button
            type="button"
            class="btn btn-danger"
            @click="deleteElement()"
            data-bs-dismiss="modal"
          >
            {{ translationsVue.form_label_delete }}
          </button>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
export default {
  name: "base-modal-delete",
  props: {
    title: {
      type: String,
      default: "",
    },

    message: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      translationsVue,
    };
  },

  methods: {
    deleteElement() {
      this.$emit("delete-element");
    },
  },
};
</script>

<style scoped lang="scss">
.base-modal-delete {
  .modal-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 0px;
    padding-top: 0;

    .question {
      font-size: 1rem;
      font-weight: bold;
      margin: 0;
    }
  }

  .modal-footer {
    background: #fff;
  }
}

img {
  width: 100%;
  height: 40rem;
}
</style>
