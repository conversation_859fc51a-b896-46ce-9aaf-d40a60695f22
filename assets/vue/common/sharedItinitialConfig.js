import $ from 'jquery';
import {get} from "vuex-pathify";
import initLocale from "./utils/initLocale";
import {goBack, initRoute} from "./utils/checkStarterRoute";
import {findFroalaKey} from "./utils/initFroalaEditor";
import initUserInfo from "./utils/initUserInfo";

export const computed = {
    getContentTitle: get('contentTitleModule/getContentTitle'),
    useGlobalEventBus: get('contentTitleModule/getUseGlobalEventBus'),
    deleteLastRoute: get('routerModule/getDeleteLastRoute'),
    history: get('routerModule/getHistory'),
    isAdmin: get('userModule/isAdmin'),
    isManager: get('userModule/isManager')
};

export function created(self) {
    self.$store.dispatch('contentTitleModule/useGlobalEventBus', true);
}

export function beforeMount(self) {
    const jwtDom = document.getElementById('jwt');
    if (jwtDom) {
        const jwt = jwtDom.getAttribute('jwt');
        if (jwt) self.$auth.initFromJwt(jwt);
        jwtDom.remove();
    }

    initUserInfo(self.$store, self.$el, self.$i18n);
    initLocale(self.$store, self.$el, self.$i18n);
    initRoute(self.$el, self.$router, self.$store, 'Home');
}

// Call from mounted state in vue app
export function mounted(self) {
    self.$store.dispatch('froalaEditorModule/setFroalaPlaceholderText', self.$i18n.t('FROALA.PLACEHOLDER_TEXT'))
    findFroalaKey(self.$store, $('#froala_key'));

    if (self.useGlobalEventBus) {
        self.$eventBus.$on('go-back', () => {
            goBack(self.$router, self.$store);
        });
    }

    $('body').click(function (event) {
        let timeOutId = null;
        if (event.target.className.includes('ajs-modal')) {
            $('.alertify .ajs-dialog').addClass('static');
            timeOutId = setTimeout(function () {
                $('.alertify .ajs-dialog').removeClass('static');
                if (timeOutId) clearTimeout(timeOutId);
            }, 330);
        }
    });
}

export function beforeDestroy(self) {
    if (self.useGlobalEventBus) {
        self.$eventBus.$off('go-back');
    }
}
