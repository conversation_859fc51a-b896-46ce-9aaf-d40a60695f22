const pdfJsLib = require('pdfjs-dist/build/pdf');
pdfJsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';

export function readAsDataURLAsync(blob) {
    return new Promise((resolve, reject) => {
        let reader = new FileReader();
        reader.onload = e => {
            resolve(reader.result);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
    });
}

export function readAsBinaryStringAsync(blob) {
    return new Promise((resolve, reject) => {
        let reader = new FileReader();
        reader.onload = e => {
            resolve(reader.result);
        };
        reader.onerror = reject;
        reader.readAsBinaryString(blob);
    });
}

export function readFileAsync(file) {
    return new Promise((resolve, reject) => {
        let reader = new FileReader();
        reader.onload = e => {
            resolve(reader.result);
        };
        reader.onerror = reject;
        if (file.type.includes('image')) reader.readAsDataURL(file);
        else reader.readAsBinaryString(file);
    });
}

/**
 * @param canvas
 * @param fileContent
 * @param height
 */
export function renderPdfContent(canvas, fileContent, height) {
    let context = canvas.getContext('2d');
    let loadingTask = pdfJsLib.getDocument({data: fileContent});
    loadingTask.promise.then(function (pdf) {
        pdf.getPage(1).then(function(page) {
            const originalViewportHeight = page.getViewport({scale: 1}).height;
            const scale = height / originalViewportHeight;

            let viewport = page.getViewport({scale: scale});
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            let renderContext = {
                canvasContext: context,
                viewport: viewport
            };

            let renderTask = page.render(renderContext);
            renderTask.promise.then(function () {
                // console.log('rendered')
            })
        });
    });
}

export function renderImageContent(canvas, content) {
    let context = canvas.getContext('2d');
    let image = new Image();
    image.onload = function () {
        canvas.width = image.naturalWidth;
        canvas.height = image.naturalHeight;
        context.drawImage(image, 0, 0);
    }
    image.src = content;
}

/**
 * Make sure to use URL.revokeObjectURL when data.url is not null to clean memory
 * @param canvas
 * @param content
 * @param autoAspectRatio
 * @param client
 * @returns {Promise<unknown>}
 */
export function renderVideoContent(canvas, content, autoAspectRatio = false, client = null) {
    return new Promise((resolve, reject) => {
        if (autoAspectRatio && client == null) reject('When autoAspectRatio is specified, is required to pass the parent element');

        const clientHeight = client?.clientHeight ?? null;

        let context = canvas.getContext('2d');
        const video = document.createElement('video');
        const data = {
            object: null,
            url: URL.createObjectURL(content)
        };

        video.src = data.urlObject;
        video.muted = true;

        video.oncanplay = async function (e) {
            await video.play();
            video.pause();
            requestAnimationFrame(updateCanvas);
        };

        video.addEventListener('loadedmetadata', function () {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            if (autoAspectRatio) self.newWidth = (video.videoWidth / video.videoHeight) * clientHeight;
        });

        data.object = video;

        function updateCanvas() {
            context.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
            window.requestAnimationFrame(updateCanvas);
        }
        resolve(data);
    })
}

export function validEmail(email) {
    return email.match(/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)
}

export function validUrl(url) {
    return url.match(/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)/g);
}
