/**
 * Share common state for froala editor
 */
export default {
    namespaced: true,
    state: {
        froalaKey: '3AC6eF6D4D3D3A3C2C-22VKOG1FGULVKHXDXNDXc2a1Kd1SNdF3H3A8A5D4A3C3E3B2A15==',
        placeholderText: ''
    },
    getters: {
        getFroalaKey(state) { return state.froalaKey; },
        getDefaultConfiguration(state) {
            return {
                key: state.froalaKey,
                filesManagerUploadURL: '/admin/froala/upload_file',
                fileUploadURL: '/admin/froala/upload_file',
                imageUploadURL: '/admin/froala/upload_image',
                imageManagerDeleteURL: '/admin/froala/delete_image',
                imageManagerLoadURL: '/admin/froala/load_images',
                videoUploadURL: '/admin/froala/upload_video',
                attribution: false,
                placeholderText: state.placeholderText
            };
        }
    },
    mutations: {
        SET_FROALA_KEY(state, key) {
            state.froalaKey = key;
        },
        SET_FROALA_PLACEHOLDER_TEXT(state, text) {
            state.placeholderText = text;
        },
    },
    actions: {
        setFroalaKey({ commit }, key) {
            commit('SET_FROALA_KEY', key);
        },
        setFroalaPlaceholderText({ commit }, text) {
            commit('SET_FROALA_PLACEHOLDER_TEXT', text)
        }
    }
}
