# Security in vue app

## How to include the class and enable functionality

To detect the user permissions from the vue application, is required to include
in the app the method `initAuthVerification` from the class `utils/Auth`. The call to these
method is required to allow shorthand access.

```js
import Vue from 'vue';
import initAuthVerification from './utils/auth';
const auth = initAuthVerification();

new Vue({
    // options
    auth
})

```
After the initialization of the Auth class, is `required` to fill the authentication information
of the user.
Currently only from a `jwt` is supported.

## Init user authentication information (Posible ways)
1. Request from api method before initializing the vue application. ex:
    ```js
    import Vue from 'vue';
    import initAuthVerification from './utils/auth';
    const auth = initAuthVerification();
   
   callMethodFromApi().then(r => {
       /** 
        * Get information from a jwt, based on requirements we can
        * declare other methods of role initialization inside the class
       **/
       auth.initFromJwt(r.jwt);
       new Vue({
            // options
            auth
       });
   });
    ```
2. Pass the information using html attribute from the main container. ex:
    ```html
    <div id="app" jwt="jwt-value">
        <div id="option-2-to-pass" jwt="jwt"></div>
    </div>
    ```
   Read the information from the js in beforeMount event in vue
    ```js
   function beforeMount() {
        /** From attribute in main element **/
        const jwt = this.$el.attributes['jwt'].value;
        auth.initFromJwt(jwt);
   
        /** From another html tag **/
        const jwtDom = document.getElementById('jwt');
        if (jwt) {
            const jwt2 = jwtDom.getAttribute('jwt');
            auth.initFromJwt(jwt2);
            jwtDom.remove(); // Remove information from dom
        }
   }
    ```

## Modify Auth class to allow more shorthand methods
Currently, the class when initialized support 3 shorthand calls.
1. $isGranted
2. $getUser
3. $isTutor

Example of using the shorthand calls

```js
// Example 1
function test() {
    if (this.$isGranted('ROLE_ADMINISTRATOR'))
        console.log('do something as administrator');
    console.log('do other things')
}
```

```vue
<div v-if="$isGranted('ROLE_ADMINISTRATOR')">
  <span>Only for admin users</span>
</div>
```

If is required to add other shorthand calls, modify `initAuthVerification()` from the class
`utils/Auth`.

Example:
```js
class Auth {
   shorthandFunctionInClass() {
      /**
       * Do what is required based on current user information
       */
   }
}

export default function initAuthVerification() {
    /**
     * Default methods calls and data
     */
    
    const auth = new Auth();
    Vue.prototype.$auth = auth;
    Vue.prototype.$isGranted = function (attribute) {
        return this.$auth.isGranted(attribute);
    }

    Vue.prototype.$getUser = function () {
        return this.$auth.getUser();
    }

    Vue.prototype.$isTutor = function () {
        return this.$auth.isTutor();
    }

    /**
     * Add other shorthand calls
     */
    Vue.prototype.$shorhandCallName = function () {
        return this.$auth.shorthandFunctionInClass();
    }

    return auth;
}
```
