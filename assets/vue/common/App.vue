<template>
  <div class="App">
    <router-view v-show="!loading"></router-view>
    <div class="w-100" v-show="loading">
      <div class="d-flex flex-column align-items-center justify-content-center">
        <spinner />
        <span>{{ message }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import {get} from "vuex-pathify";
import Spinner from "../admin/components/base/Spinner.vue";

export default {
  name: "App",
  components: {Spinner},
  computed: {
    loading: get('loaderModule/loading'),
    message: get('loaderModule/message')
  },
}
</script>

 <style scoped lang="scss"> 
.App {
  width: 100%;
  overflow: auto;
}
</style>
