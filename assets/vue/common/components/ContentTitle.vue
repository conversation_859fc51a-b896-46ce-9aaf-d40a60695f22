<template>
  <div class="ContentTitle">
    <button v-if="this.params.length - 1 > 0"
            class="back-button" type="button"
            @click="goBack()"
    ><i class="fa fa-arrow-left"></i></button>
    <div v-for="(param, index) in params">
      <a v-if="param?.params?.type === 'link'" :href="param.params?.linkValue">{{ param.params.linkName }}</a>
      <router-link v-else-if="param.params" :to="{ name: param.routeName, params: param.params.params }">
        {{ param.params.linkName }}
      </router-link>
      <span>{{ param.params ? checkCurrentRoute(index) : param }}</span>
    </div>
  </div>
</template>

<script>
export const TYPE_DEFAULT = 'default';
export const TYPE_LINK = 'link';// If is link, ignore vue router
export default {
  name: "ContentTitle",
  computed: {
    params() {
      return this.$store.getters['contentTitleModule/getContentTitle'];
    },
    history() {
      return this.$store.getters['routerModule/getHistory'];
    },
    useGlobalEventBus() {
      return this.$store.getters['contentTitleModule/getUseGlobalEventBus'];
    }
  },
  methods: {
    checkCurrentRoute(index) {
      const size = this.params.length;
      return index === (size - 1) ? '' : '/';
    },
    goBack() {
      this.$store.dispatch('routerModule/setDeleteLastRoute', true);
      if (this.useGlobalEventBus) {
        this.$eventBus.$emit('go-back');
      } else {
        this.$emit('go-back');
      }
    }
  }
}
</script>

 <style scoped lang="scss"> 
.ContentTitle {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  justify-content: flex-start;
  .back-button {
    margin-right: 0.5rem;
    border: none;
    background-color: transparent;
    color: #9e9e9e;
    &:hover {
      border: none;
      color: #c1c1c1;
    }
  }
  span {
    margin-left: 0.15rem;
    margin-right: 0.15rem;
  }
}
</style>
