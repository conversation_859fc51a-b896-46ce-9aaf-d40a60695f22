<template>
  <div v-if="loading" class="d-flex align-items-center justify-content-center">
    <spinner />
  </div>
  <div class="NewObservation" v-else>
    <step-form
      :id="formId"
      :current-step="steps.current"
      :number-of-steps="steps.max"
      @on-step-change="onCurrentStepChange"
      @next="steps.current += 1"
      @prev="steps.current -= 1"
      @submit="submit()"
      header-prefix="ANNOUNCEMENT_OBSERVATION"
    >
      <template v-slot:form-content>
        <div v-show="steps.current === 1">
          <observation-step1 :observation="observation" />
        </div>
        <div v-show="steps.current === 2">
          <observation-step2 :observation="observation" />
        </div>
        <div v-show="steps.current === 3">
          <observation-step3 :observation="observation" />
        </div>
      </template>
    </step-form>
  </div>
</template>

<script>
import FormProgress from "../../../common/components/FormProgress.vue";
import ObservationStep1 from "./ObservationStep1.vue";
import ObservationStep2 from "./ObservationStep2.vue";
import ObservationStep3 from "./ObservationStep3.vue";
import Spinner from "../../../admin/components/base/Spinner.vue";
import StepForm from "../../../common/views/StepForm.vue";

export default {
  name: "NewObservation",
  components: {
    FormProgress,
    ObservationStep1,
    ObservationStep2,
    ObservationStep3,
    Spinner,
    StepForm,
  },
  data() {
    return {
      steps: {
        current: 1,
        max: 3,
      },
      formId: "form-new-observation",

      loading: false,
      observation: null,
    };
  },
  computed: {
    useGlobalEventBus() {
      return this.$store.getters["contentTitleModule/getUseGlobalEventBus"];
    },
  },
  created() {
    if (this.$route.name === "UpdateObservation") {
      this.loadObservation();
    }
  },
  methods: {
    async loadObservation() {
      this.loading = true;
      try {
        const { data, error } = await this.$store.dispatch(
          "announcementObservationModule/getObservation",
          this.$route.params.id
        );
        this.observation = data;
      } finally {
        this.loading = false;
      }
    },
    onCurrentStepChange(step) {
      this.steps.current = step;
    },
    submit() {
      const form = document.forms[this.formId];
      const formData = new FormData(form);
      const update = this.$route.name === "UpdateObservation";
      this.$alertify.confirmWithTitle(
        this.$t(
          `ANNOUNCEMENT_OBSERVATION.${
            update ? "UPDATE" : "CREATE"
          }.CONFIRM.TITLE`
        ),
        this.$t(
          `ANNOUNCEMENT_OBSERVATION.${
            update ? "UPDATE" : "CREATE"
          }.CONFIRM.DESCRIPTION`
        ),
        () => {
          this.saveObservation(formData, update);
        },
        () => {}
      );
    },
    saveObservation(formData, update = false) {
      const self = this;
      function save() {
        if (update)
          return self.$store.dispatch(
            "announcementObservationModule/updateObservation",
            { id: self.$route.params.id, formData }
          ); // In this method ID is the observation id
        else
          return self.$store.dispatch(
            "announcementObservationModule/addNewObservation",
            { id: self.$route.params.id, formData }
          ); // In this method ID is the announcement
      }

      this.$store.dispatch('loaderModule/setLoading', { loading: true, message: this.$t('SAVING')});
      save().then((res) => {
        // Use error to show what failed
        const { data, error } = res;
        if (error) {
          this.$toast.error(
            this.$t(`ANNOUNCEMENT_OBSERVATION.SAVE.FAILED`) + ""
          );
        } else {
          this.$toast.success(
            this.$t(`ANNOUNCEMENT_OBSERVATION.SAVE.SUCCESS`) + ""
          );
          this.$store.dispatch("routerModule/setDeleteLastRoute", true);
          this.$router.replace({
            name: "ViewObservation",
            params: { id: data },
          });
        }
      }).finally(() => {
        this.$store.dispatch('loaderModule/setLoading', { loading: false});
      });
    },
  },
};
</script>

 <style scoped lang="scss"> 
</style>
