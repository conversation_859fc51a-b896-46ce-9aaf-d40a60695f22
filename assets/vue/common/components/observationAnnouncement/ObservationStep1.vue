<template>
  <div class="ObservationStep1 d-flex flex-row">
    <div class="col-xs-12 col-6">
      <button-with-description
        title="ANNOUNCEMENT_OBSERVATION.COMUNICADO_FUNDAE"
        description="ANNOUNCEMENT.COMUN_FUNDAE.DESCRIPTION"
        v-model="comunicadoFundae"
        name="comunicadoFundae"
      />
      <!---   <button-with-description
          title="ANNOUNCEMENT_OBSERVATION.COMUNICADO_ABILITIA"
          description="ANNOUNCEMENT.COMUN_ABILITIA.DESCRIPTION"
          v-model="comunicadoAbilitia"
          name="comunicadoAbilitia"
      /> -->
    </div>
    <div class="col-xs-12 col-6">
      <div class="w-100 form-group p-0 m-0">
        <label for="courseStatus">{{
          $t("ANNOUNCEMENT_OBSERVATION.COURSE_STATUS")
        }}</label>
        <input
          type="text"
          name="courseStatus"
          id="courseStatus"
          class="form-control"
          v-model="courseStatus"
          :placeholder="$t('INPUT.PLACEHOLDER.COURSE_STATUS')"
        />
      </div>
      <div class="w-100 form-group p-0 m-0">
        <label for="economicModule">{{
          $t("ANNOUNCEMENT_OBSERVATION.ECONOMIC_MODULE")
        }}</label>
        <input
          type="text"
          name="economicModule"
          id="economicModule"
          class="form-control"
          v-model="economicModule"
          :placeholder="$t('INPUT.PLACEHOLDER.TRAVEL_AND_MAINTENANCE')"
        />
      </div>
      <div class="w-100 form-group p-0 m-0">
        <label for="travelAndMaintenance">{{
          $t("ANNOUNCEMENT_OBSERVATION.TRAVEL_AND_MAINTENANCE")
        }}</label>
        <input
          type="text"
          name="travelAndMaintenance"
          id="travelAndMaintenance"
          class="form-control"
          v-model="travelAndMaintenance"
          :placeholder="$t('INPUT.PLACEHOLDER.TRAVEL_AND_MAINTENANCE')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import ButtonWithDescription from "../../../common/components/ButtonWithDescription.vue";

export default {
  name: "ObservationStep1",
  components: { ButtonWithDescription },
  props: {
    observation: null,
  },
  data() {
    return {
      comunicadoFundae: this.observation?.comunicadoFundae ?? false,
      comunicadoAbilitia: this.observation?.comunicadoAbilitia ?? false,

      courseStatus: this.observation?.courseStatus ?? "",
      economicModule: this.observation?.economicModule ?? "",
      travelAndMaintenance: this.observation?.travelAndMaintenance ?? "",
    };
  },
  watch: {
    observation(newValue) {
      this.comunicadoFundae = newValue.comunicadoFundae ?? false;
      this.comunicadoAbilitia = newValue.comunicadoAbilitia ?? false;
      this.courseStatus = newValue.courseStatus ?? "";
      this.economicModule = newValue.economicModule ?? "";
      this.travelAndMaintenance = newValue.travelAndMaintenance ?? "";
    },
  },
};
</script>

 <style scoped lang="scss"> 
</style>
