<template>
  <div class="AddRemove" :class="enableAll ? 'all' : ''">
    <h1 class="AddRemove--title" v-if="title">{{ title }}</h1>
    <div class="AddRemove--available">
      <h4 class="title">{{ $t('CATEGORY_FILTER.AVAILABLE') }}</h4>
      <div class="header btn-right">
        <input
            type="text"
            class="form-control"
            v-model="srcQuery"
            :placeholder="$t(placeholder.source)"
        />
        <button type="button"
                @click="addAll()"
                class="btn btn-primary">{{ $t('ADD_ALL') }} <i class="fa fa-angle-double-right"></i></button>
      </div>
      <div v-if="loadingSource" class="d-flex align-items-center justify-content-center">
        <spinner />
      </div>
      <div class="content" v-else>
        <div class="item card btn-right"
             v-for="(item, index) in availableFiltered"
             :key="(idField ? item['' + idField] : index) + '-source-' + name"
        >
          <span class="info">
            {{ nameField ? item['' + nameField] : item }}
          </span>
            <button type="button" @click="add(item)">
              <i class="fa fa-chevron-right"></i>
            </button>
        </div>
      </div>
    </div>
    <div class="AddRemove--selected">
      <h4 class="title">{{ $t('CATEGORY_FILTER.SELECTED') }}</h4>
      <div class="header btn-left">
        <button type="button"
                class="btn btn-primary"
                @click="removeAll()">
          <i class="fa fa-angle-double-left"></i> {{ $t('REMOVE_ALL') }}
        </button>
        <input
            type="text"
            class="form-control"
            v-model="selectedQuery"
            :placeholder="$t(placeholder.destination)"
        />
      </div>
      <div v-if="loadingSelected" class="d-flex align-items-center justify-content-center">
        <spinner />
      </div>
      <div class="content" v-else>
        <div class="item card btn-left"
             v-for="(item, index) in selectedFiltered"
             :key="(idField ? item['' + idField] : index) + '-selected-' + name"
        >
          <button type="button" @click="remove(item)">
            <i class="fa fa-chevron-left"></i>
          </button>
          <span class="info">
            {{ nameField ? item['' + nameField] : item }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import Spinner from "../../../admin/components/base/Spinner.vue";

export default {
  name: "AddRemove",
  components: {Spinner},
  props: {
    title: {
      type: String,
      default: null
    },
    name: {
      type: String,
      default: 'add-remove'
    },
    /**
     * @param sourceItems Base available elements
     */
    sourceItems: {
      type: Object|Array,
      default() {
        return []
      }
    },
    /**
     * @param value -> v-model
     */
    value: {
      type: Object|Array,
      default: function () {
        return [];
      }
    },

    loadingSource: {
      type: Boolean,
      default: false,
    },

    loadingSelected: {
      type: Boolean,
      default: false,
    },

    realtime: {
      type: Boolean,
      default: true,
    },

    rest: {
      type: Boolean,
      default: true,
    },

    enableAll: {
      type: Boolean,
      default: true,
    },

    placeholder: {
      type: Object,
      default: () => ({
        source: 'ADD_REMOVE.PLACEHOLDER.FIND_SOURCE',
        destination: 'ADD_REMOVE.PLACEHOLDER.FIND_SELECTED'
      })
    },

    urls: {
      type: Array|Object,
      default: () => ({
        add: "",
        remove: "",
        addAll: "",
        removeAll: ""
      })
    },

    fields: {
      type: Array|Object,
      default: () => ({
        id: "id",
        name: "name"
      })
    },
    i18n: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      srcQuery: '',
      selectedQuery: '',
    };
  },
  computed: {
    innerValue: {
      get() {
        return this.value ?? [];
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    },

    available() {
      if (this.innerValue.length > 0) {
        return this.sourceItems.filter(item => {
          const index = this.innerValue.findIndex(selected => {
            if ('id' in this.fields) {
              const id = this.fields.id;
              return item['' + id] === selected['' + id];
            }
            return selected === item;
          })
          return index < 0;
        })
      }
      return this.sourceItems;
    },

    availableFiltered() {
      const available = this.available;
      if (this.srcQuery.length > 0) {
        const query = this.normalize(this.srcQuery);
        return available.filter(item => {
          if ('name' in this.fields) {
            const name = this.fields.name;
            return this.normalize(item['' + name]).includes(query);
          }
          return this.normalize(item).includes(query);
        })
      }
      return available;
    },

    selectedFiltered() {
      if (this.selectedQuery.length > 0) {
        const query = this.normalize(this.selectedQuery);
        return this.innerValue.filter(item => {
          if ('name' in this.fields) {
            const name = this.fields.name;
            return this.normalize(item['' + name]).includes(query);
          }
          return this.normalize(item).includes(query);
        })
      }
      return this.innerValue;
    },

    idField() {
      if ('id' in this.fields) return this.fields.id;
      return null;
    },

    nameField() {
      if ('name' in this.fields) return this.fields.name;
      return null;
    },

    urlAdd() {
      if ('add' in this.urls) return this.urls.add;
      return null;
    },

    urlRemove() {
      if ('remove' in this.urls) return this.urls.remove;
      return null;
    },

    urlAddAll() {
      if ('addAll' in this.urls) return this.urls.addAll;
      return null;
    },

    urlRemoveAll() {
      if ('removeAll' in this.urls) return this.urls.removeAll;
      return null;
    },
  },
  methods: {
    async add(item) {
      let success = !this.realtime;
      if (this.realtime) {
        const url = this.urlAdd;
        if (url == null || url.length < 1) {
          return;
        }
        const self = this;
        function execute() {
          if (self.rest) return axios.post(url + '/' + (self.idField ? item['' + self.idField] : item));
          else return axios.post(url, item);
        }
        const result = await execute();
        const { data, error } = result.data;
        if (error) {
          this.$toast.error((this.i18n ? this.$t(data) : data) + '')
        } else {
          success = true;
          this.$toast.success((this.i18n ? this.$t(data) : data) + '')
        }
      }

      if (success) {
        this.addElement(item);

        // this.$emit('input', values);
      }
    },
    addAll() {
      const items = structuredClone(this.availableFiltered);
      if (this.realtime) {
        // Send to backend
      }
      items.forEach(item => {
        this.addElement(item);
      })
    },
    addElement(item) {
      const index = this.innerValue.findIndex(selected => selected.id === item.id);
      if (index < 0) this.innerValue.push(item);
    },
    async remove(item) {
      let success = !this.realtime;
      if (this.realtime) {
        const url = this.urlRemove;
        if (url == null || url.length < 1) {
          return;
        }
        const self = this;
        function execute() {
          if (self.rest) return axios.delete(url + '/' + (self.idField ? item['' + self.idField] : item));
          else return axios.post(url, item);
        }
        const result = await execute();
        const { data, error } = result.data;
        if (error) {
          this.$toast.error((this.i18n ? this.$t(data) : data) + '')
        } else {
          success = true;
          this.$toast.success((this.i18n ? this.$t(data) : data) + '')
        }
      }
      if (success) this.removeFromSelected(item);
    },
    removeAll() {
      const items = structuredClone(this.selectedFiltered);
      if (this.realtime) {
        // send to backend
      }
      items.forEach(item => {
        this.removeFromSelected(item);
      })
    },

    removeFromSelected(item) {
      const index = this.innerValue.findIndex(selected => {
        if ('id' in this.fields) {
          return item['' + this.fields.id] === selected['' + this.fields.id];
        }
        return selected === item;
      });
      if (index >= 0) this.innerValue.splice(index, 1);
    },

    normalize(string) {
      return string
          .toLowerCase()
          .normalize("NFD")
          .replace(/[\u0300-\u036f]/g, "");
    },
  }
}
</script>

 <style scoped lang="scss"> 
.AddRemove {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 1rem;

  &--title {
    grid-column-start: 1;
    grid-column-end: 3;
    font-size: 18px;
    width: 100%;
    text-align: center;
    padding: 0.5rem;
  }

  //&.all {
  //  //grid-template-columns: 45% 1fr 45%;
  //  //.AddRemove--title {
  //  //  grid-column-start: 1;
  //  //  grid-column-end: 4;
  //  //}
  //}

  .AddRemove--available {
    .item {
      padding: .5rem .2rem .5rem 1rem;
    }
  }

  .AddRemove--selected {
    .item {
      padding: .5rem .2rem .5rem .2rem;
    }
  }

  .AddRemove--available, .AddRemove--selected {
    display: flex;
    flex-flow: column;
    padding: 1rem .5rem .5rem .5rem;
    background: var(--color-neutral-lighter);
    border: 1px solid var(--color-neutral-mid-light);
    border-radius: 5px;

    .title {
      color: var(--color-neutral-darkest);
      width: 100%;
      font-size: 20px;
      text-align: center;
    }

    .header {
      width: 100%;
      padding: 12px 0;
      display: grid;
      gap: .5rem;
      margin-top: 1rem;

      &.btn-right {
        grid-template-columns: 1fr 150px;
      }
      &.btn-left {
        grid-template-columns: 150px 1fr;
      }
    }

    .content {
      height: 250px;
      overflow-y: auto;
      display: flex;
      flex-flow: column;
      width: 100%;
      gap: .5rem;
      padding: 5px;
      background-color: #FFFFFF;

      .item {
        //display: flex;
        flex-flow: row nowrap;
        align-items: center;
        display: grid;
        gap: .5rem;
        background-color: #D9F0FA;
        border-radius: 5px;
        color: var(--color-neutral-darkest);


        &.btn-right {
          grid-template-columns: 1fr 30px;
        }

        &.btn-left {
          grid-template-columns: 30px 1fr;
        }

        & > button {
          border: none;
          background: transparent;
          color: var(--color-primary) !important;
        }
      }
    }
  }

  &--actions {
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;

    > * {
      margin: 0.1rem
    }
  }
}
</style>
