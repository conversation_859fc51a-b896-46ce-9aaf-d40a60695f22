<template>
  <div class="FilePreview" :id="`${id}-file-preview`"
  >
    <div :id="`${id}-file-preview-content`" class="FilePreview--content" :class="previewClass"
         :style="style">
      <canvas v-if="!isImage" :id="canvasId"></canvas>
      <p>
        <button class="type-icon">
          <i v-if="isFontAwesomeIcon" :class="fontAwesomeIcon"></i>
        </button>
      </p>
    </div>
    <div class="FilePreview--footer" v-if="footer">
      <span>Aqui va el nombre</span>
      <span class="date">2021-01-01</span>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import {readAsBinaryStringAsync, renderPdfContent} from "../utils/utils";

export default {
  name: "FilePreview",
  props: {
    id: {
      type: Number|String,
      required: true
    },
    file: {
      type: File|Object|Array,
      required: true
    },
    basePath: {
      type: String,
      default: ''
    },
    footer: {
      type: Boolean,
      default: true
    },
    applyViewport: {
      type: Boolean,
      default: true
    },

    width: {
      type: String,
      default: '200px'
    },

    height: {
      type: String,
      default: '200px'
    },
    customStyle: {
      type: Object|Array,
      default: function () { return {}; }
    }
  },
  data() {
    return {
      isImage: false,
      imageHeight: 0,
      previewClass: '',
      isFontAwesomeIcon: false,
      fontAwesomeIcon: '',
      style: {
        width: '200px',
        height: '200px'
      }
    };
  },
  computed: {
    canvasId() {
      return `${this.id}-file-preview-canvas`;
    },
    // Validate whether file is if type File, otherwise it can be handled as array/object
    isFile() {
      return this.file instanceof File;
    },
    extension() {
      const separated = this.file.filename.split('.');
      return separated.length > 1 ? separated[separated.length - 1] : 'NOT';
    }
  },
  mounted() {
    let defaultStyle = {
      ...this.style,
      ...this.customStyle
    };
    let style = {};
    const mimeType = this.file.mimeType;
    if (mimeType.includes('image')) {
      this.previewClass = 'is-image';
      style = {
        'background-image' : `url(${this.basePath}/${this.file.filename})`
      };
      this.isFontAwesomeIcon = true;
      this.fontAwesomeIcon = 'fa fa-camera';
    } else if (mimeType.includes('pdf')) {
      style = {
        'background-image': 'unset'
      }
      this.isFontAwesomeIcon = true;
      this.fontAwesomeIcon = 'fa fa fa-file-pdf';
      this.previewClass = 'pdf';
      this.handlePdfFile();
    }
    this.style = {...defaultStyle, ...style};
  },
  methods: {
    async handlePdfFile() {
      const url = `${this.basePath}/${this.file.filename}`;
      const elementHeight = document.getElementById(this.canvasId).clientHeight;
      const { data } = await axios.get(url, {
        responseType: 'blob'
      });
      const result = await readAsBinaryStringAsync(data);
      renderPdfContent(document.getElementById(this.canvasId), result, parseInt(this.style.height.replace('px', '')) - 20);
    },
    // async getFileRemoteContent() {
    //   const url = `${this.basePath}/${this.file.filename}`;
    //   const { data } = await axios.get(url, {
    //     responseType: 'blob'
    //   });
    //   if (this.file.mimeType && this.file.mimeType.includes('pdf')) {
    //     readAsBinaryStringAsync(data).then(r => {
    //       renderPdfContent(
    //           document.getElementById(this.id + '-file-preview-content'),
    //           document.getElementById(this.canvasId),
    //           r,
    //           this.applyViewport
    //       )
    //     });
    //   }
    // }
  }
}
</script>

 <style scoped lang="scss"> 
.FilePreview {
  position: relative;
  width: 100%;
  padding: 0.15rem;
  border: 1px solid $base-border-color;

  &--content {
    position: relative;
    &.is-image {
      width: 100%;
      background-size: cover;
      background-position: center center;
    }

    &>p {
      position: absolute;
      top: 0;
      font-size: 17px;
      color: #FFFFFF;
      width: 100%;
      height: 100%;
      overflow: hidden;
      text-shadow: 1px 1px 3px #000000;
      background-color: rgba(#000000, 0.3);
      display: flex;
      margin: 0;

      .type-icon {
        cursor: pointer;
        background: transparent;
        color: #ffffff;
        width: 70px;
        height: 70px;
        margin: auto;
        text-align: center;
        border: none;
        transition: all .2s ease-in;
        font-size: 45px;
        &:hover {
          border: none;
          transform: scale(1.1);
        }
      }
    }
  }

  &--footer {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: flex-start;
    color: #212121;

    .date {
      margin-left: auto;
    }
  }

  canvas {
    width: 100%;
    height: 100%;
  }
}
</style>
