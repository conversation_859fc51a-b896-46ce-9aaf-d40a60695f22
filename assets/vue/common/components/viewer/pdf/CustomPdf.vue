<template>
  <div class="CustomPdf">
    <div class="CustomPdf--header">
      <div>
        <button><i class="fa fa-plus"></i></button>
        <span>100%</span>
        <button><i class="fa fa-minus"></i></button>
      </div>
    </div>
    <div v-if="loading" class="d-flex align-items-center justify-content-center">
      <spinner />
    </div>
    <div class="CustomPdf--body" v-show="!loading">
      <button type="button" @click="prev()">
        <i class="fa fa-chevron-left"></i>
      </button>
      <div class="CustomPdf--body--pdf">
        <vue-pdf
            :src="src"
            :page="currentPage"
            @loaded="loading = false"
            @num-pages="numPages = $event"
            @page-loaded="currentPage = $event"
        ></vue-pdf>
      </div>
      <button type="button" @click="next()">
        <i class="fa fa-chevron-right"></i>
      </button>
    </div>
    <div class="CustomPdf--footer">
      <div>
        <button @click="prev()"><i class="fa fa-chevron-left"></i></button>
        <span><span class="current">{{ currentPage }}</span> / {{ numPages }}</span>
        <button @click="next()"><i class="fa fa-chevron-right"></i></button>
      </div>
    </div>
  </div>
</template>

<script>
import VuePdf from 'vue-pdf';
import Spinner from "../../../../admin/components/base/Spinner.vue";
export default {
  name: "CustomPdf",
  components: {Spinner, VuePdf},
  props: {
    src: {
      type: String,
      required: true
    },
    page: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      currentPage: 1,
      numPages: 1,
      loading: true
    };
  },
  watch: {
  },
  methods: {
    next() {
      this.currentPage++;
      this.$emit('current-page', this.currentPage);
    },
    prev() {
      this.currentPage--;
      this.$emit('current-page', this.currentPage);
    }
  }
}
</script>

 <style scoped lang="scss"> 
.CustomPdf {
  width: 100%;

  &--header {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: center;
    background-color: #019bde;
    padding: 0.25rem 0;

    &>div {
      display: grid;
      grid-template-columns: 30px auto 30px;
      gap: 1rem;
      align-items: center;

      button {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: none;
        background-color: #FFFFFF;
        color: #019bde;
        &:hover {
          border: none;
        }
      }

      span {
        text-align: center;
        color: #ffffff;
      }
    }
  }

  &--body {
    width: 100%;
    @media #{min-medium-screen()} {
      display: grid;
      grid-template-columns: [left] auto [pdf] 700px [right] auto;
      align-items: center;
    }

    button {
      display: none;
      @media #{min-medium-screen()} {
        display: block;
      }
      border: none;
      background-color: transparent;
      color: #FFFFFF;
      font-size: 100px;
      text-align: center;
      transition: ease-in all 0.5s;
      &:hover {
        border: none;
        transform: scale(1.1);
      }
    }

    &--pdf {
      width: 100%;

      @media #{min-medium-screen()} {
        width: 700px;
      }
    }
  }

  &--footer {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: center;
    background-color: #2c2c2c;
    padding: 0.4rem 0;

    &>div {
      display: grid;
      grid-template-columns: 25px auto 25px;
      gap: 1rem;
      align-items: center;

      button {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        border: none;
        background-color: #019bde;
        color: #FFFFFF;
        &:hover {
          border: none;
        }
      }

      span {
        text-align: center;
        color: #d0cdcd;
        &.current {
          color: #FFFFFF;
        }
      }
    }
  }
}
</style>
