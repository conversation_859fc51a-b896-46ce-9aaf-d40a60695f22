<template>
  <div class="VideoViewer">
    <vimeo-custom-viewer :url="url" :width="width" :height="height" v-if="custom"/>
    <vimeo-viewer :url="url" :width="width" :height="height" v-else/>
  </div>
</template>

<script>
import VimeoCustomViewer from "./video/VimeoCustomViewer.vue";
import VimeoViewer from "./video/VimeoViewer.vue";

export default {
  name: "VideoViewer",
  components: {VimeoCustomViewer, VimeoViewer},
  props: {
    url: {
      type: String,
      default: ''
    },

    /**
     * @param source vimeo|youtube
     */
    source: {
      type: String,
      default: 'vimeo'
    },

    custom: {
      type: Boolean,
      default: false
    },

    playerHeightName: {
      type: String,
      default: '720p'
    }
  },
  computed: {
    width() {
      switch (this.playerHeightName) {
        case '240px': return 426;
        case '360p': return 640;
        case '480p': return 854;
        case '720p': return 1280;
        case '1080p': return 1920;
      }
    },
    height() {
      switch (this.playerHeightName) {
        case '240px': return 240;
        case '360p': return 360;
        case '480p': return 480;
        case '720p': return 720;
        case '1080p': return 1080;
      }
    }
  }
}
</script>

 <style scoped lang="scss"> 

</style>
