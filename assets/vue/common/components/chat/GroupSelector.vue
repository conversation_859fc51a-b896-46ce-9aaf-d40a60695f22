<script>
export default {
  name: "GroupSelector",
  props: {
    groups: {
      type: Array,
      default: []
    },
    selectedGroupId: {
      type: Number,
      default: null
    },
  },
  data() {
    return {
      currentIndex: 0
    }
  },
  computed: {
    showCarets() {
      return this.groups.length > 3
    },
    rangeList() {
      return [...this.groups].slice(this.currentIndex, this.currentIndex + 3)
    }
  },
  methods: {
    setPage(value = 0) {
      this.currentIndex = value
    },
    nextPage() {
      this.setPage(Math.min(this.groups.length - 3, this.currentIndex + 1))
    },
    prevPage() {
      this.setPage(Math.max(0, this.currentIndex - 1))
    },
  }
}
</script>

<template>
  <div class="GroupSelector">
    <button
      v-if="showCarets"
      class="btn btn-sm btn-default"
      @click="prevPage">
      <i class="fa fa-caret-left"/>
    </button>
    <button type="button" class="btn-selector"
            v-for="(g, i) in rangeList"
            :key="'group_' + g.id + '_i_' + i"
            :class="g.id === selectedGroupId ? 'primary' : 'default'"
            @click="$emit('selectGroup', g.id)">{{ g.name }}</button>
    <button
      v-if="showCarets"
      class="btn btn-sm btn-default"
      @click="nextPage">
      <i class="fa fa-caret-right"/>
    </button>
  </div>
</template>

<style scoped lang="scss">
.GroupSelector {
  width: 100%;
  padding: 1rem 0 0.8rem;
  background-color: white;
  display: flex;
  gap: 0.5rem;

  button {
    color: var(--color-neutral-lightest);
    border: none;
    border-radius: 3px;
    font-size: 0.85rem;
    padding: 0.5rem;
    
    &.btn-selector {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow-x: clip;
      width: 5.5rem;
      
      &.default {
        background-color: var(--color-neutral-mid-dark);
      }
      
      &.primary {
        background-color: var(--color-primary);
      }
    }
  }
}
</style>