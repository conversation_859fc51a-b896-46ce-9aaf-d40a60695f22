<template>
  <div class="ResultRoulette">
    <h4 class="mb-1">{{ $t('RESULTS') }} ({{ correctAnswers }}/{{ questions.length }})</h4>
    <div class="questions">
      <div class="question" v-for="(item, index) in questions" :key="index">

        <div class="letter state--default">{{ item.question.letter }}</div>

        <div class="roulette-options">
          <h5 v-if="item.question.type">
            {{ $t('ROULETTE.BEGINS_WITH') }}:
          </h5>

          <h5 v-else>
            {{ $t('ROULETTE.CONTAINS') }}:
          </h5>
        </div>

        <h5> {{ $t('ROULETTE.HINT') }}: {{item.question.question}} </h5>

        <div class="answers">
          <div class="answer"
               :class="'selected'"
          >
            <div class="answer-option correct">
              {{item.question.word}}
            </div>

            -->

            <div class="answer-option"
                 :class="item.correct ? 'correct' : 'wrong'">
              {{item.value}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ResultRoulette",
  props: {
    questions: {
      type: Array|Object,
      default: function () {
        return [];
      }
    }
  },
  computed: {
    correctAnswers: function () {
      let correct = 0;
      for (let i = 0; i < this.questions.length; i++) {
        if (this.questions[i].correct) {
          correct++;
        }
      }

      return correct;
    },
  }
}
</script>

 <style scoped lang="scss"> 

</style>
