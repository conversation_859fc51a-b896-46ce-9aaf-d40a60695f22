.VueApp {
  width: 100%;
  overflow: auto;
}

.tooltip-inner {
  color: #000 !important;
  background-color: var(--color-primary-lighter) !important;
}

.bs-tooltip-right .arrow::before, .bs-tooltip-auto[x-placement^=right] .arrow::before {
  border-right-color: var(--color-primary-lighter) !important;
}

.bs-tooltip-left .arrow::before, .bs-tooltip-auto[x-placement^=left] .arrow::before {
  border-left-color: var(--color-primary-lighter) !important;
}

.bs-tooltip-top .arrow::before, .bs-tooltip-auto[x-placement^=top] .arrow::before {
  border-top-color: var(--color-primary-lighter) !important;
}

.bs-tooltip-bottom .arrow::before, .bs-tooltip-auto[x-placement^=bottom] .arrow::before {
  border-bottom-color: var(--color-primary-lighter) !important;
}