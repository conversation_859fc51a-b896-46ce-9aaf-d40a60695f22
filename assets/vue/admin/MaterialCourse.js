import Vue from 'vue';
import listMaterialCourse from './components/material-course/listMaterialCourse.vue';
import vueVimeoPlayer from 'vue-vimeo-player';
import store from './store';
import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';
import VueAlertify from "vue-alertify";
import { getI18nApi } from "../common/i18n";

Vue.use(vueVimeoPlayer);


Vue.use(VueToast, {
  duration: 5000,
  position: "top-right",
});




function startVueApp({ i18n }) {
  const alertifyOk = i18n.t("ALERTIFY.OK");
  const alertifyCancel = i18n.t("ALERTIFY.CANCEL");
  Vue.use(VueAlertify, {
    closable: false,
    movable: false,
    glossary: {
      title: "AlertifyJS",
      ok: alertifyOk,
      cancel: alertifyCancel,
    },
  });
  window.Vue = new Vue({
      components: { listMaterialCourse },
      store,
      i18n
  }).$mount('#materialCourse');
}

getI18nApi().then((data) => {
  startVueApp(data);
});
