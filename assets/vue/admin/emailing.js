import Vue from 'vue';
import store from './store';
import Multiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.min.css';
import axios from "axios";

Vue.component('multiselect', Multiselect)
new Vue({
    delimiters: ['${', '}'],
    components: {Multiselect},
    store,
    data() {
        return {
            professionalCategories: [],
            departments: [],
            centers: [],
            countries: [],

            recipients: 'test',
            testEmails: '',
            sendAt: '',
            filters: {
                categories: [],
                departments: [],
                centers: [],
                countries: [],

            },
        }
    },
    async created() {
        this.professionalCategories = typeof professionalCategories !== 'undefined' ? professionalCategories : [];
        this.departments = typeof departments !== 'undefined' ? departments : [];
        this.centers = typeof centers !== 'undefined' ? centers : [];
        this.countries = typeof countries !== 'undefined' ? countries : [];
        this.email = typeof email !== 'undefined' ? email : null;
    },

    methods: {
        async send() {
            let payload = {
                email: this.email,
                sendAt: this.sendAt,
                recipients: this.recipients,
                testEmails: this.testEmails,
                filters: this.filters,
            }

            await axios.post('/admin/emailing/send', payload)
                .then(response => {
                    if(this.recipients == 'test'){
                       alert(response.data.data);
                    }
                    else if(this.recipients == 'users'){
                      window.location = indexUrl;
                    }
                })
        }
    },
    watch: {},

    computed: {}
}).$mount('#email-send')
