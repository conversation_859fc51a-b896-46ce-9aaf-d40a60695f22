import Vue from 'vue';
import VueToast from 'vue-toast-notification'
import CourseStatsUsers from './CourseStatsUsers.vue'
import CourseStatsDetails from './CourseStatsDetails.vue'
import CourseStatsOpinions from './CourseStatsOpinions.vue'
import { getI18nApi } from '../../common/i18n'
import '../../../css/courseStats.scss'
import '../../../css/vueMultiSelect.css'

import * as Highcharts from "highcharts";
import Stock from "highcharts/modules/stock";
Stock(Highcharts);
import HighchartsVue from "highcharts-vue";


import "../../registerBaseComponents";

getI18nApi().then(({i18n}) => {
  new Vue({
    components: { CourseStatsDetails },
    i18n,
    render(createElement, context) {
      return createElement(CourseStatsDetails, context)
    }
  }).$mount('#CourseStatsUsers');

  new Vue({
    components: { CourseStatsUsers },
    i18n,
    render(createElement, context) {
      return createElement(CourseStatsUsers, context)
    }
  }).$mount('#CourseStatsDetails');

  new Vue({
    components: { CourseStatsOpinions },
    i18n,
    render(createElement, context) {
      return createElement(CourseStatsOpinions, context)
    }
  }).$mount('#CourseStatsOpinions');

  Vue.use(HighchartsVue);
  Vue.use(VueToast, {
    duration: 5000,
    position: 'top-right',
  });
})
