<template>
  <div class="CourseStatsDetailsModal">
    <BaseModal
      identifier="CourseStatsDetailsModal"
      size="modal-xl"
      padding="0"
      :title="courseData.name || ''"
      @close="closeModal"
    >
      <div v-if="!chapterData.length">
        <spinner />
      </div>
      <div class="row mx-0" v-if="chapterData.length">
        <div class="col-lg-3 col-md-6 col-sm-12">
          <chart
            v-if="charLoaded"
            :options="pieConfig"
            :is-loading="false"
            :show-header="false"
          />
        </div>
        <div
          class="col-lg-9 col-md-6 col-sm-12 d-flex flex-column justify-content-between py-3"
        >
          <div class="d-flex gap-2 font-weight-bold">
            <p class="my-0"><i class="fa fa-user" /></p>
            <p class="my-0">
              {{
                $t("COURSE.COURSE_SECTION.STATS_RESUMEN", [
                  totalFinished,
                  totalStarted,
                  totalInactives,
                ])
              }}
              <br />
              {{ $t("COURSE.COURSE_SECTION.STATS_RESUMEN_AVG", [totalAvg]) }}
            </p>
          </div>
          <div v-if="!allUsersInChapters">
            <p
              class="my-0 alert alert-info font-weight-bold"
              style="font-size: 14px"
            >
              ** {{ $t("ITINERARY.CHAPTERS_EXCEPTION_MESSAGE") }} **
            </p>
          </div>
       <!--   <div class="text-right">
            <button
              class="btn btn-sm btn-primary"
              data-bs-toggle="modal"
              data-bs-target="#userCourseExcelStatsModal"
            >
              <i class="fa fa-download"></i>
              {{ $t("ANNOUNCEMENT.MODALS.REPORT_XML") }}
            </button>
          </div>-->
        </div>
      </div>
      <div class="table-responsive mt-3" v-if="chapterData.length">
        <table class="table table-condensed">
          <thead>
            <tr>
              <th>{{ $t("CHAPTERS.LABEL.PLURAL") }}</th>
              <th>{{ $t("TYPE") }}</th>
              <th>{{ $t("ANNOUNCEMENT.STATUS.IN_PROCESS") }}</th>
              <th>{{ $t("FINISHED") }}</th>
              <th>{{ $t("TOTAL_TIME") }}</th>
              <th class="text-center">{{ $t("RESULTS") }}</th>
            </tr>
          </thead>
          <tbody class="position-relative">
            <tr
              v-for="(chapter, index) in chapterData"
              :key="'chapter_' + index"
            >
              <td>
                <img :src="chapter.image" :alt="chapter.name" />
                {{ chapter.name }}
              </td>
              <td>
                <img :src="getIconDir(chapter.icon)" alt="Chapter Type" />
                <span class="text-capitalize">{{ chapter.type }}</span>
              </td>
              <td>
                <b>{{ chapter.inProcessPorc }} %</b>
              </td>
              <td>
                <b>{{ chapter.finishedPorc }} %</b>
              </td>
              <td>{{ chapter.time }}</td>
              <td class="text-center text-primary">
                <b
                  v-if="chapter.showView"
                  class="cursor-pointer"
                  data-bs-toggle="modal"
                  data-bs-target="#generalCourseStatsModal"
                  @click="setChapter(index)"
                >
                  {{ $t("VIEW") }}
                </b>
                <b v-else>-</b>
              </td>
            </tr>
            <tr v-if="!chapterData.length">
              <td class="text-center" colspan="10">{{ $t("EMPTY.TITLE") }}</td>
            </tr>
            <tr v-if="loadingData" class="tableLoadingInfo">
              <td colspan="10" class="text-center">
                <b>{{ $t("LOADING") }}...</b>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <Pagination
        v-if="chapterData.length"
        :total-items="totalItems"
        :page-size="pageSize"
        :prop-current-page="currentPage"
        :disabled="loadingData"
        @current-page="setCurrentPage"
      />
    </BaseModal>
    <StatsChapterCourse
      :chapter-data="chapterSelected"
      :courseData="courseBaseData"
      :itinerary="true"
    />
    <ExcelDownloadModal
      :filters="currentFilters"
      :chapters="chapterData"
      :sending-loading="sendingExcelData"
      @download="downloadExcel"
    />
  </div>
</template>

<script>
import BaseModal from "../../../base/BaseModal.vue";
import Chart from "../../stats/Chart.vue";
import { GeneralStatsModel } from "../models/GeneralStatsModel";
import StatsChapterCourse from "./StatsChapterCourse.vue";
import axios from "axios";
import ExcelDownloadModal from "./ExcelDownloadModal.vue";
import Pagination from "../../components/Pagination.vue";
import Spinner from "../../../base/BaseSpinner.vue";
import TaskQueueMixin from '../../../mixins/TaskQueueMixin';

export default {
  name: "CourseStatsDetailsModal",
  components: {
    Spinner,
    ExcelDownloadModal,
    StatsChapterCourse,
    Chart,
    BaseModal,
    Pagination,
  },
  props: {
    courseId: { type: Number, default: 0 },
    itineraryId: { type: String, default: "" },
    courseData: { type: Object, default: () => ({}) },
    isModalOpen: { type: Boolean, default: false },
  },
  data: () => ({
    pieConfig: {
      type: "pie",
      height: 200,
      series: [],
    },
    charLoaded: false,
    chapterData: [],
    coursesData: [],
    chapterSelected: undefined,
    totalStarted: 0,
    totalFinished: 0,
    allUsersInChapters: true,
    totalInactives: 0,
    totalAvg: 0,
    loadingData: false,
    sendingExcelData: false,
    currentFilters: {},
    courseBaseData: undefined,
    pageSize: 10,
    currentPage: 1,
    totalItems: 0,
  }),
  computed: {
    translationData: () => courseTranslations || {},
    assetsHome() {
      return assetsDir || "";
    },
  },
  watch: {
    isModalOpen(newVal) {
      if (newVal) {
        this.loadData();
      } else {
        this.chapterData = [];
        this.pieConfig = {
          type: "pie",
          height: 200,
          series: [],
        };
        this.totalStarted = 0;
        this.totalFinished = 0;
        this.totalFinished = 0;
        this.totalInactives = 0;
        this.totalAvg = 0;
      }
    },
  },
  mixins: [
    TaskQueueMixin
  ],
  methods: {
    setChapter(index) {
      this.chapterSelected = this.chapterData[index];
      this.loadingChapterDetails(this.chapterSelected);
      // this.courseBaseData = this.courseData.courseBaseData;
    },
    getIconDir(icon = "") {
      return `${this.assetsHome}${icon}`;
    },
    loadData() {
      this.chapterData = (this.courseData.chaptersData || []).map(
        (user) =>
          new GeneralStatsModel({
            inProcess: user.inProgress,
            totalTime: user.time,
            ...user,
          })
      );
      this.totalItems = this.courseData.chaptersData.totalItems;
      this.totalStarted = this.courseData.totalStarted || 0;
      this.totalFinished = this.courseData.totalFinished || 0;
      this.allUsersInChapters = this.courseData.allUsersInChapters;
      this.totalInactives = Math.max(
        (this.courseData.totalUsers || 0) -
          this.totalFinished -
          this.totalStarted,
        0
      );
      const totalUsers =
        this.totalStarted + this.totalFinished + this.totalInactives;
      this.totalAvg = (
        totalUsers ? (this.totalFinished * 100) / totalUsers : 0
      ).toFixed(0);
      this.pieConfig.series = [
        {
          name: `${this.$t("NO_STARTING")}: ${this.totalInactives}`,
          y: +this.totalInactives,
          color: "#CBD5E1",
        },
        {
          name: `${this.$t("FINISHED")}: ${this.totalFinished}`,
          y: +this.totalFinished,
          color: "#009BDB",
        },
        {
          name: `${this.$t("STARTED")}: ${this.totalStarted}`,
          y: +this.totalStarted,
          color: "#0074A4",
        },
      ];
      this.charLoaded = true;
      this.porcentChapterData();
    },
    porcentChapterData() {
      this.chapterData.forEach((chapter) => {
        let total = chapter.inProcess + chapter.finished;
        if (total > 0) {
          chapter.inProcessPorc =
            chapter.inProcess > 0
              ? parseFloat(chapter.inProcess).toFixed(0)
              : 0;
          chapter.finishedPorc =
            chapter.finished > 0 ? parseFloat(chapter.finished).toFixed(0) : 0;
        } else {
          chapter.inProcessPorc = 0;
          chapter.finishedPorc = 0;
        }
      });
    },
    async loadingChapterDetails(chapter) {
      const {
        data: { data },
      } = await axios.post(
        `/admin/api/v1/statistics/chapters/${chapter.id}/progress`,
        {
          itineraryId: this.itineraryId,         
         
        }
      );
      chapter.setDetails(data);
    },
    async getChaptersCourseItinerary() {
      this.chapterData = [];
      const payload = this.paramsToString({
        page: this.currentPage,
      });
      let route = `admin/api/v1/statistics/itineraries/${this.itineraryId}/courses/${this.courseId}/chapters?${payload}`;
      await axios.get(route).then((response) => {
        this.chapterData = (response.data?.data?.chapters || []).map(
          (user) =>
            new GeneralStatsModel({ inProcess: user.inProgress, ...user })
        );
        this.porcentChapterData();
      });
    },
    paramsToString(obj = {}) {
      return Object.keys(obj)
        .filter((key) => !!obj[key])
        .reduce((acc, key) => [...acc, `${key}=${obj[key]}`], [])
        .join("&");
    },
    async downloadExcel(filters) {
        if (this.sendingExcelData) return null;
        this.sendingExcelData = true;
        
        const url = this.itineraryId ?
            `/admin/itinerary/${this.itineraryId}/zip-export` :
            `/admin/api/v1/course-stats/${this.courseId}/xlsx?announcementId=${this.announcementId}`;
        
        try {
            await this.enqueueTask({
                url,
                data: filters,
                messages: {
                    success: `${this.translationData.export_success}<br/>(${this.$t("COURSE_STATS.EXPORT.ZIP_DIR")})`,
                    error: this.translationData.export_error
                }
            });
        } catch (error) {
            console.error('Error:', error);
        } finally {
            this.sendingExcelData = false;
        }
    },
    closeModal() {
      this.$emit("close"); // Emitir evento para informar al padre
    },
    setCurrentPage(value) {
      if (this.loadingData) return null;
      this.currentPage = value;
      this.getChaptersCourseItinerary();
    },
  },
};
</script>
<style>
.modal-body {
  padding: 10px !important;
}
</style>