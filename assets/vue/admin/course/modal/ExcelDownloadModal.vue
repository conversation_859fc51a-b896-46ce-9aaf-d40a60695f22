<template>
  <div class="ExcelDownloadModal">
    <BaseModal
      identifier="userCourseExcelStatsModal"
      size="modal-lg"
      padding="0"
      :title="`${ $t('GENERATE_REPORT') }`"
    >
      <div class="row mx-0">
        <div class="col-12 p-4">
          <div class="row mx-0 p-3 bg-gray" style="border: 1px solid #B0BEC5; border-radius: 7px;">
            <div class="col-12 text-center pb-2">
              <label class="d-flex gap-2 align-self-center text-primary mx-auto user-select-none" style="width: fit-content;">
                <BaseSwitch tag="ExcelDownloadModal_selectAll" v-model="selectAll" @input="(val) => setSelectAll(val)"/>
                <b>{{ $t('COMPONENT.USER_FILTER.SELECT_ALL') }}</b>
              </label>
            </div>
            <div class="mb-3" :class="cardClass">
              <div class="bg-white p-3">
                <div class="d-flex gap-2 align-self-center mx-auto user-select-none justify-content-between">
                  <b>{{ $t('COURSE.COURSE_SECTION.EXPORT_EXCEL_COURSES') }}</b>
                  <BaseSwitch tag="ExcelDownloadModal_courseDetails" v-model="exportFilters.courseDetails" @input="setCourseDetailsValue"/>
                </div>
                <p class="subtitle mb-0">{{ $t('COURSE.COURSE_SECTION.EXPORT_EXCEL_COURSES_DESC') }}</p>
              </div>
            </div>
            <div :class="cardClass">
              <div class="bg-white p-3">
                <div class="d-flex gap-2 align-self-center mx-auto user-select-none justify-content-between">
                  <b>{{ $t('COURSE.COURSE_SECTION.EXPORT_EXCEL_CHAPTERS') }}</b>
                  <BaseSwitch tag="ExcelDownloadModal_chapterDetails" v-model="exportFilters.chapterDetails.export" @input="setChapterDetailsValue"/>
                </div>
                <p class="subtitle mb-0">{{ $t('COURSE.COURSE_SECTION.EXPORT_EXCEL_CHAPTERS_DESC') }}</p>
                <div class="row mx-0">
                  <Multiselect
                    :options="chapters"
                    track-by="id"
                    label="name"
                    :multiple="true"
                    :close-on-select="false"
                    :preselect-first="true"
                    :show-labels="false"
                    :placeholder="$t('SELECT')"
                    class="col-12 px-0"
                    v-model="exportFilters.chapterDetails.chapters"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-12 pb-4 text-center">
          <button
            class="btn btn-sm btn-primary"
            data-bs-dismiss="modal"
            :class="{ disabled }"
            @click="emitDownload">
            <i class="fa mr-1" :class="sendingLoading ? 'fa-spinner' : 'fa-file-alt'"></i>
            {{ $t('ANNOUNCEMENT.MODALS.REPORT_XML') }}
          </button>
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script>
import BaseModal from '../../../base/BaseModal.vue'
import BaseSwitch from '../../../base/BaseSwitch.vue'
import CheckItem from '../../../inspectorFundae/components/checkItem.vue'
import Multiselect from 'vue-multiselect'

export default {
  name: "ExcelDownloadModal",
  components: { CheckItem, BaseSwitch, BaseModal, Multiselect },
  props: {
    filters: { type: Object, default: () => ({}) },
    chapters: { type: Array, default: () => [] },
    sendingLoading: { type: Boolean, default: false },
  },
  data: () => ({
    selectAll: true,
    exportFilters: {
      courseDetails: true,
      chapterDetails: {
        export: true,
        chapters: []
      }
    }
  }),
  computed: {
    chapterActives() {
      return this.exportFilters.chapterDetails.chapters.map((chapter) => chapter.id)
    },
    disabled() {
      return !this.exportFilters.chapterDetails.export && !this.exportFilters.courseDetails || !this.chapters.length
    },
    cardClass() {
      return this.chapters.length > 4 ? 'col-12' : 'col-md-6 col-sm-12'
    }
  },
  watch: {
    chapters() {
      this.exportFilters.chapterDetails.chapters = this.chapters.map((chapter) => ({ id: chapter.id, name: chapter.name }))
    }
  },
  methods: {
    setSelectAll(val) {
      this.selectAll = val
      this.setCourseDetailsValue(val)
      this.setChapterDetailsValue(val)
    },
    setCourseDetailsValue(val) {
      this.exportFilters.courseDetails = val
      if (!this.exportFilters.courseDetails) this.selectAll = false
      else if (this.exportFilters.chapterDetails.export) this.selectAll = true
    },
    setChapterDetailsValue(val, updateAll = true) {
      this.exportFilters.chapterDetails.export = val
      this.selectAll = val && this.exportFilters.courseDetails;
      
      if (val && !this.chapterActives.length) {
        this.exportFilters.chapterDetails.chapters.forEach((chapter) => {
          chapter.options[0].active = true
        })
      }
    },
    emitDownload() {
      if (this.disabled || this.sendingLoading) return null
      this.$emit('download', {
        ...this.filters,
        ...(this.exportFilters.courseDetails && { courseDetails: true}),
        ...(this.exportFilters.chapterDetails.export && { chapters: this.chapterActives})
      })
    }
  }
}
</script>

<style>
.checkItem ul {
  margin: 0;
  width: 3rem;
  padding: 0 !important;
  
  li::before {
    display: none !important;
  }
}
</style>