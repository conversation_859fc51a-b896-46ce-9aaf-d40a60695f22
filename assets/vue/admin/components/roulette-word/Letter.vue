<template>
  <div class="Letter" :class="[`state--${state}`]">
    {{ letter }}
  </div>
</template>

<script>
const states = ["default", "correct", "selected"];

export default {
  props: {
    letter: {
      type: String,
      default: "",
    },

    state: {
      type: String,
      default: "default",
      validator: (state) => states.includes(state),
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Letter {
  cursor: pointer;
  $size: 40px;

  width: $size;
  height: $size;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;

  text-transform: uppercase;
  //font-family: $font-family-secondary;
  border-radius: 50px;
  background: var(--local-background);
  color: var(--local-color);
  box-shadow: var(--local-shadow);
  transition: all 0.3s ease-in-out;

  &.state {
    &--default {
      --local-background: var(--color-neutral-mid);
      --local-color: var(--color-neutral-mid-darker);
      --local-shadow: none;
    }

    &--correct {
      --local-background: #{$color-success};
      --local-color: #fff;
      --local-shadow: #{$shadow-elevation-1};
    }

    &--selected {
      --local-background: #{$color-info};
      --local-color: #{$color-info};
      --local-shadow: none;
    }
  }
}
</style>
