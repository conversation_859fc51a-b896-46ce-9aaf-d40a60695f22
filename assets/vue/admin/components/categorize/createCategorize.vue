<template>
  <div class="add-question">
    <!--    <p><b>{{ translationsVue.games_categorize }} </b></p>-->
    <div class="categorize-form">
      <div class="form-question-input">
        <div class="mb-3">
          <label for="question" class="form-label">
            {{ translationsVue.question_label_in_singular }}</label
          >
          <textarea
            type="text"
            class="form-control"
            name="question"
            v-model="questionCopy.question"
            rows="5"
          />
        </div>

        <div>
          <label for="title" class="form-label"
            >{{ translationsVue.games_text_common_time }}
          </label>
          <input
            type="time"
            id="appt"
            name="appt"
            class="form-control"
            v-model="time"
            min="00:00:00"
            max="23:00:00"
          />
        </div>
      </div>

      <div class="image-question">
        <label>{{ translationsVue.games_text_common_ilustre_question }}</label>
        <div
          :style="{ backgroundImage: 'url(' + currentImage + ')' }"
          :class="
            image == null && questionCopy.image == null
              ? 'preview-image preview-image-default'
              : 'preview-image'
          "
          @click="$refs.inputFile.click()"
        ></div>

        <div class="mb-3 mt-2">
          <input
            type="file"
            @change="loadImage($event)"
            accept="image/*"
            ref="inputFile"
          />

          <a class="btn-sm btn btn-primary" @click="$refs.inputFile.click()">
            <i class="fas fa-upload"></i>
            {{ translationsVue.games_text_common_select_image }}
          </a>

          <a class="btn-sm btn btn-danger" @click="removeImage()">
            <i class="fas fa-trash-alt"></i>
          </a>
        </div>
      </div>
    </div>

    <div class="optiones">
      <label> {{ translationsVue.games_opciones }}</label>
      <div
        class="row mt-2"
        v-for="(category, index) in question.categorizeAnswers"
        :key="category.id"
      >
        <div class="col-md-12">
          <div class="form-check form-check row text-break">
            <input
              class="form-check-input"
              type="checkbox"
              :id="`inlineCheckbox${category.id}`"
              value="option1"
              v-model="category.correct"
              @input="checkSelected(index)"
            />
            <label
              class="form-check-label"
              :for="`inlineCheckbox${category.id}`"
              >{{ category.options.name }}
            </label>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-5" v-if="!processSave">
      <button class="btn btn-primary btn-sm" @click="editQuestion()">
        {{ translationsVue.Save }}
      </button>
    </div>
    <div v-else class="text-center">
      <Spinner />
    </div>
  </div>
</template>
  
  <script>
import { get } from "vuex-pathify";
import Spinner from "../base/Spinner";

import { modalMixin } from "../../../mixins/modalMixin";
import { alertToastMixin } from "../../../mixins/alertToastMixin";

export default {
  components: {
    Spinner,
  },

  props: {
    categories: {
      type: Array,
      required: true,
    },

    question: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      selectedCategory: "",
      image: null,
      preview: "/assets/common/add_image_file.svg",
      translationsVue,
      chapterId,
      time: "00:00:30",
      option: null,
      answersDeleted: [],
      processSave: false,
    };
  },

  mixins: [modalMixin, alertToastMixin],

  computed: {
    ...get("questionsGamesModule", ["isLoading", "getRouteChapter"]),

    routeChapter() {
      return this.getRouteChapter();
    },

    currentImage() {
      if (this.image) {
        return this.preview;
      }

      return this.getQuestionImage ?? this.preview;
    },

    getQuestionImage() {
      return this.question.image
        ? "/uploads/games/categorize/" + this.question.image
        : null;
    },

    questionCopy() {
      return JSON.parse(JSON.stringify(this.question));
    },
  },

  created() {
    this.getTime(this.questionCopy?.time);
  },

  methods: {
    checkSelected(index) {
      this.question.categorizeAnswers.forEach((ans, i) => {
        if (i != index) {
          ans.correct = false;
        }
      });
    },

    deletedCategory(index) {
      if (this.question.categorizeAnswers.length > 2) {
        this.answersDeleted.push(this.question.categorizeAnswers[index]);
        this.question.categorizeAnswers.splice(index, 1);
      }
    },

    loadImage(event) {
      const input = event.target;

      if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = (e) => {
          this.preview = e.target.result;
        };

        this.image = input.files[0];

        reader.readAsDataURL(input.files[0]);
      }
    },

    async editQuestion() {
      const verify = this.questionCopy.categorizeAnswers.filter(
        (category) => category.correct
      );

      if (
        verify.length === 1 &&
        (this.questionCopy.question !== "" || this.currentImage != null)
      ) {
        this.sendQuestionFormData();

        this.closeModal(`modal-edit-question${this.question.id}`);

        this.fetchQuestions();

        this.alertSuccesSave();
      } else {
        this.$toast.open({
          message: this.translationsVue.games_validate_add_categorize,
          type: "info",
          duration: 5000,
          position: "top-right",
        });
      }
    },

    async sendQuestionFormData() {
      const time = this.time;
      const seconds = time.split(":");
      const secondsTime =
        +seconds[0] * 60 * 60 + +seconds[1] * 60 + +seconds[2];

      const formData = new FormData();
      formData.append("id", this.questionCopy.id);
      formData.append("question", this.questionCopy.question);
      formData.append("image", this.image);
      formData.append(
        "answers",
        JSON.stringify(this.questionCopy.categorizeAnswers)
      );
      formData.append("answersDeleted", JSON.stringify(this.answersDeleted));
      formData.append("idChapter", this.chapterId);
      formData.append("time", secondsTime);

      this.processSave = true;
      await this.$store.dispatch(
        "questionsGamesModule/editCategories",
        formData
      );
      this.processSave = false;
    },

    async fetchQuestions() {
      await this.$store.dispatch(
        "questionsGamesModule/fetchCategories",
        this.chapterId
      );
    },

    removeImage() {
      this.question.image = null;
      this.image = null;
      this.preview = "/assets/common/add_image_file.svg";
    },

    getTime(time) {
      const hours = Math.floor(time / 3600);
      const minutes = Math.floor((time - hours * 3600) / 60);
      const seconds = Math.floor(time - hours * 3600 - minutes * 60);

      const minutesFormat = minutes < 10 ? "0" + minutes : minutes;
      const secondsFormat = seconds < 10 ? "0" + seconds : seconds;
      const hoursFormat = hours < 10 ? "0" + hours : hours;

      this.time = hoursFormat + ":" + minutesFormat + ":" + secondsFormat;
    },
  },
};
</script>
  
   <style scoped lang="scss"> 
.add-question {
  .categorize-form {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
    .form-question-input {
      flex: 1;
    }
  }
  .preview-image {
    width: 250px;
    height: 181px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    cursor: pointer;
  }
  .preview-image-default {
    background-size: 30%;
  }

  input[type="file"] {
    display: none;
  }
}
</style>
  