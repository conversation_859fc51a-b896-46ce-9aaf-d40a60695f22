<template>
  <div v-if="questions && questions.length > 0" class="questions">
    <table class="datagrid">
      <thead style="height: 3rem" class="thead-light">
        <tr>
          <th scope="col" style="padding-left: 0.5rem">
            {{ translationsVue.common_areas_image }}
          </th>
          <th scope="col">
            {{ translationsVue.question_label_in_singular }}
          </th>
          <th scope="col">
            {{ translationsVue.question_configureFields_answers }}
          </th>
          <th scope="col">
            {{ translationsVue.games_text_common_time }}
          </th>
          <th scope="col">{{ translationsVue.state }}</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="question in questions" :key="question.id">
          <td>
            <a
              data-bs-toggle="modal"
              :data-bs-target="`#question${question.id}`"
            >
              <img
                :src="
                  question.image
                    ? `uploads/images/question/${question.image}`
                    : 'assets/chapters/default-image.svg'
                "
                alt=""
                @error="$event.target.src = 'assets/chapters/default-image.svg'"
              />
            </a>

            <BaseViewImagen
              :identifier="`question${question.id}`"
              :image="`uploads/images/question/${question.image}`"
            />
          </td>
          <td width="50%">{{ question.question }}</td>
          <td>{{ question.answers.length }}</td>
          <td>
            {{ convertSecondToHoursMinutesAndSeconds(question.time) }}
          </td>
          <td>
            <div
              :class="
                question.validated == false
                  ? 'icon tooltip-container icon-background-warning'
                  : 'icon tooltip-container icon-background-success'
              "
            >
              <i
                :class="
                  question.validated == false ? 'fas fa-info' : 'fas fa-check'
                "
              ></i>

              <span
                class="tooltip tooltip-top"
                v-if="question.validated == false"
              >
                {{ messageQuestion }}
              </span>
            </div>
          </td>
          <td class="text-right">
            <button
              type="button"
              class="btn-sm btn btn-danger"
              data-bs-toggle="modal"
              :data-bs-target="`#deleteModal${question.id}`"
            >
              <i class="fas fa-trash-alt"></i>
            </button>

            <button
              type="button"
              class="btn btn-primary btn-sm"
              :id="`editQuestion${question.id}`"
              data-bs-toggle="modal"
              :data-bs-target="`#staticBackdrop${question.id}`"
            >
              <i class="fas fa-edit"></i>
            </button>
          </td>

          <div
            class="modal fade"
            :id="`staticBackdrop${question.id}`"
            data-bs-backdrop="static"
            data-bs-keyboard="false"
            tabindex="-1"
            aria-labelledby="staticBackdropLabel"
            aria-hidden="true"
          >
            <div class="modal-dialog modal-dialog-centered modal-xl">
              <div class="modal-content">
                <div class="modal-header">
                  <h5
                    class="modal-title"
                    id="staticBackdropLabel"
                    v-if="chapteType !== 7"
                  >
                    {{ translationsVue.quiz_configureFields_title_creation }}
                  </h5>
                  <h5 class="modal-title" id="staticBackdropLabel" v-else>
                    {{ translationsVue.hiddenword_configureFields_title }}
                  </h5>
                  <button
                    type="button"
                    class="btn-close"
                    data-bs-dismiss="modal"
                    aria-label="Close"
                  ></button>
                </div>
                <div class="modal-body">
                  <EditForm :questions="question" key="editQuestion" />
                </div>
              </div>
            </div>
          </div>

          <BaseModalDelete
            :identifier="`deleteModal${question.id}`"
            :title="translationsVue.quiz_configureFields_question_delete"
            @delete-element="deleteQuestion(question.id)"
          />
        </tr>
      </tbody>
    </table>
    <div class="list-pagination-counter mt-3 px-3">
      <strong>{{ questions.length }}</strong>
      {{ translationsVue.common_results }}
    </div>
  </div>
</template>

<script>
import { formatDateMixin } from "../../../mixins/formatDateMixin";
import EditForm from "./EditForm";
export default {
  components: {
    EditForm,
  },

  props: {
    questions: {
      type: Array,
      required: true,
    },
  },

  mixins: [formatDateMixin],

  data() {
    return {
      translationsVue,
      messageQuestion,
      chapteType,
    };
  },

  methods: {
    deleteQuestion(id) {
      this.$emit("delete-question", id);
    },
  },
};
</script>

 <style scoped lang="scss"> 
.questions {
  background: #fff;
}
.tooltip-container {
  position: relative;
  display: inline-block;
  color: white;
  width: 1.7rem;
  height: 1.5rem;
  border-radius: 0.2rem;
  text-align: center;
  padding: 3.2px;
}

.table .thead-light {
  tr {
    th {
      padding: 0.5rem;
    }
  }
}

img {
  width: 50px;
  cursor: zoom-in;
}
</style>
