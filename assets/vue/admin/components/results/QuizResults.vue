<template>
    <div class="results">
        <div class="header">
            <h3>Intento {{ number }}</h3>
            <span class="quiz-results-info">({{ correctAnswers }}/{{ quiz.length }})</span>
            <span class="quiz-toggle-questions fas"
                  :class="show ? 'fa-minus' : 'fa-plus'"
                  @click="show = !show"

            ></span>
        </div>
        <div class="questions" v-show="show">
            <div class="question" v-for="(item, index) in quiz"  :key="index">
                <h5>{{ item.question.question }}</h5>
                <div class="answers">
                    <div class="answer"
                         v-for="(answer, option) in item.question.answers"
                         :class="answer.id === item.id ? 'selected' : ''"
                    >
                        <div class="answer-option"
                             :class="answer.correct ? 'correct' : answer.id === item.id ? 'wrong' : ''"
                        >{{ options[option] }}</div>
                        <div class="answer-text">{{ answer.answer }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name: 'QuizResults',
    props: ['quiz', 'number'],

    data() {
        return {
            options: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
            show: false,
        }
    },

    computed: {

        correctAnswers: function () {
            let correct = 0;
            for (let i = 0; i < this.quiz.length; i++) {
                if (this.quiz[i].correct) {
                    correct++;
                }
            }

            return correct;
        },
    }
};
</script>

 <style scoped lang="scss"> 
</style>
