<template>
  <div class="VimeoResource">
    <div class="modal" v-if="showModal == 1">
      <img
        :src="`/assets/video/replay.svg`"
        class="iconsSvg"
        @click="reloadPage()"
      />

      <img
        :src="`/assets/video/cerrar.svg`"
        class="iconsSvg"
        @click="closeModal()"
      />
    </div>

    <div v-if="infoChapter != undefined && showModal == 0">
      <vimeo-player
        ref="player"
        :class="`vimeo-${orientation}`"
        :video-url="urlvideo"
        @ready="onReady"
        @playing="playing"
        @pause="pause"
        @ended="ended"
        @loaded="loaded"
        :options="{
          autoplay: 1,
          title: false,
          responsive: true,
          loop: false,
          texttrack: subtitle,
        }"
      />
    </div>

    <div v-if="infoChapter == undefined && showModal == 0">
      <spinner />
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Spinner from "../base/Spinner";
export default {
  name: "vimeo-resource",
  components: {
    Spinner,
  },
  props: ["id", "identifier", "urlvideo", "widthvideo", "heightvideo"],
  data() {
    return {
      height: 500,
      playerReady: false,
      infoChapter: undefined,
      showModal: 0,
      subtitle: "",
      orientation: "horizontal",
    };
  },

  async created() {
    this.orientation = (this.widthvideo > this.heightvideo) ? 'horizontal' : 'vertical';
    this.infoChapter = await this.$store.dispatch(
      "chapterModule/getState",
      this.id
    );
    await this.$store
      .dispatch("chapterModule/getState", this.id)

      .then(async (data) => {
        this.removeStyle();
        await this.$store.dispatch("timeModule/initStart", this.userChapter());

        const chapterStatus = {
          chapter: this.id,
          finished: false,
        };
        if (!this.infoChapter) {
          await this.$store.dispatch(
            "chapterModule/updateState",
            chapterStatus
          );
        }
      });



  },

  mounted() {
    setInterval(() => {
      this.saveTime();
    }, 10000);
    this.removeStyle();


  },

  computed: {
    ...get("chapterModule", ["userChapter"]),
    player() {
      return this.$refs.player.player;
    },
  },

  methods: {
    async loaded() {
      const getSubtitles = await this.player.getTextTracks();
      this.subtitle = getSubtitles[0] ? getSubtitles[0].language : "";
      if (this.subtitle) {
        await this.player.enableTextTrack(this.subtitle);
      }
    },

    async onReady() {
      const duration = await this.player.getDuration();
      if (this.infoChapter.timeVideo && this.infoChapter.timeVideo < duration) {
        await this.player.setCurrentTime(this.infoChapter.timeVideo);
        this.removeStyle();
      }
    },

    async pause() {
      let time = await this.player.getCurrentTime();
      const chapterStatus = {
        chapter: this.id,
        finished: false,
        timeVideo: Math.round(time),
      };

      if (this.infoChapter.finished) {
        await this.$store.dispatch("chapterModule/updateState", chapterStatus);
      }
    },

    async playing() {
      let time = await this.player.getCurrentTime();
      const chapterStatus = {
        chapter: this.id,
        finished: false,
        timeVideo: Math.round(time),
      };

      if (!this.infoChapter.finished) {
        await this.$store.dispatch("chapterModule/updateState", chapterStatus);
      }
    },

    async ended() {
      const chapterStatus = {
        chapter: this.id,
        finished: true,
      };
      await this.saveTime();
      await this.$store.dispatch("chapterModule/updateState", chapterStatus);
      this.infoChapter = await this.$store.dispatch(
        "chapterModule/getState",
        this.id
      );
      this.showModal = 1;
    },

    async saveTime() {
      let time = await this.player.getCurrentTime();
      const chapterStatus = {
        chapter: this.id,
        finished: false,
        timeVideo: Math.round(time),
      };

      if (!this.infoChapter.finished) {
        await this.$store.dispatch("chapterModule/updateState", chapterStatus);
      }
    },

    closeModal() {
      window.parent.postMessage("close-modal", "*");
    },

    reloadPage() {
      location.reload();

    },

    removeStyle() {
     var target = document.querySelectorAll("div");

      Array.prototype.forEach.call(target, function (element) {
        element.removeAttribute("style");
      });

    },
  },
};
</script>

 <style scoped lang="scss"> 
.VimeoResource {
  width: 100%;
  height: 100%;
  background: black;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;

  .modal {
    display: flex;
    border-radius: 5px;
    padding: 3rem;
    text-align: center;

    .iconsSvg {
      width: 5rem;
      margin: 0.5rem;
      cursor: pointer;
      transition: transform 0.5s ease-in-out 0.5s;

      &:hover {
        transform: scale(1.2);
      }
    }
  }
}

.spinner {
  margin: auto;
  margin-top: 20%;
}
.vimeo-horizontal {
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  height: 100%;
  width: 100%;
  position: absolute;
  margin: auto;
  display: grid;
  align-self: center;

}

.vimeo-vertical {
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  height: 100%;
  width: 25%;
  position: absolute;
  margin: auto;
  display: grid;
  align-self: center;
}

@media only screen and (max-width: 600px) {
  .vimeo-vertical {
    width: 100%;
  }
}
</style>
