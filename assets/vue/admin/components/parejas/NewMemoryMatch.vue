<template>
  <div class="new-title">
    <CreateMemoryMatch :pareja="pareja" @clears-inputs="clearsPairs" />
  </div>
</template>

<script>
import CreateMemoryMatch from "./CreateMemoryMatch";
export default {
  components: {
    CreateMemoryMatch,
  },

  data() {
    return {
      pareja: {
        text: "",
        time: 30,
        image: null,
      },
    };
  },

  methods: {
    clearsPairs() {
      this.pareja = {
        text: "",
        time: 30,
        image: null,
      };
    },
  },
};
</script>

 <style scoped lang="scss"> 
</style>
