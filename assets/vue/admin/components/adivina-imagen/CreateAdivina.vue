<template>
  <form @submit.prevent="saveQuestion" ref="form">
    <div class="new-question">
      <div class="options">
        <div class="form">
          <div class="form-head">
            <div class="form-inputs">
              <div>
                <BaseTextTarea
                  :label="translationsVue.quiz_configureFields_question"
                  :max="54"
                  :value.sync="adivina.title"
                  :placeholder="
                    translationsVue.quiz_configureFields_question_placeholder
                  "
                  :required="true"
                  :rows="2"
                  :submitted="submitted"
                ></BaseTextTarea>
              </div>

              <div class="time">
                <label for="title" class="form-label"
                  >{{ translationsVue.games_text_common_time }} 
                </label>
                <BaseInputTime
                  v-model="time"
                  :options="['minutes', 'seconds']"
                  :maxMinutes="31"
                  :time="time"
                  @time-update="timeUpdate"
                />
              </div>

              <div class="form-footer">
                <div class="words">
                  <BaseTextTarea
                    :label="translationsVue.guesword_configureFields_solution"
                    :min="2"
                    :max="20"
                    :value.sync="adivina.words"
                    :placeholder="
                      translationsVue.guesword_configureFields_solution_placeholder
                    "
                    :required="true"
                    :rows="2"
                    :submitted="submitted"
                    :preventSpace="true"
                    :validateSpecialCharacters="true"
                    :acceptSpecialCharactersEnie="true"
                  />
                </div>

                <div class="help">
                  <BaseTextTarea
                    :label="translationsVue.games_help"
                    :max="60"
                    :value.sync="adivina.clue"
                    :placeholder="
                      translationsVue.guesword_configureFields_help_placeholder
                    "
                    :rows="2"
                  ></BaseTextTarea>
                </div>
              </div>
            </div>

            <div class="form-image">
              <BaseInputFileImage
                :url-image="currentImage"
                :title="translationsVue.games_text_common_ilustre_question"
                :delete-image="deleteImage"
                size="s"
                @file-selected="fileSelected($event)"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="mt-2 text-center">
        <button
          v-show="0"
          type="button"
          class="btn btn-secondary"
          data-bs-dismiss="modal"
          ref="closeChapterContentModal"
        ></button>

        <button
          class="btn-sm btn btn-primary"
          type="submit"
          @click="submitted = true"
        >
          {{ translationsVue.Save }}
        </button>
      </div>
    </div>
  </form>
</template>
  
  <script>
import BaseInputFileImage from "../base/BaseInputFileImagen";
import { formatDateMixin } from "../../../mixins/formatDateMixin";
import { modalMixin } from "../../../mixins/modalMixin";
import { alertToastMixin } from "../../../mixins/alertToastMixin";

export default {
  components: {
    BaseInputFileImage,
  },

  props: {
    adivina: {
      type: Object,
      required: true,
    },
  },

  mixins: [formatDateMixin, modalMixin, alertToastMixin],

  data() {
    return {
      image: null,
      preview: null,
      chapteType: typeChapter,
      translationsVue,
      deleteImage: false,
      submitted: false,
      time: "00:00:30",
    };
  },

  watch: {
    adivina: {
      immediate: true,
      handler() {
        this.time =
          this.convertSecondToHoursMinutesAndSeconds(this.adivina.time) ??
          "00:00:30";
      },
    },
  },

  computed: {
    currentImage() { 
      return this.getQuestionImage ?? this.preview;
    },

    getQuestionImage() {
      return this.adivina.image
        ? "uploads/games/adivinaImagen/" + this.adivina.image
        : null;
    },
  },

  methods: {

    fileSelected(file) {
      this.image = file;
      this.adivina.image = file.name;
    },

    async saveQuestion() {
      if (!this.$refs.form.checkValidity()) {
        return;
      }

      if (
        this.adivina.title == "" ||
        this.currentImage == null ||
        this.adivina.words == ""
      ) {
        this.$toast.open({
          message: this.translationsVue.games_validate_hidden_image,
          type: "warning",
          duration: 5000,
          position: "top-right",
        });
      } else {
        await this.createGame();

        this.alertSuccesSave();
        this.fetchQuestions();
        this.$refs["closeChapterContentModal"].click();
        this.clearInputs();
        // this.closeModal(`modal-edit-question${this.adivina.id}`);
      }
    },

    async updateGame() {
      const secondsTime = this.convertDateHoursMinutesAndSeconds(this.time);

      const formData = new FormData();
      formData.append("id", this.adivina.id);
      formData.append("title", this.adivina.title);
      formData.append("words", this.adivina.words);
      formData.append("time", secondsTime);
      formData.append("help", this.adivina.clue);
      formData.append("image", this.image); // this.image is a File object
      formData.append("idChapter", chapterId);

      await this.$store.dispatch(
        "adivinaImagenModule/editAdivinaimagen",
        formData
      );
    },

    async createGame() {
      const secondsTime = this.convertDateHoursMinutesAndSeconds(this.time);

      const formData = new FormData();
      formData.append("id", this.adivina.id);
      formData.append("title", this.adivina.title);
      formData.append("words", this.adivina.words);
      formData.append("time", secondsTime);
      formData.append("help", this.adivina.clue);
      formData.append("image", this.image);
      formData.append("idChapter", chapterId);

      return this.adivina?.id
        ? await this.$store.dispatch(
            "adivinaImagenModule/editAdivinaimagen",
            formData
          )
        : await this.$store.dispatch(
            "adivinaImagenModule/newAdivinaimagen",
            formData
          );
    },

    async fetchQuestions() {
      await this.$store.dispatch("adivinaImagenModule/fetchAdivina", chapterId);
    },

    clearInputs() {
      this.adivina.title = "";
      this.adivina.words = "";
      this.adivina.clue = "";
      this.adivina.image = null;
      this.image = null;
      this.preview = null;
      this.deleteImage = true;
      this.time = "00:00:30";
      this.submitted = false;
      this.help = "";
    },

    timeUpdate(time) {
      this.time = time;
    },
  },
};
</script>
  
   <style scoped lang="scss"> 
.new-question {
  .preview-image {
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 1px solid #ccc;
  }

  input[type="file"] {
    display: none;
  }

  .trash-question {
    display: flex;
    justify-content: center;
    align-content: center;
    flex-direction: column;
    .trash {
      cursor: pointer;
    }
  }

  .options {
    .form {
      height: 100%;
      flex: 2;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      flex-wrap: wrap;
      .form-head {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 1rem;
        .form-inputs {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }
      }

      .form-footer {
        margin-top: 1rem;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 1rem;
        .words {
          flex-grow: 1;
        }
        .help {
          flex-basis: 50%;
        }
      }
    }
  }
}
</style>
  