<template>
  <div class="itinerary">
    <div
      class="itinerary-person-header col-lg-12 d-flex align-items-center justify-content-between py-2 px-0"
    >
      <slot name="content-header"></slot>
      <div class="justify-content-end" style="display: flex; gap: 0.4rem; flex-wrap: wrap;">
        <itinerary-user-filter
          :id="id"
          :url-get-filters="urlGetFilters"
          :url-get-users="urlGetUsers"
          :url-search-users="urlSearchUsers"
          :url-add-user="urlAddUser"
          :url-add-all="urlAddAll"
          :url-remove-user="urlRemoveUser"
          :url-remove-all="urlRemoveAll"
          :translations="translations.itinerary_user_filter"
          @cancel-edition="reloadItinerary()"
          :use-i18n="false"
          i18n-prefix="ITINERARY.USERS"
          @users-updated="usersUpdated"
        >
        </itinerary-user-filter>
        <itinerary-category-filter
          v-if="loadingAssignedFilters"
          :realtime="false"
          :value="assignedFilters"
          @input="setAssignedFilters"
          :save-selected-filters-raw="saveSelectedFiltersRaw"
          :translations="translations.itinerary_user_filter"
          :use-i18n="false"
          i18n-prefix="ITINERARY.USERS"
          @users-updated="usersUpdated"
        >
        </itinerary-category-filter>
        <slot name="filter-content"></slot>
      </div>
    </div>
    <div class="col-md-12 custom-content p-0" v-if="showCustomContent && !edit">
      <slot name="custom-content"></slot>
      <jw-pagination
          :items="paginatedUsers"
          v-on:changePage="onChangePage"
          :labels="paginationCustomLabels"
      ></jw-pagination>
    </div>
    <UserCourseDetails :course-id="courseId" :user-data="userCourseDetails" :course_data="userCourseData" :itinerary="true"/>
  </div>
</template>

<script>
import Multiselect from "vue-multiselect";
import Loader from "./Loader";
import axios from "axios";
import { get } from "vuex-pathify";
import store from "../store";
import JwPagination from "jw-vue-pagination";

import VueToast from "vue-toast-notification";
import "vue-toast-notification/dist/theme-sugar.css";
import VueAlertify from "vue-alertify";

import ItineraryUserFilter from "./ItineraryUserFilter.vue";
import ItineraryCategoryFilter from "./ItineraryCategoryFilter.vue";

import UserCourseDetails from '../course/modal/UserCourseDetails.vue'
import UserStatsModel from '../course/models/UserStatsModel'

export default {
  name: "Itinerary",
  components: {
    Loader,
    Multiselect,
    JwPagination,
    VueToast,
    VueAlertify,
    ItineraryCategoryFilter,
    ItineraryUserFilter,
    UserCourseDetails
  },
  store,
  computed: {
    ...get("userFilterModule", []),
    paginationCustomLabels() {
      return {
        first: "<<",
        last: ">>",
        previous: "<",
        next: ">",
      };
    },
    prefix() {
      return this.i18nPrefix.length > 0 ? `${this.i18nPrefix}.` : "";
    },
  },
  props: {
    useRestMethods: {
      type: Boolean,
      default: false,
    },

    id: {},
    paginatedUsers: {},
    courseId: undefined,
    userCourseDetails: {type: UserStatsModel, default: () => new UserStatsModel()},
    userCourseData: [],
    translations: {},
    showCustomContent: {
      type: Boolean,
      default: false,
    },
    urlGetFilters: {
      type: String,
      default: null,
    },
    urlSearchUsers: {
      //Url to find users
      type: String,
      default: null,
    },
    urlGetUsers: {
      //Load selected users
      type: String,
      default: null,
    },
    urlAddUser: {
      type: String,
      default: null,
    },
    urlAddAll: {
      type: String,
      default: null,
    },
    urlRemoveUser: {
      type: String,
      default: null,
    },
    urlRemoveAll: {
      type: String,
      default: null,
    },
    loadingAssignedFilters: {
      type: String,
      default: null,
    },
    assignedFilters: {
      default: () => {},
    },
    setAssignedFilters: {
      type: Function,
      default: () => {},
    },
    saveSelectedFiltersRaw: {
      type: Function,
      default: () => {},
    },
    useI18n: {
      type: Boolean,
      default: false,
    },
    i18nPrefix: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      edit: false,
      showFilters: false,
      loadingFilters: false,
      filters: [],
      pageOfUsers: [],
      translationTemp: {
        no_users: "No users have been added to the itinerary",
        cancel: "Cancel",
        modify_users: "Modify Users",
        assign_manual: "Assign manually",
        assign_filters: "Assign by Filters",
        placeholder_search_user: "Search for users",
        apply_filters: "Apply filters",
        result_found: "Results found",
        clear_result: "Clear Results",
        add_all: "Add All",
        remove_all: "Remove All",
        find_by: "Find by",
      },
      filterQueries: {},
      userSearchQuery: "",
      showAddRemoveLoader: false,

      searchingUsers: false,
      loadingUsers: false,
      users: [],
      availableUsers: [],
    };
  },
  async created() {
    await this.getUsers();
    await this.getFilters();
  },
  methods: {
    onChangePage(pageOfUsers) {
      this.pageOfUsers = pageOfUsers;
      this.$emit("on-change-page", pageOfUsers);
    },
    async getFilters() {
      if (this.urlGetFilters === undefined) return;
      this.loadingFilters = true;
      try {
        await axios.get(this.urlGetFilters).then((r) => {
          this.filters = r.data.data.filters;
        });
      } finally {
        this.loadingFilters = false;
      }
    },

    async searchUsers() {
      try {
        this.searchingUsers = true;
        await axios
          .post(this.urlSearchUsers, {
            searchQuery: this.userSearchQuery,
            filters: this.filterQueries,
          })
          .then((r) => {
            if (!r.data.error) {
              this.availableUsers = r.data.data.users;
            }
          });
      } finally {
        this.searchingUsers = false;
      }
    },

    clearUsers() {
      this.availableUsers = [];
      this.userSearchQuery = "";
      this.filterQueries = [];
    },

    async getUsers() {
      this.loadingUsers = true;
      try {
        await axios.post(this.urlGetUsers, { id: this.id }).then((r) => {
          if (!r.data.error) {
            this.users = r.data.data.users;
          }
        });
      } finally {
        this.loadingUsers = false;
      }
    },

    async add(index) {
      if (this.urlAddUser == null) {
        return;
      }

      this.showAddRemoveLoader = true;
      try {
        function addUser(useRest = false, url, user_id) {
          if (useRest) return axios.post(`${url}/${user_id}`);
          else return axios.post(url, { user_id });
        }

        let user = this.availableUsers[index];
        addUser(this.useRestMethods, this.urlAddUser, user.id)
          .then((res) => {
            const { message, error } = res.data;

            if (error) {
              this.$toast.error(
                message ??
                  (this.useI18n
                    ? this.$t(`${this.prefix}ADD_USER.ERROR`, [user.email])
                    : this.translations?.add?.error)
              );
            } else {
              this.$toast.success(
                message ??
                  (this.useI18n
                    ? this.$t(`${this.prefix}ADD_USER.SUCCESS`, [user.email])
                    : this.translations?.add?.success)
              );
              this.availableUsers.splice(index, 1);
              this.users.push(user);
              this.usersUpdated();
            }
          })
          .finally(() => {
            this.showAddRemoveLoader = false;
          });
      } finally {
        // this.showAddRemoveLoader = false;
      }
    },

    async addAll() {
      this.showAddRemoveLoader = true;
      try {
        const ids = this.availableUsers.map((user) => user.id);

        await axios
          .post(this.urlAddAll, {
            id: this.id,
            user_ids: ids,
          })
          .then((r) => {
            if (!r.data.error) {
              this.users = [...this.users, ...this.availableUsers];
              this.availableUsers = [];
              this.$toast.success(r.data.message);
              this.usersUpdated();
            }
          });
      } finally {
        this.showAddRemoveLoader = false;
      }
    },

    async removeUser(index) {
      this.showAddRemoveLoader = true;
      try {
        function remove(useRest = false, url, user_id) {
          if (useRest) return axios.delete(`${url}/${user_id}`);
          else return axios.post(url, { user_id });
        }

        const user = this.users[index];
        remove(this.useRestMethods, this.urlRemoveUser, user.id)
          .then((res) => {
            const { message, error } = res.data;
            if (error) {
              this.$toast.error(
                message !== undefined
                  ? message
                  : this.useI18n
                  ? this.$t(`${this.prefix}REMOVE_USER.ERROR`, [user.email])
                  : this.translations?.remove?.error
              );
            } else {
              this.$toast.success(
                message !== undefined
                  ? message
                  : this.useI18n
                  ? this.$t(`${this.prefix}REMOVE_USER.SUCCESS`, [user.email])
                  : this.translations?.remove?.success
              );
              this.users.splice(index, 1);
              this.usersUpdated();
            }
          })
          .finally(() => {
            this.showAddRemoveLoader = false;
          });
      } finally {
      }
    },

    async removeAll() {
      this.$alertify.confirmWithTitle(
        this.useI18n
          ? this.$t(`${this.prefix}REMOVE_ALL.CONFIRM.TITLE`)
          : this.translations.confirm_delete_all.title,
        this.useI18n
          ? this.$t(`${this.prefix}REMOVE_ALL.CONFIRM.DESCRIPTION`)
          : this.translations.confirm_delete_all.subtitle,
        async () => {
          this.showAddRemoveLoader = true;
          try {
            await axios
              .post(this.urlRemoveAll, {
                id: this.id,
              })
              .then((r) => {
                if (!r.data.error) {
                  this.users = [];
                  this.searchUsers();

                  this.$toast.success(r.data.message);
                  this.usersUpdated();
                }
              });
          } finally {
            this.showAddRemoveLoader = false;
          }
        },
        () => {}
      );
    },

    usersUpdated() {
      this.$emit("users-updated");

      console.log("users updated");
    },
  },
};
</script>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<style scoped lang="scss">
@import "../assets/config/_transitions.scss";

.users-container {
  .available-users.show {
    display: grid;
    max-height: 70vh;
  }

  .hide {
    display: none;
  }

  .action.show {
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
  }

  .max-height-70 {
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .available-users,
  .selected-users {
    border: 1px solid var(--color-neutral-mid-light);
    border-radius: 5px;
    padding: 0.5rem;
    background: var(--color-neutral-lighter);
  }

  .available-users,
  .selected-users {
    display: grid;
    gap: 0.25rem;
    align-content: flex-start;

    .card {
      padding: 0.75rem;
      flex-direction: row;
      align-items: center;

      .card-information {
        display: grid;
        flex: 1;
        gap: 0.125rem;
        font-size: 0.9rem;

        .card-firstName {
          font-weight: 500;
        }

        .card-email {
          font-style: italic;
          color: var(--color-neutral-dark);
        }
      }
    }
  }
}

.custom-content {
  display: grid;
  .pagination {
    justify-self: flex-end;
  }

  @media screen and (max-width: 768px) {
    overflow: auto;
  }
}
</style>
