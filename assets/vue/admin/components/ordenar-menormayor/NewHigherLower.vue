<template>
  <CreateHighLower :lower="lower" @clear-inputs="clearInputs" />
</template>

<script>
import CreateHighLower from "./CreateHighLower";
export default {
  components: {
    CreateHighLower,
  },

  data() {
    return {
      lower: {
        title: "",
        time: 30,
        higherLowerWords: [
          {
            id: 1,
            position: 1,
            word: "",
          },
          {
            id: 2,
            position: 2,
            word: "",
          },
        ],
      },
    };
  },

  methods: {
    clearInputs() {
      this.lower = {
        title: "",
        time: 30,
        higherLowerWords: [
          {
            id: 1,
            position: 1,
            word: "",
          },
          {
            id: 2,
            position: 2,
            word: "",
          },
        ],
      };
    },
  },
};
</script>

 <style scoped lang="scss"> 
</style>
