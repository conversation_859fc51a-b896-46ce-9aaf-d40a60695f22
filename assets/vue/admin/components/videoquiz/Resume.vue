<template>
  <tr>
      <td>  {{ block.title }} </td>
      <td> {{ block.url}} </td>
      <td>
        <div class="cursor" @click="modifyLine"> 
          <i class="fa fa-pen"></i>
          Modificar
        </div>

        <div class="cursor" @click="deleteLine">
          <i class="fa fa-trash"></i>
          Eliminar
        </div>
        <div class="cursor" @click="detailLine(1)">
          <i class="fa fa-list-alt"></i>
          Ver Preguntas
        </div>
        <div class="cursor" @click="detailLine(2)">
          <i class="fa fa-play"></i>
          Ver video
        </div>
      </td>
  </tr>
</template>

<script>

export default{
  props: {
    block: {
      type: Object,
      default: () => ({}),
    },
  },

  methods:{
    modifyLine(){
      const data = {
        id: this.block.id,
        url: this.block.url,
        title: this.block.title,
        pathImage: 'uploads/games/videoquiz/',
      }

      this.$emit('modifyLine', data);
    },

    deleteLine(){
      this.$emit('deleteLine', this.block.id);
    },

    detailLine(llamado){   
      const data = {
        id:  this.block.id,
        url: this.block.url,
        pathImage: 'uploads/games/videoquiz/',
        llamado:llamado,
        currentTime:0
      }
      this.$emit('detailLine', data); 
    }
  }
}

</script>

 <style scoped lang="scss"> 
  .Resume{
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .cursor{
    cursor: pointer;
  }
</style>
