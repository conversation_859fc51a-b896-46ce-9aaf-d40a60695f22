<template>
    <div class="GameBlock" v-bind="mostrarImagen" v-if="!mostrarImagen">
      <div class="options">
        <div class="title">
          <label>Agregar:</label>
        </div>
  
        <div class="question">
          <label for="title">Título:</label>
          <input type="text" id="title" v-model="title" placeholder="Título del juego">
        </div> 
  
        <div class="row">
          <div class="col-md-8" >
            <label for="image">Imagen: </label>
            <form>
              <input
                type="file"
                ref="image"
                accept="image/*"
                class="form-control-file"
                @change="fileSelected($event.target.files)"
              />
            </form>     
          </div>     
          
          <div class="col-md-4" v-if="editionActive">
            <label for="imageUrl">{{imageRoute}}</label>
            <img id="imageUrl" @click="mostrarImagen=true" :src="getImgUrl(imageRoute)"  width="150px" height="150px">
          </div>  
        </div>
  
        <div class="question">
          <label for="words">Palabras:</label>
          <input type="text" id="words" v-model="words" placeholder="Palabras a descubrir, separadas por espacio">
        </div>   
  
        <div class="question">
          <label for="clue">Ayuda:</label>
          <input type="text" id="clue" v-model="clue" placeholder="Texto de ayuda">
        </div> 
  
        <div class="question">
          <label for="time">Tiempo (segundos):</label>
          <input type="number" id="time" v-model="time" min="1">
        </div> 
  
        <div>       
            <label class="labelMensaje" v-if="mensajeActivo">{{mensaje}}</label>
        </div>
  
        <button
            type="button" data-dismiss="modal" class="btn btn-primary ml-1"
            @click="checkIfComplete"
          ><i class="fa fa-check"></i>
          <div v-if="editionActive">
            Editar
          </div>
  
          <div v-else>
            Añadir
          </div>
        </button>
  
        <button
            v-if="editionActive"
            type="button" data-dismiss="modal" class="btn btn-danger ml-1"
            @click="clearCurrent"
          ><i class="fa fa-check"></i>
          Cancelar edición
        </button>
      </div>
    </div>
    <div  class="GameBlock" v-else>
      <label for="imageUrl">{{imageRoute}}</label>
      <img id="imageUrl" :src="getImgUrl(imageRoute)"  width="80%" height="80%">
      <button @click="mostrarImagen=false" type="button"  class="btn btn-primary">Cerrar</button>
    </div>
  </template>
  
  <script>
  
  
  export default {
    watch: {
      currentLine: function (val) {
        this.updateLetter();
      },
  
    },
  
    props: {
      chapterId: {
        type: Number,
        default: 0,
      },
  
      currentLine: {
        type: Object,
        default: {},
      },
    },
  
    components: {
    },
  
    data() {
      return {
        words: undefined,
        image: undefined,
        time: undefined,
        title: undefined,
        clue: undefined,
        editionActive: false,
        imageRoute: '',
        mostrarImagen: false,
        pathImage: undefined,
        mensajeActivo:false,
        mensaje:'',
      };
    },
  
    computed: {
  
    },
  
    methods: {
      getImgUrl(nombre) {
          return  this.pathImage + nombre; 
      },
      fileSelected(files) {
        if (!files || files.length === 0) return;
  
        this.image = files[0];
      },
      async checkIfComplete(){
        if(!this.image || this.words === '' || this.time === null || this.title === ''){
          this.mensaje="Debe suministrar la información";
          this.mensajeActivo = true;
        }else{
          if(this.editionActive){
            const data = {
              id: this.currentLine.id,
              words: this.words,
              time: this.time,
              title: this.title, 
              clue: this.clue,
              image: this.image, 
            };
  
            await this.$store.dispatch("adivinaImagenModule/editBlock", data);
          }else{
            const data = {
              id: this.chapterId,
              words: this.words,
              time: this.time,
              title: this.title,
              clue: this.clue,
              image: this.image,
            };
  
            await this.$store.dispatch("adivinaImagenModule/setBlock", data);
          }
  
          this.clearCurrent();
          
          this.$emit('reloadBlock');
        }
      },
  
      clearCurrent(){
        this.words = undefined;
        this.image = undefined;
        this.time = undefined;
        this.title = undefined;
        this.clue = undefined;
        this.editionActive = false;
        this.imageRoute = '';
        this.mensajeActivo=false;
      },
  
      updateLetter(){
        this.editionActive = true;
        this.words = this.currentLine.words;
        this.image = this.currentLine.image;
        this.time = this.currentLine.time;
        this.title = this.currentLine.title;
        this.clue = this.currentLine.clue;
        this.imageRoute = this.currentLine.image;
        this.$refs.image.value = '';
        this.pathImage = this.currentLine.pathImage;
      },
    }
  };
  </script>
  
   <style scoped lang="scss"> 
  .GameBlock {
    padding: 1.5rem;
  
    .game-block{
      height: 30rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  
    .title{
      color: black;
      font-size: 1.5rem
    }
  
    .select-block{
      padding: .5rem;
    }
  
    .question, .response{
      display: flex;
      flex-direction: column;
      margin-bottom: .5rem;
      padding: .5rem;
    }
  
    #words, #time, #clue, #title,  #response{
      border: 1px solid #5ae8e8;
      border-radius: 25px;
    }
  
    #words, #time, #clue, #title{
      height: 2.5rem;
      padding: .5rem;
    }
  
    label {
      text-transform: uppercase;
      font-weight: 500;
      padding: 0 .5rem;
      margin-bottom: .25rem;
      padding: .5rem;
    }
  
    select{
      background-color: #fff;
      border: 1px solid #ffffff;
      border-radius: 50px;
  
      width: 35%;
      height: calc(1.5em + 0.75rem + 2px);
      padding: 0.375rem 0.75rem;
      font-size: 1rem;
      font-weight: 400;
      line-height: 1.5;
      color: #495057;
    }
  
    .options{
      height: 100%;
      flex:2;
  
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
    .labelMensaje{    
      width: 60%;
      padding: .5rem;
      align-content: center;
      color: red;
    }
  }
  </style>
  