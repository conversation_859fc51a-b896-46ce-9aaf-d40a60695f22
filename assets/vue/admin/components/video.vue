<template>
    <div class="VideoContainer">
      <video
        class="video"
        :src="url"
        autoplay
      />
    </div>
  </template>
  
  <script>
  
  export default {
    props: {
      url: {
        type: String,
        required: true,
      },
    },
  
    mounted() {
      const video = this.$el.querySelector('video');
      video.onloadeddata = () => {
        this.$emit('loaded');
      };
    },
  };
  </script>
  
   <style scoped lang="scss"> 
  .VideoContainer {
    .video {
      top: 0;
      left: 0px;
      width: 100%;
    }
  }
  </style>
  