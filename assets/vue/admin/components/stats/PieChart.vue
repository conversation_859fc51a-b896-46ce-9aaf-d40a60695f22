<template>
    <highcharts class="hc" :options="chartOptions" ref="chart"></highcharts>
</template>

<script>

// import Highcharts from "highcharts";
    export default {
        name: "Pie<PERSON><PERSON>",
        components: {},
        props: {
            title: {
                type: String,
                default: "",
            },
            seriesData: {
                type: Array,
                default: () => ([]),
            },
            tooltip: {
                type: String,
                default: '{point.y} ({point.percentage:.1f}%)',
            },
            legend: {
                type: Boolean,
                default: true,
            },
            colors: {
                type: Array,
                default: () => {['var(--color-primary)']},
            },
            innerSize: {
                type: String,
                default: '0%',
            },
            colorToMonochrome: {
                type: String,
                default: undefined,
            },
            height: {

                default: 300,
            }
        },
        data() {
            return {
                chartOptions: {
                    chart: {
                        plotBackgroundColor: null,
                        // plotBorderWidth: null,
                        // plotShadow: false,
                        type: "pie",
                        reflow: true,
                        height: this.height,
                    },
                    credits: {
                        enabled: false,
                    },
                    title: undefined,
                    tooltip: {
                      formatter: function(){
                        return this.key + ': ' + this.y + ' ('+ Math.round(this.percentage) +' %)';
                      }
                    },
                    plotOptions: {
                        pie: {
                            allowPointSelect: true,
                            cursor: "pointer",
                            dataLabels: {
                                enabled: false,
                                format: "<b>{point.name}</b>: {point.percentage:.1f} %",
                            },
                            showInLegend: true,
                            colors: this.colors,
                        },
                    },
                    legend: false,
                    series: [
                        {
                            innerSize: this.innerSize,
                            name: this.title,
                            colorByPoint: true,
                            data: this.seriesData,
                        },
                    ],
                    lang: {
                        noData: this.$t('NO_INFORMATION'),
                    },
                    noData: {
                        style: {
                            fontWeight: 'bold',
                            fontSize: '15px',
                            color: '#303030'
                        }
                    }
                },
            };
        },

        created () {
            if (this.legend) {
                this.chartOptions['legend'] = { labelFormat: "{name}", itemStyle: { fontWeight: "normal"}};
            }

            if (this.tooltip) {
                this.chartOptions['tooltip'] = { pointFormat: this.tooltip }
            }

            if (this.colorToMonochrome) {
                const color = this.isRootColor(this.colorToMonochrome) ? this.getColorFromRootColor(this.colorToMonochrome) : this.colorToMonochrome;
                const hsl = this.isHexColor(color) ? this.hexAToRGBA(color) : this.getHSLParameters(color);

                const colors = this.generateMonochromeColors(hsl);
                this.chartOptions.plotOptions.pie.colors = colors;
                this.$emit('colors', colors);
            } else {
                this.$emit('colors', this.colors);
            }
        },

        methods: {
            isRootColor(color) {
                return /--/.test(color);
            },

            getColorFromRootColor(color) {
                return window.getComputedStyle(document.documentElement).getPropertyValue(color);
            },

            isHexColor(color) {
                return /#/.test(color);
            },

            hexAToRGBA(hex) {
                const hexTrim = hex.trim();
                const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hexTrim);

                let r = parseInt(result[1], 16);
                let g = parseInt(result[2], 16);
                let b = parseInt(result[3], 16);

                r /= 255, g /= 255, b /= 255;
                let max = Math.max(r, g, b), min = Math.min(r, g, b);
                let h, s, l = (max + min) / 2;

                if(max == min){
                    h = s = 0; // achromatic
                } else {
                    let d = max - min;
                    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                    switch(max) {
                        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                        case g: h = (b - r) / d + 2; break;
                        case b: h = (r - g) / d + 4; break;
                    }
                    h /= 6;
                }

                s = s*100;
                s = Math.round(s);
                l = l*100;
                l = Math.round(l);
                h = Math.round(360*h);

                return {h, s, l};
            },

            getHSLParameters(hslColor) {
                let sep = hslColor.trim().indexOf(",") > -1 ? "," : " ";
                const hsl = hslColor.trim().substr(4).split(")")[0].split(sep);

                const h = hsl[0];
                const s = hsl[1].substr(0,hsl[1].length - 1);
                const l = hsl[2].substr(0,hsl[2].length - 1);

                return {h:parseInt(h,10), s: parseFloat(s), l:parseFloat(l)};
            },

            generateMonochromeColors({h, s, l}) {
                const size = this.seriesData.length;
                const scale = (90 - l) / size;

                return [...Array(size).keys()].map( i => {
                    let lightness = (l + scale*i);
                    lightness = lightness > 100 ? 100 : lightness;
                    return `hsl(${h},${s}%,${lightness}%)`
                })
            },
        },
        watch: {
            seriesData: function (newVal, oldVal) {
                // watch it
                this.chartOptions.series[0].data = newVal;
            },
        },
    };
</script>

 <style scoped lang="scss"> 
</style>
