<template>
  <div class="visor-imagen">
    <img :src="`${base}${name}`" alt=""/>
  </div>
</template>

<script>
export default {
  props: {
    name: {
      type: String,
      default: '',
    },
    base: {
      type: String,
      default: "/uploads/material_course/",
    },
  },
};
</script>

 <style scoped lang="scss"> 
.visor-imagen {
  height: 80vh;
  img {
    width: 100%;
    height: 100%;
    aspect-ratio: 1;
    object-fit: contain;
    object-position: center;
  }
}
</style>
