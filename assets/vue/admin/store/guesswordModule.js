import axios from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({
    questions: undefined
});

const state = () => getDefaultState();

export const getters = {
    getQuestions: (state) => () => state.questions,
};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {
   async setBlock(context, request) {
        const url = "/admin/chapter/ordenarmenormayor/set-block";
        const { data } = await axios.post(url, request);
        return data;
    },

    editBlock(context, data) {
        const url = "/admin/chapter/ordenarmenormayor/edit-line";
        return axios.post(url, data);
    },

    async reloadBlock({commit}, request) {
        const url = "/admin/chapter/ordenarmenormayor/reload-block";

        const  result = await axios.post(url, request);
        commit('SET_QUESTIONS', result?.data?.data);
           
        return  result;
    },

    deleteLine(context, data) {
        const url = "/admin/chapter/ordenarmenormayor/delete-line";
        return axios.post(url, data);
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
