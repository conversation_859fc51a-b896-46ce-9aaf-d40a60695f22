import axios from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({
    questions: undefined
});

const state = () => getDefaultState();

export const getters = {
    getQuestions: (state) => () => state.questions,
};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {
    async setBlock(context, { chapterId, text, select, route, time}) {
        const url = "/admin/chapter/trueorfalse/set-block";
        const options = {
            headers: { 'Content-Type': 'multipart/form-data' },
        };

        const formData = new FormData();
        formData.append('chapterId', chapterId);
        formData.append('text', text);
        formData.append('time', time);
        formData.append('select', select);
        formData.append('categorized', false);
        formData.append('route', route);

        const { data } = await axios.post(url, formData, options);
        return data;
    },

    editBlock(context, { id, text, select, route, time}) {
        const url = "/admin/chapter/trueorfalse/edit-line";
        const options = {
            headers: { 'Content-Type': 'multipart/form-data' },
        };

        const formData = new FormData();
        formData.append('id', id);
        formData.append('text', text);
        formData.append('time', time);
        formData.append('select', select);
        formData.append('route', route);

        const { result } = axios.post(url, formData, options);

        return result;
    },

    async reloadBlock({commit}, data) {       
        const url = "/admin/chapter/trueorfalse/reload-block";
         const  result = await axios.post(url, data);
      
        commit('SET_QUESTIONS', result?.data?.data); 

        return await axios.post(url, data);
    },

    deleteLine(context, data) {
        const url = "/admin/chapter/trueorfalse/delete-line";
        return axios.post(url, data);
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
