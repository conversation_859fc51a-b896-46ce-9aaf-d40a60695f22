import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
    chapter: undefined,
    loading: false, // todo add funcionality
});

const state = () => getDefaultState();

export const getters = {
    isLoading: (state) => () => state.loading,
};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {
    async fetchChapter({ commit }, chapter) {
        const url = `/api/contents/${chapter}/get`;
        let contents;

        try {
            commit('SET_LOADING', true);
            const {data} = await axios.get(
                url,
                { headers: { Authorization: 'Bearer ' + localStorage.getItem('token')}}
            );
            chapter = data.data.chapter;
            commit('SET_CHAPTER', chapter);
        } finally {
            commit('SET_LOADING', false);
        }

        return chapter;
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
