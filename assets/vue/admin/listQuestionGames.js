import Vue from 'vue';
import '../../vue/registerBaseComponents';
import ListQuestionGames from './views/ListQuestionGames';
import store from './store';
import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';
import { getI18nApi } from '../common/i18n';


getI18nApi().then((data) => {
  startVueApp(data);
});

Vue.use(VueToast);

function startVueApp({ i18n }) {
  new Vue({
    i18n,
    components: { ListQuestionGames },
    store,
  }).$mount('list-question-games');
}
