<template>
  <div class="StarsGraphics" :class="{loading: isLoading}" :style="{color}">
    <p class="title">
      <i :class="isLoading ? 'fa fa-circle-o-notch fa-spin fa-fw': icon"></i>
      <span class="title-text">{{ isLoading ? '' : $t(title) }}</span>
    </p>
    <div class="text-block"></div>
    <table class="table-stars">
      <tr class="table-title" :style="{borderColor: color}">
        <td class="total">Total de valoraciones: </td>
        <td class="value">{{ isLoading ? '' : formatNumber(total) }}</td>
      </tr>
      <tr v-for="(stars, index) in (isLoading ? 6 : value)">
        <td class="icons">
          <i class="icon fa fa-star" v-for="(i, i2) in (index)"></i>
          <i class="icon fa fa-star-o" v-for="(i, i2) in ((value.length || 6) - index - 1)"></i>
        </td>
        <td class="text-right" v-show="!isLoading">{{ formatNumber(stars) }}</td>
      </tr>
    </table>
  </div>
</template>

<script>

export default {
  name      : "StarsGraphics",
  components: {},
  props     : {
    isLoading: {
      type: Boolean,
      default: true
    },
    icon : {
      type   : String,
      default: "fa fa-user",
    },
    title: {
      type   : String,
      default: "Title",
    },
    value: {
      type   : Array,
      default: () => ([0,0,0,0,0,0]),
    },
    color: {
      type   : String,
      default: "#4DB6AC",
    },
  },
  computed: {
    total() {
      return this.value.reduce((acc, cur) => acc + cur, 0)
    }
  },
  methods: {
    formatNumber(number) {
      if (!number) return 0;
      return number.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
    },
  }
};
</script>

 <style scoped lang="scss"> 
.StarsGraphics {
  width: 100%;
  background-color: white;
  border: 1px solid #E7EBF0;
  box-shadow: 0 0 2px 2px #ECEFF1;
  overflow: hidden;
  border-radius: 7px;
  padding: 0 0 1rem;

  p { margin: 0 auto; }

  .icons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.2rem;
  }

  .total {
    margin: 0.5rem auto;
    font-size: 1rem;
  }

  .value {
    text-align: right;
    font-size: 1.5rem;
    font-weight: bold;
  }

  .table-stars {
    margin: 0 auto;
    .table-title {
      border-width: 0 0 2px;
    }
    tr:nth-child(2) td {
      padding-top: 1rem;
    }
    td {
      padding: 0 0.5rem;
    }
  }

  .title {
    background-color: #E7EBF0;
    color: #37474F;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: bold;
    margin-bottom: 1rem;
  }

  &.loading {
    .text-block, .icon {
      animation: opacityAnimation 1.1s linear infinite alternate;
    }
    .text-block {
      display: block;
      margin: 0 auto;
    }
    .table-title { display: none; }
    .icon { color: #CFD8DC; }
  }
}
</style>
