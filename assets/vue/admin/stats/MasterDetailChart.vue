<template>
  <div class="MasterDetailChart" :key="newKey" :class="{loading: isLoading, hideHeader: hideHeader}">
    <p class="title"><i :class="isLoading ? 'fa fa-circle-o-notch fa-spin fa-fw': icon"></i>
      <span class="title-text">{{ $t(title) }}</span>
    </p>
    <div v-if="isLoading" class="loading-container">
      <i class="fa fa-chart-bar fa-5x"></i>
      <div class="text-block"></div>
      <i class="fa fa-chart-line fa-5x"></i>
      <div class="text-block"></div>
    </div>
    <highcharts v-if="!isLoading" class="hc" :options="detailChartOptions"></highcharts>
    <highcharts v-if="!isLoading" class="hc" :options="masterChartOptions"></highcharts>
  </div>
</template>

<script>

export default {
  name: "MasterDetailChart",
  components: {},
  props: {
    isLoading: {
      type: Boolean,
      default: true
    },
    tag: {
      type: String,
      default: "MasterDetailChart",
    },
    title: {
      type: String,
      default: "Title",
    },
    icon: {
      type: String,
      default: "fa fa-user",
    },
    options: {
      type: Object,
      default: () => ({}),
    },
    footer: {
      type: String,
      default: "",
    },
    pagination: {
      type: Object,
      default: undefined,
    },
    hideHeader: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      data: undefined,
      detailData: undefined,
      timestamp: '',
      millisecondsInADay: 24 * 3600 * 1000
    }
  },
  computed: {
    newKey() { return this.tag + this.timestamp },
    masterChartOptions() {
      this.data = this.options.series
      if (!this.data) return {legend: { enabled: false }, credits: { enabled: false }};
      this.detailData = [...this.data];
      const self = this;
      const base = {
        chart: {
          borderWidth: 0,
          backgroundColor: null,
          marginLeft: 50,
          marginRight: 20,
          marginBottom: 50,
          zoomType: 'x',
          events: {
            selection: function (event) {
              let extremesObject = event.xAxis[0],
                  min = extremesObject.min,
                  max = extremesObject.max,
                  detailData = [],
                  xAxis = this.xAxis[0];
              this.series[0].data.forEach(point => {
                if (point.x > min && point.x < max) {
                  detailData.push([point.x, point.y]);
                }
              });
              xAxis.removePlotBand('mask-before');
              xAxis.addPlotBand({
                id: 'mask-before',
                from: self.data[0][0],
                to: self.data[self.data.length - 1][0],
                color: 'rgba(0, 0, 0, 0.2)'
              });
              xAxis.removePlotBand('mask-after');
              xAxis.addPlotBand({
                id: 'mask-after',
                from: max,
                to: self.options.series[self.options.series.length - 1][0],
                color: 'rgba(0, 0, 0, 0.2)'
              });
              self.detailData = detailData;
              return false;
            }
          }
        },
        accessibility: { enabled: false },
        title: { text: undefined },
        credits: { enabled: false },
        xAxis: { type: 'datetime', crosshair : true },
        yAxis: {
          gridLineWidth: 0,
          labels: { enabled: false },
          title: { text: null },
          min: 0.6,
          showFirstLabel: false
        },
        lang: { noData: this.$t('NO_INFORMATION') },
        noData: { style: { fontWeight: 'bold', fontSize: '15px', color: '#303030' } },
        tooltip: { formatter: function () { return false; } },
        legend: { enabled: false },
        series: [{
          type: 'area',
          name: '',
          color: '#B3E5FC',
          lineColor: '#4FC3F7',
          pointInterval: self.millisecondsInADay,
          pointStart: this.data[0][0],
          data: this.data
        }]
      };
      this.timestamp = (new Date()).getTime();
      return {...base };
    },

    detailChartOptions() {
      if (!this.masterChartOptions.chart) return {legend: { enabled: false }, credits: { enabled: false }};
      const detailData = [],
            detailStart = this.data[0][0];

      this.masterChartOptions.series[0].data.forEach(point => {
        if (point.x >= detailStart) { detailData.push(point.y); }
      });

      return {
        chart: {
          marginTop: 50,
          marginLeft: 50,
          marginRight: 20,
          type: 'column'
        },
        xAxis: { type: 'datetime', title: { text: null }, },
        yAxis: { title: { text: null } },
        // rangeSelector: { enabled: true },
        accessibility: { enabled: false },
        title: { text: undefined },
        credits: { enabled: false },
        exporting: { enabled: false },
        legend: { enabled: false },
        series: [{
          name: '',
          color: '#4FC3F7',
          pointStart: detailStart,
          pointInterval: self.millisecondsInADay,
          data: this.detailData
        }],
      };
    }
  }
};
</script>

 <style scoped lang="scss"> 
.MasterDetailChart {
  width: 100%;
  height: 100%;
  background-color: white;
  border: 1px solid #E7EBF0;
  box-shadow: 0 0 2px 2px #ECEFF1;
  overflow: hidden;
  border-radius: 7px;
  display: grid;
  grid-template-columns: auto;
  grid-template-rows: 2rem auto 10rem;
  gap: 2rem;

  &.hideHeader {
    .title, .loading-container { display: none };
    grid-template-rows: auto 10rem;
  }

  p { margin: 0; }

  .hc { padding: 0 1rem; }

  .title {
    background-color: #E7EBF0;
    color: #37474F;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: bold;
  }

  .footer {
    color: #78909C;
    text-align: center;
    padding: 1rem 2rem;
  }

  &.loading {
    gap: 1.5rem;
    grid-template-rows: 2rem auto;
  }
}
</style>
