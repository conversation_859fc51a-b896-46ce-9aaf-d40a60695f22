<template>
  <div class="Chart" :key="newKey" :class="{withFooter: footer.length || !withoutPagination, loading: isLoading}">
    <div class="title" v-if="showHeader">
      <p>
        <i :class="isLoading ? 'fa fa-circle-o-notch fa-spin fa-fw': icon"></i>
        <span class="title-text">{{ $t(title) }}</span>
      </p>
      <span><i v-if="allowSort" class="fa fa-sort sorting hideOnPrint" @click="showModal"></i></span>
    </div>
    <div v-else></div>
    <div v-if="isLoading" class="loading-container">
      <i class="fa fa-chart-bar fa-5x"></i>
      <div class="text-block"></div>
    </div>
    <highcharts
        v-else
        :constructorType="constructorType"
        class="hc"
        :options="optionsWithPaginatedData"
        :key="newKey"
    ></highcharts>
    <div v-if="withoutPagination && !isLoading && !!(options.footer || footer)" class="footer" v-html="options.footer || footer || ''"></div>
    <chart-pagination v-else-if="!withoutPagination && !isLoading" @current-page="updatePage" :page-size="options.pagination" :total-items="options.size"></chart-pagination>
  </div>
</template>

<script>

import topology   from './topology.json'
import chartPagination from './Pagination'

export default {
  name: "Chart",
  components: { chartPagination },
  props: {
    isLoading: {
      type: Boolean,
      default: true
    },
    tag: {
      type: String,
      default: "Chart",
    },
    title: {
      type: String,
      default: "Title",
    },
    icon: {
      type: String,
      default: "fa fa-user",
    },
    options: {
      type: Object,
      default: () => ({}),
    },
    allowSort: {
      type: Boolean,
      default: false,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    footer: {
      type: String,
      default: "",
    },
  },
  data() {
    return { constructorType: undefined, timestamp: '', currentPage: 0 }
  },
  computed: {
    newKey() { return this.tag + this.timestamp },
    withoutPagination() {
      return (!this.options.pagination || isNaN(this.options.pagination) || this.options.size < this.options.pagination);
    },
    chartOptions() {
      const base = {
        chart: {},
        title: { text: undefined },
        credits: { enabled: false },
        xAxis: { crosshair : true, title: { text: null }, categories: this.options.categories },
        lang: { noData:  this.$t('NO_INFORMATION')},
        noData: { style: { fontWeight: 'bold', fontSize: '15px', color: '#303030' } },
        color: this.options.color || ['#2f7ed8', '#0d233a', '#8bbc21', '#910000', '#1aadce',
          '#492970', '#f28f43', '#77a1e5', '#c42525', '#a6c96a'],
        legend: this.options.hideLegend ? { enabled: false } : {},
        series: this.options.series || [],
      };

      let extraConfig = this[`${this.options.type}Config`] || (() => {});
      this.timestamp = (new Date()).getTime();
      return { ...base, ...extraConfig() };
    },
    optionsWithPaginatedData() {
      const chartOptions = this.chartOptions;
      if (this.withoutPagination) return chartOptions;
      const min = this.currentPage * this.options.pagination;
      const max = Math.min(min + this.options.pagination, this.options.size);

      const xAxis = Array.isArray(chartOptions.xAxis) ? chartOptions.xAxis.map(xAxis => ({
        ...xAxis,
        categories: xAxis.categories.slice(min, max)
      })) : {
        ...chartOptions.xAxis,
        categories: chartOptions.xAxis.categories.slice(min, max)
      };

      return {
        ...chartOptions,
        xAxis,
        series: chartOptions.series.map(serie => ({
          ...serie,
          data: serie.data.slice(min, max)
        }))
      };
    },
  },
  methods: {
    pyramidConfig() {
      return {
        chart: { type: 'bar', ...(this.options.height ? {height: `${this.options.height}px`} : {} ) },
        xAxis: this.options.categories.map((categories, index) => {
          return {
            ...(index ? { linkedTo: 0, opposite: true } : {}),
            categories: this.options.categoriesList,
            reverse: false,
            labels: { step: 1, enabled: !this.options.leftLabelDisabled || index },
            accessibility: { description: categories.description }
          }
        }),
        yAxis: {
          title: { text: null },
          labels: {
            formatter: function () {
              return Math.abs(this.value)
            }
          },
        },
        plotOptions: {
          series: {
            stacking: 'normal'
          }
        },
        tooltip: {
          formatter: this.options.formatter
        }
      };
    },
    barConfig() {
      return {
        chart: { type: 'bar', ...(this.options.height ? {height: `${this.options.height}px`} : {} ) },
        xAxis: { categories: this.options.categories, title: { text: null }, opposite: this.options.opposite || false, },
        yAxis: { title: { text: null }, ...(this.options.hideLabels ? {labels: { enabled: false }} : {} )  },
        tooltip: { formatter: this.options.formatter }
      }
    },
    stackedBarConfig(){
      return {
        chart: { type: 'bar', ...(this.options.height ? {height: `${this.options.height}px`} : {} ) },
        xAxis: { categories: this.options.categories, title: { text: null }, labels: { useHTML: this.options.useHTML } },
        yAxis: { title: { text: null } },
        legend: { reversed: true, ...(this.options.hideLegend ? { enabled: false } : {}) },
        tooltip: { formatter: this.options.formatter },
        plotOptions: {
          series: {
            stacking: 'normal',
            dataLabels: this.options.dataLabels || {
              enabled: false
            }
          }
        },
      }
    },
    columnConfig() {
      return {
        chart: { type: 'column', marginTop: 40, ...(this.options.height ? {height: `${this.options.height}px`} : {} ) },
        yAxis: { min: 0, title: { text: null } },
        xAxis: { title: { text: null }, crosshair: true, categories: this.options.categories},
        plotOptions: { column: { pointPadding: 0.2, borderWidth: 0 } },
        tooltip: {
          formatter: function () {
            return '<b>' + this.series.name + '</b><br/>' + Math.abs(this.point.y);
          }
        },
        series: this.options.series
      }
    },
    donutConfig() {
      return {
        chart: {
          plotBackgroundColor: null,
          plotBorderWidth: null,
          plotShadow: false,
          type: 'pie',
          ...(this.options.height ? {height: `${this.options.height}px`} : {} )
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: { enabled: false },
            showInLegend: true,
            innerSize: '80%',
          }
        },
        tooltip: { pointFormat: this.options.formatter },
        series: [{
          name: '',
          colorByPoint: true,
          data: this.options.series,
        }],
        legend: {
          ...(this.options.hideLegend ? { enabled: false } : {}),
          ...(this.options.legend || {})
        }
      }
    },
    pieConfig() {
      return {
        chart: {
          plotBackgroundColor: null,
          plotBorderWidth: null,
          plotShadow: false,
          type: 'pie',
          ...(this.options.height ? {height: `${this.options.height}px`} : {} )
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: { enabled: false },
            showInLegend: true,
            innerSize: '0%',
          }
        },
        tooltip: { pointFormat: this.options.formatter },
        series: [{
          name: '',
          colorByPoint: true,
          data: this.options.series,
        }],
        legend: {
          ...(this.options.hideLegend ? { enabled: false } : {}),
          ...(this.options.legend || {})
        }
      }
    },
    gaugeConfig() {
      return {
        chart: {
          plotBackgroundColor: null,
          plotBorderWidth: 0,
          plotShadow: false,
          height: 280,
        },
        plotOptions: {
          pie: {
            dataLabels: {
              enabled: true,
              style: {
                fontWeight: 'bold',
                color: 'white'
              }
            },
            startAngle: -90,
            endAngle: 90,
            center: ['50%', '75%'],
            size: '110%'
          }
        },
        series: [{
          type: 'pie',
          name: this.options.name || '',
          innerSize: '50%',
          data: this.options.series
        }]
      }
    },
    mapConfig() {
      this.constructorType = 'mapChart';
      return {
        chart: {
          map: topology,
          ...(this.options.height ? {height: `${this.options.height}px`} : {} )
        },
        legend:{ enabled:false },
        mapView: { fitToGeometry: { type: 'MultiPoint', coordinates: [[-164, 54],[-35, 84],[179, -38],[-68, -55]] } },
        series: [{ name: 'Countries', color: '#E0E0E0', enableMouseTracking: false },
          {
            type   : 'mapbubble',
            name   : this.options.name,
            joinBy : ['iso-a3', 'code3'],
            data   : this.options.series,
            minSize: 4,
            maxSize: '12%',
            tooltip: {
              pointFormat: '<b>{point.countryName}</b>: {point.z} {point.type}'
            }
          }
        ]
      }
    },
    heatmapConfig() {
      const self = this;
      return {
        chart: {
          type: 'heatmap',
          marginTop: 40,
          marginBottom: 80,
          plotBorderWidth: 1,
          height: 50 + '%',
        },
        xAxis: { categories: this.options.categories[0], title: { text: null }, },
        yAxis: { categories: this.options.categories[1], title: null, reversed: true },
        colorAxis: {
          min: 0,
          minColor: '#FFFFFF',
          maxColor: this.options.color
        },
        legend: {
          align: 'right',
          layout: 'vertical',
          margin: 0,
          verticalAlign: 'top',
          y: 25,
          symbolHeight: 280
        },
        tooltip: {
          formatter: function () {
            return '<b>' + self.getPointCategoryName(this.point, 'y') + ' '
                + self.getPointCategoryName(this.point, 'x') + '<br><b>'
                + self.formatNumber(this.point.value) + '</b>';
          }
        },
        plotOptions: {
          series: { dataLabels: { formatter: function () { return '' }}}
        },
        series: [{
          name: '',
          borderWidth: 1,
          data: this.options.series,
          dataLabels: {
            enabled: true,
            color: '#000000'
          }
        }]
      }
    },
    getPointCategoryName(point, dimension) {
      const series = point.series,
          isY = dimension === 'y',
          axis = series[isY ? 'yAxis' : 'xAxis'];
      return axis.categories[point[isY ? 'y' : 'x']];
    },
    updatePage(data) { this.currentPage = data; },
    showModal() {
      this.$emit('show-modal')
    },
    formatNumber(number) {
      if (!number) return 0;
      return number.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
    },
  }
};
</script>

 <style scoped lang="scss"> 
.Chart {
  width: 100%;
  height: 100%;
  background-color: white;
  border: 1px solid #E7EBF0;
  box-shadow: 0 0 2px 2px #ECEFF1;
  overflow: hidden;
  border-radius: 7px;
  display: grid;
  grid-template-columns: auto;
  grid-template-rows: fit-content(100%) minmax(auto, 100%) 0;
  gap: 0;

  &.withFooter {
    grid-template-rows: fit-content(100%) minmax(auto, 100%) fit-content(100%);
  }

  p { margin: 0; }

  .hc { padding: 0 1rem; }

  .title {
    background-color: #E7EBF0;
    color: #37474F;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    font-weight: bold;
  }

  .footer {
    color: #78909C;
    text-align: center;
    padding: 1rem 2rem;
    position: relative;

    ::v-deep {
      .total, .percent {
        position: absolute;
      }
      .total {
        color: #8BC34A;
        font-size: 3rem;
        font-weight: bold;
        inset: -6rem 0 0;
      }
      .percent {
        inset: -2rem 0 0;
      }
    }
  }
}
</style>
