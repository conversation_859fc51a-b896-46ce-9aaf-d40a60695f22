<template>
  <div class="demayormenor">
    <div class="col align-self-end text-right mb-2">
      <button
        type="button"
        class="btn btn-primary"
        data-bs-toggle="modal"
        :data-bs-target="`#modal-chapter-${typeChapter}`"
      >
        {{ translationsVue.add }}
      </button>
    </div>

    <!-- Modal -->
    <div
      class="modal fade"
      :id="`modal-chapter-${typeChapter}`"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="staticBackdropLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="staticBackdropLabel">
              {{ translationsVue.pairs_configureFields_title }}
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              :id="`close-modal-chapter-${typeChapter}`"
            ></button>
          </div>
          <div class="modal-body">
            <NewHigherLower />
          </div>
        </div>
      </div>
    </div>

    <div class="mt-4">
      <table class="table" v-if="higherLowers && higherLowers.length > 0">
        <thead>
          <tr>
            <th scope="col">
              {{ translationsVue.content_configureFields_title }}
            </th>
            <th scope="col">{{ translationsVue.games_words }}</th>
            <th scope="col">{{ translationsVue.games_text_common_time }}</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="lower in higherLowers" :key="lower.id">
            <td>{{ lower.title }}</td>
            <td>{{ lower.higherLowerWords.length }}</td>
            <td>{{ convertSecondToHoursMinutesAndSeconds(lower.time) }}</td>
            <td class="text-right">
              <button
                type="button"
                class="btn btn-primary btn-sm"
                data-bs-toggle="modal"
                :data-bs-target="`#modal-edit-question${lower.id}`"
              >
                <i class="fas fa-edit"></i>
              </button>
              <button
                type="button"
                class="btn-sm btn btn-danger"
                data-bs-toggle="modal"
                :data-bs-target="`#deleteModal${lower.id}`"
              >
                <i class="fas fa-trash-alt"></i>
              </button>
            </td>

            <!-- Modal -->
            <div
              class="modal fade"
              :id="`modal-edit-question${lower.id}`"
              data-bs-backdrop="static"
              data-bs-keyboard="false"
              tabindex="-1"
              aria-labelledby="staticBackdropLabel"
              aria-hidden="true"
            >
              <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="staticBackdropLabel">
                      {{ translationsVue.pairs_configureFields_title }}
                    </h5>
                    <button
                      type="button"
                      class="btn-close"
                      data-bs-dismiss="modal"
                      aria-label="Close"
                    ></button>
                  </div>
                  <div class="modal-body">
                    <EditHigherLower :lower="lower" />
                  </div>
                </div>
              </div>
            </div>

            <BaseModalDelete
              :identifier="`deleteModal${lower.id}`"
              :title="translationsVue.quiz_configureFields_question_delete"
              @delete-element="deleteLower(lower.id)"
            />
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Loader from "../components/Loader";
import NewHigherLower from "../components/ordenar-menormayor/NewHigherLower";
import EditHigherLower from "../components/ordenar-menormayor/EditHigherLower";

import { alertToastMixin } from "../../mixins/alertToastMixin";
import { formatDateMixin } from "../../mixins/formatDateMixin";
import { modalMixin } from "../../mixins/modalMixin";

export default {
  components: {
    Loader,
    NewHigherLower,
    EditHigherLower,
  },

  props: {
    blocks: {
      type: Array,
      default: () => [],
    },
    chapterId: {
      type: Number,
      default: 0,
    },
  },

  mixins: [alertToastMixin, formatDateMixin, modalMixin],

  data() {
    return {
      translationsVue,
      typeChapter,
    };
  },

  computed: {
    ...get("callModule", ["isLoading"]),
    ...get("ordenarMenorMayorModule", ["getHigherLower"]),

    showCalled() {
      return !this.isLoading() && this.called;
    },

    higherLowers() {
      return this.getHigherLower();
    },
  },

  async created() {
    await this.fetchQuestions();
  },

  methods: {
    async fetchQuestions() {
      await this.$store.dispatch(
        "ordenarMenorMayorModule/fetchHigherLower",
        this.chapterId
      );
    },
    async deleteLower(idLower) {
      const formData = new FormData();
      formData.append("id", idLower);
      formData.append("idChapter", this.chapterId);

      await this.$store.dispatch(
        "ordenarMenorMayorModule/deleteHigherLower",
        formData
      );
      this.closeModal(`deleteModal${idLower}`);

      await this.fetchQuestions();
    },
  },
};
</script>

 <style scoped lang="scss"> 
.demayormenor {
  background: #fff;
  padding-top: 2rem;
}
</style>
