<template>
  <div class="questions-games">
    <BaseAlertWarning
      class="alerts-games"
      :message="messageMinimumQuestion"
      v-if="questions && questions.length < minimalQuestion"
    />

    <NewForm ref="newForm" />

    <Questions
      v-if="questions"
      :questions="questions"
      @delete-question="deleteQuestion($event)"
    />
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import { modalMixin } from "../../mixins/modalMixin";
import Questions from "../components/question-games/Questions";
import NewForm from "../components/question-games/NewForm";

export default {
  components: {
    Questions,
    NewForm,
  },

  data() {
    return {
      chapterId,
      messageQuestion,
      translationsVue,
      chapteType,
      messageMinimumQuestion,
      minimalQuestion,
    };
  },

  computed: {
    ...get("questionsGamesModule", ["getQuestions"]),

    questions() {
      return this.getQuestions();
    },
  },

  mixins: [modalMixin],

  async created() {
    await this.questionsGame();
  },

  async mounted() {
    await this.questionsGame();
  },

  methods: {
    async deleteQuestion(id) {
      const body = { idQuestion: id, idChapter: this.chapterId };

      await this.$store.dispatch("questionsGamesModule/deleteQuestion", body);
      this.closeModal(`staticBackdrop${id}`);
      await this.questionsGame();
    },

    async questionsGame() {
      await this.$store.dispatch(
        "questionsGamesModule/fetchQuestionsGames",
        this.chapterId
      );
    },
  },
};
</script>

 <style scoped lang="scss"> 
.questions-games {
  .alerts-games {
    padding-top: 0.5rem;
  }
}

.tooltip-container {
  position: relative;
  display: inline-block;
  color: white;
  width: 1.7rem;
  height: 1.5rem;
  text-align: center;
  border-radius: 0.2rem;
}

.modal-body {
  padding: 0 !important;
}
</style>