<template>
  <div class="RouleteWord">
    <div class="time">
      <label for="title" class="form-label"
        >{{ translationsVue.games_text_common_time }}
      </label>

      <BaseInputTime
        v-model="time"
        :options="['minutes', 'seconds']"
        :maxMinutes="31"
        :time="time"
        @time-update="timeUpdate"
      />
    </div>

    <div class="game-block">
      <div class="container">
        <span v-if="currentLetter" class="current-letter">
          {{ currentLetter.letter }}
        </span>

        <div class="circleContainer">
          <div class="circle">
            <Letter
              v-for="(letter, i) in alphabet"
              :key="i"
              :letter="letter"
              :state="checkStatus(letter)"
              @click.native="loadCurrentLetter(letter)"
            />
          </div>
        </div>
      </div>

      <Options
        v-if="currentLetter"
        class="options"
        :loading="loading"
        :letter="currentLetter"
        @update="updateCurrentLetter"
        @save="saveLetter"
        @delete="deleteLetter"
      />
    </div>
  </div>
</template>

<script>
import { get, sync } from "vuex-pathify";
import Letter from "../components/roulette-word/Letter";
import Options from "../components/roulette-word/Options";

import { formatDateMixin } from "../../mixins/formatDateMixin";

const STORE = "rouletteWordModule";
const ALPHABET = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
const DEFAULT_LETTER = ALPHABET.charAt(0);
const LETTER_STATES = Object.freeze({
  DEFAULT: "default",
  CORRECT: "correct",
  SELECTED: "selected",
});

export default {
  components: {
    Letter,
    Options,
  },

  props: {
    letters: {
      type: Array,
      default: () => [],
    },

    chapterId: {
      type: Number,
      default: undefined,
    },
  },

  mixins: [formatDateMixin],

  computed: {
    ...get(STORE, ["getPreviousLetterAvailable", "getTimeChapter"]),
    savedLetters: sync(`${STORE}/letters`),
    loading: get(`${STORE}/loading`),

    timeFromChapter() {
      return this.getTimeChapter();
    },
  },

  data() {
    const alphabet = ALPHABET.split("");

    return {
      alphabet,
      currentLetter: undefined,
      translationsVue,
      time: "00:00:30",
    };
  },

  async created() {
    this.loadCurrentLetter(DEFAULT_LETTER);
    await this.fetchTimeChapter();
    this.convertTime();
  },

  watch: {
    letters: {
      immediate: true,
      handler(newValue) {
        // * To save letters at store
        this.savedLetters = newValue;
      },
    },
  },

  methods: {
    getLetterTemplate(letter) {
      return {
        letter,
        question: undefined,
        word: undefined,
        state: LETTER_STATES.DEFAULT,
        type: false,
      };
    },

    loadCurrentLetter(letter) {
      const currentLetter = this.findLetter(letter);
      this.currentLetter = currentLetter ?? this.getLetterTemplate(letter);
    },

    async fetchTimeChapter() {
      console.log("fetchTimeChapter");
      return await this.$store.dispatch(
        `${STORE}/fetchTimeChapter`,
        this.chapterId
      );
    },

    convertTime() {
      this.time =
        this.convertSecondToHoursMinutesAndSeconds(this.timeFromChapter) ??
        "00:00:30";
    },

    async saveTimeChapter() {
      const formData = new FormData();
      const secondsTime = this.convertDateHoursMinutesAndSeconds(this.time);
      formData.append("idChapter", this.chapterId);
      formData.append("time", secondsTime);
      await this.$store.dispatch(`${STORE}/saveTimeChapter`, formData);
    },

    findLetter(letter) {
      return this.savedLetters.find((l) => l.letter === letter);
    },

    checkStatus(letter) {
      if (this.currentLetter.letter === letter) {
        return LETTER_STATES.SELECTED;
      }

      const letterSaved = this.findLetter(letter);
      return letterSaved?.word ? LETTER_STATES.CORRECT : LETTER_STATES.DEFAULT;
    },

    updateCurrentLetter(attributes) {
      this.currentLetter = {
        ...this.currentLetter,
        ...attributes,
      };
    },

    async saveLetter() {
      const letter = {
        ...this.currentLetter,
        chapter: this.chapterId,
      };
      await this.$store.dispatch(`${STORE}/setLetter`, letter);

      this.loadCurrentLetter(letter.letter);
    },

    async deleteLetter() {
      const letter = {
        letterId: this.currentLetter.id,
        chapterId: this.chapterId,
      };

      const currentCharacter = this.currentLetter.letter;
      const previousLetter = this.getPreviousLetterAvailable(currentCharacter);
      await this.$store.dispatch(`${STORE}/removeLetter`, letter);

      if (previousLetter) {
        this.loadCurrentLetter(previousLetter);
      } else {
        this.loadCurrentLetter(currentCharacter);
      }
    },

    timeUpdate(time) {
      this.time = time;
      this.saveTimeChapter();
    },
  },
};
</script>

 <style scoped lang="scss"> 
.RouleteWord {
  max-width: 1400px;
  margin: auto;

  .time {
    margin-bottom: 2rem;
  }
  .game-block {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
  }

  .container {
    flex: 0;
    height: 30rem;
    min-width: 30rem;
    position: relative;
    display: grid;
    place-content: center;

    .current-letter {
      font-weight: 500;
      font-size: 4rem;
    }

    .circleContainer .circle {
      position: absolute;
      inset: 0;
      z-index: 2;
      .Letter {
        $s-item: 2.75em;
        $s-circle: $s-item * 11;
        $s-item-mobile: $s-item / 2;
        $s-circle-mobile: $s-item * 9.5;
        $nb-items: 26;

        position: absolute;
        top: calc(50% - #{$s-item-mobile});
        left: calc(50% - #{$s-item-mobile});

        display: block;
        width: $s-item;
        height: $s-item;
        transition: all 0.3s ease-in-out;
        border-radius: 50px;
        color: white;
        font-weight: 600;
        line-height: $s-item;
        text-align: center;

        $rot: 270;
        $angle: (360 / $nb-items);
        @for $i from 1 through $nb-items {
          &:nth-of-type(#{$i}) {
            transform: rotate($rot * 1deg)
              translate($s-circle-mobile / 2)
              rotate($rot * -1deg);
          }

          $rot: $rot + $angle;
        }
      }
    }
  }

  .options {
    min-width: 450px;
    flex: 1;
    display: grid;
  }
}
</style>
