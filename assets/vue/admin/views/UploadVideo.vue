<template>
  <div class="UploadVideo">
    <new-video
      :chapter="chapter"
      :vimeouploadsubdomain="vimeouploadsubdomain"
      :locale="locale"
      :translate="translate"
      v-if="action == 'new'"
    />
    <edit-video
      :chapter="chapter"
      :idvideo="idvideo"
      :vimeouploadsubdomain="vimeouploadsubdomain"
      :locale="locale"
      :translate="translate"
      v-if="action == 'edit'"
    />
  </div>
</template>
<script>
import NewVideo from "../components/upload-video/NewVideo";
import EditVideo from "../components/upload-video/EditVideo";

export default {
  name: "upload-video",
  components: {
    NewVideo,
    EditVideo,
  },

  props: ["chapter", "action", "idvideo", "vimeouploadsubdomain", "locale"],

  data() {
    return {
      translate: undefined,
    };
  },

  async created() {
    const data = await this.$store.dispatch(
      "uploadModule/translateTextComponent"
    );
    this.translate = data.data;
  },
};
</script>
 <style scoped lang="scss"> 
</style>
