import Vue from 'vue';
import store from './store';
import Highcharts from 'highcharts';
import Stock from 'highcharts/modules/stock';
import noData from 'highcharts/modules/no-data-to-display';
import HighchartsVue from 'highcharts-vue';
import './filters/index';

import <PERSON><PERSON><PERSON> from './components/stats/BarChart';
import <PERSON><PERSON><PERSON> from './components/stats/LineChart';
import <PERSON><PERSON><PERSON> from './components/stats/PieChart';
import mySelect from './components/html/select';
import myMultiSelect from './components/html/multiselect';
import inputDate     from "./components/html/input-date";

import '../../css/transitions.scss';

Stock(Highcharts);
noData(Highcharts);

Vue.use(HighchartsVue);

new Vue({
    delimiters: ['${', '}'],
    components: {
        Line<PERSON>hart,
        Pie<PERSON>hart,
        Bar<PERSON>hart,
        mySelect,
        myMultiSelect,
        inputDate,
    },
    store,
    data() {
        return {
            logins: undefined,
            totalDistincLogin: undefined,
            status: undefined,
            usersGender: undefined,
            usersCountry: undefined,
            totalLogin: undefined,
            general: {
                totalUsers: undefined,
                totalCourses: undefined,
                totalAnnouncements: undefined,
                totalChapters: undefined,
                totalQuestions: undefined,
                totalCoursesFinished: undefined,
                totalChaptersFinished: undefined,
                nps: undefined,
                npsAVG: undefined,
                ratioCourses : undefined,
                rankingCourses: undefined
            },
            chapterTypes: undefined,
            chapterTypesColored: undefined,
            finishedChapterTypes: undefined,
            finishedCourses: undefined,
            finishedChapters: undefined,
            mergedChapters: undefined,
            timeSpentByType: undefined,
            dailyPosts: [],
            countries: [],
            courses: [],
            centers: [],
            professionalCategories: [],
            departaments: [],
            genders:[],
            divisions: [],
            filters: {},
            bd_filters: {},
            showFilters: false,
            divicesSesion:[],
            allDevicesSesion: 0,
            distributionAgeUsers:[],
            usersWithAtLeastOneCourseFinished: undefined,
            filterCategories: [],
            ischartCountry: false,
            isChartAge: false,
            isChartGender: false,
            loadingData: false,
        }
    },

    async created() {
        // set default filters and this.loadData beacause watch
        this.filters = this.defaultFilters();
        this.bd_filters = this.defaultBdFilters();

        this.countries = typeof countries !== 'undefined' ? countries : [];
        this.courses = typeof courses !== 'undefined' ? courses : [];
        this.centers = typeof centers !== 'undefined' ? centers : [];
        this.professionalCategories = typeof professionalCategories !== 'undefined' ? professionalCategories : [];
        this.departaments = typeof departaments !== 'undefined' ? departaments : [];
        this.genders = typeof genders !== 'undefined' ? genders : [];
        this.totalLogin = typeof totalLogin !== 'undefined' ? totalLogin : [];
        this.divisions = typeof divisions !== 'undefined' ? divisions : [];
        this.divisionCountries = typeof divisionCountries !== 'undefined' ? divisionCountries : [];
        this.divicesSesion = typeof divicesSesion !== 'undefined' ? divicesSesion : [];
        this.calculateAllDevices(this.divicesSesion);
        this.distributionAgeUsers = typeof distributionAgeUsers !== 'undefined' ? distributionAgeUsers : [];
        this.usersWithAtLeastOneCourseFinished = typeof usersWithAtLeastOneCourseFinished !== 'undefined' ? usersWithAtLeastOneCourseFinished : [];

        this.filterCategories = typeof filterCategories !== 'undefined' ? filterCategories : [];
        this.ischartCountry = typeof chartCountry !== 'undefined' ? chartCountry : false;
        this.isChartAge = typeof chartAge !== 'undefined' ? chartAge : false;
        this.isChartGender = typeof chartGender !== 'undefined' ? chartGender : false;


        await this.loadData();
    },

    methods: {
        calculateAllDevices(divicesSesion){
            let total = 0;
            divicesSesion.forEach(item =>  total = total + item.y);
            this.allDevicesSesion = total;
        },

        calculatedPercent(device){
            const element = this.divicesSesion.filter(item => item.name === device);

            if(element.length > 0){
                return (element[0].y*100)/this.allDevicesSesion;
            }
            return  0;
        },

        async loadData() {
            if(!this.loadingData) {
                this.loadingData = true;
                const response = await this.dataCall();
                this.loadingData = false;
            }
        },
        async dataCall() {
            let filters = this.filters;
            filters['filters'] = this.bdFilterIds;

            await this.$store.dispatch('generalStatsModule/fetchUsersGender', {filters})
                .then(status => {
                    this.usersGender = status;
                }).catch((error) => { console.log(error); });
            if(this.chartGender){ this.usersGender = await this.$store.dispatch('generalStatsModule/fetchUsersGender', {filters});}
            if(this.ischartCountry){ this.usersCountry = await this.$store.dispatch('generalStatsModule/fetchUsersCountry', {filters})}
            this.general = await this.$store.dispatch('generalStatsModule/fetchGeneralStats', {filters});

            this.chapterTypes = await this.$store.dispatch('generalStatsModule/fetchChapterTypes',{filters});
            this.finishedChapterTypes = await this.$store.dispatch('generalStatsModule/fetchFinishedChapterTypes',{filters});
            this.loadMergedChapters();

            this.timeSpentByType = await this.$store.dispatch('generalStatsModule/fetchTimeSpentByType', {filters});
            this.divicesSesion =  await this.$store.dispatch('generalStatsModule/fetchDevicesSesion', {filters});
            this.totalLogin =  await this.$store.dispatch('generalStatsModule/fetchLoginSesion', {filters});
            this.totalDistincLogin =  await this.$store.dispatch('generalStatsModule/fetchLoginDistinctSesion', {filters});
            this.calculateAllDevices(this.divicesSesion);

            this.usersWithAtLeastOneCourseFinished =  await this.$store.dispatch('generalStatsModule/fetchUsersWithAtLeastOneCourseFinished', {filters});
            if(this.isChartAge) {this.distributionAgeUsers =  await this.$store.dispatch('generalStatsModule/fetchDistributionUserAge', {filters})}

            await this.loadFinishedCourses();
            await this.loadFinishedChapters();

            await this.$store.dispatch('generalStatsModule/fetchUserLogins', {filters})
                .then(logins => {
                    this.logins = [];
                    if (!logins.length) return;

                    let start = Date.parse(logins[0].date);
                    let today = Date.now();
                    let days = {};

                    for (let i = 0; i < logins.length; i++) {
                        days[logins[i].date] = parseInt(logins[i].count);
                    }

                    for (let i = start; i < today; i = i + 86400000) {
                        let date = new Date(i);
                        let day = date.getFullYear()
                            + '-'
                            + (date.getMonth() < 9 ? '0' : '')
                            + (date.getMonth() + 1)
                            + '-'
                            + (date.getDate() < 10 ? '0' : '')
                            + date.getDate();

                        this.logins.push([i, (day in days) ? days[day] : 0]);
                    }
                }).catch((error) => { console.log(error); });

            this.status = await this.$store.dispatch('generalStatsModule/fetchUsersStatus', {filters});

            await this.$store.dispatch('generalStatsModule/fetchDailyPosts')
                .then(dailyPosts => {
                    this.dailyPosts = this.parseDailyResponse(dailyPosts);
                });
        },
        loadMergedChapters() {
            const finishedChapterTypesObject = this.finishedChapterTypes.reduce((acc, chapter) => {
                acc[chapter.name] = chapter;
                return acc;
            }, {});

            this.mergedChapters = this.chapterTypes.map(chapter => ({...chapter, done: finishedChapterTypesObject[chapter.name]?.y ?? 0 }));
        },

        async loadFinishedChapters() {
            let filters = this.filters;

            await this.$store.dispatch('generalStatsModule/fetchFinishedChapters', {filters})
                .then(finishedChapters => {
                    this.finishedChapters = [];
                    if (!finishedChapters.length) return;

                    let start = Date.parse(finishedChapters[0].date);
                    let today = Date.now();
                    let days = {};

                    for (let i = 0; i < finishedChapters.length; i++) {
                        days[finishedChapters[i].date] = parseInt(finishedChapters[i].count);
                    }

                    for (let i = start; i < today; i = i + 86400000) {
                        let date = new Date(i);
                        let day = date.getFullYear()
                            + '-'
                            + (date.getMonth() < 9 ? '0' : '')
                            + (date.getMonth() + 1)
                            + '-'
                            + (date.getDate() < 10 ? '0' : '')
                            + date.getDate();

                        this.finishedChapters.push([i, (day in days) ? days[day] : 0]);
                    }
                }).catch((error) => { console.log(error); });
        },

        async loadFinishedCourses() {
            let filters = this.filters;

            await this.$store.dispatch('generalStatsModule/fetchFinishedCourses', {filters})
                .then(finishedCourses => {
                    this.finishedCourses = [];
                    if (!finishedCourses.length) return;

                    let start = Date.parse(finishedCourses[0].date);
                    let today = Date.now();
                    let days = {};

                    for (let i = 0; i < finishedCourses.length; i++) {
                        days[finishedCourses[i].date] = parseInt(finishedCourses[i].count);
                    }

                    for (let i = start; i < today; i = i + 86400000) {
                        let date = new Date(i);
                        let day = date.getFullYear()
                            + '-'
                            + (date.getMonth() < 9 ? '0' : '')
                            + (date.getMonth() + 1)
                            + '-'
                            + (date.getDate() < 10 ? '0' : '')
                            + date.getDate();

                        this.finishedCourses.push([i, (day in days) ? days[day] : 0]);
                    }
                }).catch((error) => { console.log(error); });
        },

        secondsToHms(d) {
            d = Number(d);
            let h = Math.floor(d / 3600);
            let m = Math.floor(d % 3600 / 60);
            let s = Math.floor(d % 3600 % 60);

            let hDisplay = h > 0 ? h + (h === 1 ? ' hour, ' : ' hours, ') : "";
            let mDisplay = m > 0 ? m + (m === 1 ? ' minute, ' : ' minutes, ') : "";
            //var sDisplay = s > 0 ? s + (s == 1 ? " second" : " seconds") : "";
            return hDisplay + mDisplay;
        },

        async applyFilters() {
            //this.showFilters = false;
            await this.loadData();
        },

        async clearFilters() {
            this.filters = this.defaultFilters();
            this.bd_filters = this.defaultBdFilters();
            //this.showFilters = false;
            await this.loadData();
        },

        toggleFilterDisplay() {
            this.showFilters = !this.showFilters;
        },

        resetValues() {
            this.general = {
                totalUsers: undefined,
                totalCourses: undefined,
                totalAnnouncements: undefined,
                totalChapters: undefined,
                totalQuestions: undefined,
                totalCoursesFinished: undefined,
                totalChaptersFinished: undefined,
                nps: undefined,
                npsAVG: undefined,
                ratioCourses : undefined
            };
            this.usersGender = undefined;
            this.divicesSesion = undefined;
            this.totalLogin = undefined;
            this.usersWithAtLeastOneCourseFinished = undefined;
            this.usersCountry = undefined;
            this.chapterTypes = undefined;
            this.finishedChapterTypes = undefined;
            this.timeSpentByType = undefined;
            this.logins = undefined;
            this.totalDistincLogin = undefined;
            this.finishedChapters = undefined;
            this.finishedCourses = undefined;
            this.status = undefined;
        },

        defaultFilters() {
            let filters = {
                country: '',
                center: '',
                category: [],
                departament: '',
                gender:'',
                division:'',
                dateFrom:'',
                dateTo:''
            }

            return filters;
        },

        defaultBdFilters() {
            let bd_filters = {};
            filterCategories.forEach(element => {
                bd_filters['category_' + element.id] = [];
            });

            return bd_filters;
        },

        async removeFilter(filter) {
            this.filters[filter] = '';
        },

        async removeBdFilter(categoryName) {
            filterCategories.forEach(element => {
                if(element.name == categoryName) this.bd_filters['category_' + element.id] = [];
            });
        },

        async removeCategory(category_id) {
            this.filters['category'] = this.filters['category'].filter(filter_id => filter_id !== category_id) || [];
        },

        getSecondsFormatted(currentSeconds) {
            // const days =  Math.floor(currentSeconds / (60*60*24));
            // const daysRest =  Math.floor(currentSeconds % (60*60*24));

            const days = 0;
            const daysRest = currentSeconds;

            const hours = Math.floor(daysRest / 3600);
            const hoursRest = Math.floor(daysRest % 3600);

            const minutes = Math.floor(hoursRest / 60);
            const seconds = Math.floor(hoursRest % 60);

            return { days, hours, minutes, seconds };
        },

        npsFormatted(nps) {
            const percentage = Math.round(nps * 100);

            return percentage + '%'
        },

        parseDailyResponse(data) {
            if (!data.length) return [];

            let start = Date.parse(data[0].date);
            let today = Date.now();
            let days = {};

            for (let i = 0; i < data.length; i++) {
                days[data[i].date] = parseInt(data[i].count);
            }
            let dailyData = [];
            for (let i = start; i < today; i = i + 86400000) {
                let date = new Date(i);
                let day = date.getFullYear() + '-' +
                    +(date.getMonth() < 10 ? '0' : '')
                    + (date.getMonth() + 1)
                    + '-'
                    + (date.getDate() < 10 ? '0' : '')
                    + date.getDate();

                dailyData.push([i, (day in days) ? days[day] : 0]);
            }

            return dailyData;
        },

        chapterTypesSetColors(colors) {
            console.log(colors);
            this.chapterTypesColored = this.mergedChapters.map((chapterTypes, i) => ({...chapterTypes, color: colors[i]}));
        },

        searchFilterInCategory (category, filter_id) {

            // let foundFilter = null;
            // category.filters.forEach(filter => {
            //    if(filter.id == filter_id) {
            //        foundFilter = filter;
            //        return;
            //    }
            // });

            return category.filters.find(filter => filter.id == filter_id)

            // return foundFilter;
        },
    },

    computed: {
        colors() {
            return [...Array(5).keys()].map(i => `var(--color-dashboard-${++i})`);
        },

        totalTime() {
            return this.timeSpentByType?.reduce((previous, current) => previous + current.y, 0);
        },

        formattedTotalTime() {
            if (!this.timeSpentByType) return undefined;

            return this.getSecondsFormatted(this.totalTime);
        },

        formattedTotalHours() {
            if (!this.timeSpentByType) return undefined;

            // return Math.round(this.totalTime / 3600);
            return this.getSecondsFormatted(this.totalTime);
        },

        formattedTotalTimeByUser() {
            if (!this.timeSpentByType || !this.general.totalUsers || this.general.totalUsers == 0) return undefined;

            return this.getSecondsFormatted(this.totalTime / this.general.totalUsers);
        },

        currentFilters() {
            let filters = {}

            if (this.filters['country']) {
                filters['country'] = this.countries.find(country => {
                    return country.id === this.filters['country'];
                });
            }

            if (this.filters['course']) {
                filters['course'] = this.courses.find(course => {
                    return course.id === this.filters['course'];
                });
            }

            if (this.filters['center']) {
                filters['center'] = this.centers.find(center => {
                    return center.id === this.filters['center'];
                });
            }

            if (this.filters['category']?.length) {
                filters['category'] = this.filters['category'].map(ids => this.professionalCategories.find(pc => pc.id === ids)).filter(pc => pc?.id);
            }

            if (this.filters['departament']) {
                filters['departament'] = this.departaments.find(departament => {
                    return departament.id === this.filters['departament'];
                });
            }

            if (this.filters['gender']) {
                filters['gender'] = this.genders.find(gender => {
                    return gender.id === this.filters['gender'];
                });
            }

            if (this.filters['division']) {
                filters['division'] = this.divisions.find(division => {
                    return division.id === this.filters['division'];
                });
            }

            if (this.filters['dateFrom'])
                filters['dateFrom'] = {name: `Desde: ${this.filters['dateFrom']}`};

            if (this.filters['dateTo'])
                filters['dateTo'] = {name: `Hasta: ${this.filters['dateTo']}`};

            if (this.filters['division']) {
                filters['division'] = this.divisions.find(division => {
                    return division.id === this.filters['division'];
                });
            }

            return filters;
        },

        currentBdFilters () {
            let currentBdFilters = {};
            filterCategories.forEach(element => {
                if(Object.keys(this.bd_filters['category_' + element.id]).length != 0) {
                    currentBdFilters[element.name] = [];
                    this.bd_filters['category_' + element.id].forEach(filter_id => {
                        const filter = this.searchFilterInCategory(element, filter_id);
                        currentBdFilters[element.name].push(filter.name);
                    });
                }
            });
            return currentBdFilters;
        },

        bdFilterIds () {
            let bdFiltersIds = [];
            filterCategories.forEach(element => {
                if(Object.keys(this.bd_filters['category_' + element.id]).length != 0) {
                    this.bd_filters['category_' + element.id].forEach(filter_id => {
                        bdFiltersIds.push(filter_id);
                    });
                }
            });
            return bdFiltersIds;
        },

        filteredCountries() {
            if(Object.entries(this.divisionCountries).length) {
                if(this.currentFilters['division']){
                    const divisionCountries = this.divisionCountries[this.currentFilters['division'].id];
                    let newList = this.countries.filter(country => {
                        return divisionCountries.some(divisionCountry => divisionCountry === country.id)
                    });
                    if (this.currentFilters['country']?.id && !newList.find(nl => nl.id === this.currentFilters['country'].id))
                        this.filters['country'] = '';
                    return newList;
                }
            }
            return this.countries;
        },
    },

    filters: {
        formatNumber: (number) => {
            if (!number) return 0;

            return number.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
        }
    },
}).$mount('#general-stats')
