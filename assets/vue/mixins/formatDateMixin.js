export const formatDateMixin = {
	methods: {
        convertSecondToHoursMinutesAndSeconds(timeInSeconds) {
            const hours = Math.floor(timeInSeconds / 3600);
            const minutes = Math.floor((timeInSeconds - hours * 3600) / 60);
            const seconds = Math.floor(timeInSeconds - hours * 3600 - minutes * 60);
      
            const minutesFormat = minutes < 10 ? "0" + minutes : minutes;
            const secondsFormat = seconds < 10 ? "0" + seconds : seconds;
            const hoursFormat = hours < 10 ? "0" + hours : hours;
      
            return hoursFormat + ":" + minutesFormat + ":" + secondsFormat;
          },

          convertDateHoursMinutesAndSeconds(time) {
            const seconds = time.split(":");
            const secondsTime = +seconds[0] * 60 * 60 + +seconds[1] * 60 + +seconds[2];

            return secondsTime;
          },

          formattedDateTime(dateString) {                   
            const [date, time] = dateString.split(" ");
            const [year, month, day] = date.split("-");
            const [hour, minute, second] = time.split(":");
            return `${year}-${month}-${day}T${hour}:${minute}`;
          },
	},
};
