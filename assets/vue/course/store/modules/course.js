import axios from "axios";
import {make} from "vuex-pathify";

const state = {
    loading: true,
    config: {
        defaultFilterStatus: []
    }
};

export default {
    namespaced: true,
    state,
    getters: {
        ...make.getters(state),
    },
    mutations: {
        ...make.mutations(state),
    },
    actions: {
        ...make.actions('config'),
        async loadCourse({ commit }, id) {
            try {
                const url = `/admin/course/${ id }`;
                const result = await axios.get(url);
                return result.data;
            } finally {

            }
        },
        async loadPreData({ commit }) {
            commit('SET_LOADING', true);
            try {
                const result = await axios.get('/admin/course/pre-data')
                return result.data;
            } finally {
                commit('SET_LOADING', false);
            }
        },
        async createCourse({ commit }, formData) {
            try {
                const headers = {
                    'Content-Type': 'multipart/form-data'
                };
                const result = await axios.post('/admin/course', formData, { headers });
                return result.data;
             } finally {

            }
        },
        async updateCourse({ commit }, {id, formData }) {
            try {
                const headers = {
                    'Content-Type': 'multipart/form-data'
                };
                const url = `/admin/course/update/${ id }`;
                const result = await axios.post(url, formData, { headers });
                return result.data;
            } finally {

            }
        },

        async getCourseFilters({ commit }, id) {
            try {
                const result = await axios.get(`/admin/course/${id}/filters`);
                return result.data;
            } finally {

            }
        },

        async getManagers({ commit }, id) {
            const result = await axios.get(`/admin/course/${id}/managers`);
            return result.data;
        },

        saveCourseFilters({}, {id, filtersIds}) {
            return axios.post('/admin/course/save-filters', {
                id, filters: filtersIds
            }).then(r => (r.data))
                .catch(e => ({error: true, data: 'Failed to make request', trace: e}));
        },

        saveCourseManagers({}, { id, managers }) {
            return axios.post('/admin/course/save-course-managers', {
                id,
                managers
            }).then(r => (r.data))
                .catch(e => ({ error: true, data: 'Failed to make request', trace: e}));
        },

        getProfessionalCategories() {
            return axios.get("/admin/professional-categories")
                .then(r => (r.data))
                .catch(e => ({ error: true, data: 'Failed to make request', trace: e}));
        },

        getCourseProfessionalCategories({}, id) {
            return axios.get(`/admin/course/${id}/professional-categories`)
                .then(r => (r.data))
                .catch(e => ({ error: true, data: 'Failed to make request', trace: e}))
        },

        saveCourseProfessionalCategories({}, { id, categories }) {
            return axios.post('/admin/course/save-professional-categories', {
                id, categories
            }).then(r => (r.data))
                .catch(e => ({ error: true, data: "Failed to make request", trace: e}));
        }
    }
}
