import {make} from "vuex-pathify";
import axios from "axios";

const state = {
    loading: true,
    loadingCourses: true,
    categories: [],
    courses: [],// Used by form and view
    category: {
        category: null,
        translations: []
    },
    form: {
        id: -1,
        name: '',
        description: '',
        orderType: 'auto',
        orderProperties: {
            showNewAtStart: false,
            orderCriteria: 'createdAt'
        },
        translations: [],
        typeCourses: {},
        categories: []
    }
};

export const mutations = {
    ...make.mutations(state),
};
export const getters = {
    ...make.getters(state),
};
export const actions = {
    getCategories({ commit }) {
        commit('SET_LOADING', true);
        axios.get("/admin/course-categories/all").then(r => {
            const { data } = r.data;
            commit('SET_CATEGORIES', data.data);
        }).catch(e => {
            console.log(e);
        }).finally(() => {
            commit('SET_LOADING', false);
        });
    },
    loadCategory({ commit, getters }, id) {
        const { categories } = getters;
        if (categories.length > 0) {
            const category = categories.find(c => c.id === id);
            if (category) commit('SET_CATEGORY', category);
        }

        axios.get(`/admin/course-categories/${id}`).then(r => {
            const { data, error } = r.data;
            console.log('loadCategory',data)
            const { category, translations } = data;
            commit('SET_CATEGORY', {category, translations});
        })
    },

    async initForm({ commit, dispatch }, { id = -1, locales = {}}) {
        commit('SET_LOADING', true);
        let translations = [];
        let orderType = 'auto';
        let orderProperties = {
            showNewAtStart: false,
            orderCriteria: 'createdAt'
        };
        let typeCourses = {};

        const keys = Object.keys(locales);
        if (id > 0) {
            const result = await axios.get(`/admin/course-categories/${id}`);
            const { data, error } = result.data;
            
            // const { category, translations } = data;
            // commit('SET_CATEGORY', {category, translations});
            if (data.translations && data.translations.length > 0) {
                translations = data.translations;
            }
            keys.forEach(k => {
                if (!translations.find(t => t.locale === k)) {
                    // not found, add
                    translations.push({ locale: k, name: '', description: ''});
                }
            });
            orderType = data.orderType;
            orderProperties = data.orderProperties;
            typeCourses = data.typeCourses;
        } else {
            keys.forEach(k => {
                translations.push({ locale: k, name: '', description: ''});
            });

        }

        commit('SET_FORM', {
            id,
            name: '',
            description: '',
            translations,
            categories: [],
            orderType,
            orderProperties,
            typeCourses
        })
        commit('SET_LOADING', false);
    },

    getCoursesOrder({ commit, getters }) {
        const { form } = getters;
        if (form.id < 1) return;

        commit('SET_LOADING_COURSES', true);
        axios.get(`/admin/course-categories/${form.id}/courses-to-order`).then(r => {
            const { data } = r.data;
            commit('SET_COURSES', data);
        }).finally(() => {
            commit('SET_LOADING_COURSES', false);
        });
    },

    /**
     *
     * @param getters
     * @param rootGetters
     * @return {*|Promise<axios.AxiosResponse<any> | {data: string, e: *, error: boolean}>}
     */
    save({ getters, rootGetters }) {
        // Generate course id/orders
        const { courses, form } = getters;
        const defaultLocale = rootGetters['localeModule/defaultLocale'];
        const locales = rootGetters['localeModule/locales'];
        const userLocale = rootGetters['localeModule/userLocale'];

        // Find default translation
        let errorDefault = true;
        form.translations.forEach(t => {
            if (t.locale === userLocale) {//debo validar el locale actual del administrador
                errorDefault = !t.name || t.name.length < 1;
                form.name = t.name;
                form.description = t.description;
            }
        })
        if (errorDefault) return Promise.reject("Name in locale [" + locales[userLocale] + "] is required");

        const coursesOrder = {};
        courses.forEach((course, index) => {
            coursesOrder[course.id + ''] = index + 1;
        });

        const data = {...form, coursesOrder};
        const headers = {'Content-Type': 'application/json'};

        if(form.id < 1) {//**** está fallando la creación */
            return axios.post(`/admin/api/v1/course-categories`, data, { headers }).then(r => (r.data))
                .catch(e => ({error: true, data: 'Failed to make request', e}));
        } else {
            return axios.put(`/admin/course-categories/update`, data, { headers }).then(r => (r.data))
                .catch(e => ({error: true, data: 'Failed to make request', e}));
        }
    },

    async deleteCategory({}, id) {
        try {
            const result = await axios.delete(`/admin/course-categories/${id}/delete`);
            const { error, data } = result.data;
            if (error) return Promise.reject(data);
            return Promise.resolve();
        } catch (e) {
            if(e.response.data.status == 409) return Promise.reject(e.response.data.status);
            else return Promise.reject("Failed to make request");
        }
    },

    updateCategoriesOrder({ getters }) {
        ///update-order
        const { categories } = getters;
        let order = {};
        categories.forEach((cat, index) => {
            order[cat.id] = index + 1;
        });

        return axios.post('/admin/course-categories/update-order', order).then(r => (r.data))
            .catch(e => ({ error: true, data: 'Fail to make request'}));
    },

    async updateCategoryActive({ }, id) {
        try {
            const result = await axios.post(`/admin/course-categories/update-active/${id}`);
            const { error, data } = result.data;
            if (error) return Promise.reject(data);
            return Promise.resolve();
        } catch (e) {
            return Promise.reject("Failed to make request");
        }
    }

};

export default {
    namespaced: true,
    state,
    mutations,
    getters,
    actions
};
