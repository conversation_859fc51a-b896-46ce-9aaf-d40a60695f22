<script>
import {get} from "vuex-pathify";

export default {
  name: "CourseCategoryView",
  data() {
    return {
      activeNav: 'INFO'
    };
  },
  computed: {
    id() {
      return this.$route.params.id;
    },
    routeName() {
      return this.$route.name;
    },
    category: get('courseCategoryModule/category@category')
  },
  created() {
    this.$store.dispatch('courseCategoryModule/loadCategory', this.id);
  }
}
</script>

<template>
  <div class="w-100">
    <div class="info p-3">
      <h4 class="w-100 title"><i class="fa fa-sitemap"></i> {{ category?.name }}</h4>
    </div>
    <div class="w-100">
      <ul class="nav nav-tabs ps-4 user-select-none hideOnPrint">
        <li class="nav-item" role="presentation">
          <router-link
              :to="{name: 'View', params: {id: id}}"
              class="nav-link"
              :class="routeName === 'View' ? 'active' : ''"
              id="info-tab"
          >
            <i class="fa fa-sitemap"></i>
            Information
          </router-link>
        </li>
        <li class="nav-item" role="presentation">
          <router-link
              :to="{name: 'ViewTranslations', params: {id: id}}"
              class="nav-link"
              :class="routeName === 'ViewTranslations' ? 'active' : ''"
              id="info-tab"
          >
            <i class="fa fa-language"></i>
            Translations
          </router-link>
        </li>
        <li class="nav-item" role="presentation">
          <router-link
              :to="{name: 'ViewOrder', params: {id: id}}"
              class="nav-link"
              :class="routeName === 'ViewOrder' ? 'active' : ''"
              id="info-tab"
          >
            <i class="fa fa-sort"></i>
            Order
          </router-link>
        </li>
      </ul>
    </div>
    <div class="tab-content bg-white p-0 pt-1 pb-1">
      <div class="tab-pane active">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
h4.title {
  font-size: 18px;
}
</style>
