<script>
import StepForm from "../../common/views/StepForm.vue";
import {get, sync} from "vuex-pathify";
import Spinner from "../../base/BaseSpinner.vue";

export default {
  name: "FormView",
  components: {Spinner, StepForm},
  data() {
    return {
      steps: {
        current: 1,
        total: 3
      }
    };
  },
  computed: {
    locales: get('localeModule/locales'),
    loading: sync('courseCategoryModule/loading'),
    form: sync('courseCategoryModule/form'), 
    routeName() {
      return this.$route.name;
    },
    id() {
      return this.$route.params.id ?? -1;
    }
  },
  created() {
    this.$store.dispatch('courseCategoryModule/initForm', {id: this.id, locales: this.locales});
  },
  mounted() {
    this.$eventBus.$on("onSave", (e) => {
      this.save();
    });
    this.$eventBus.$on("onDelete", (e) => {
      this.remove();
    });
  },
  beforeDestroy() {
    this.$eventBus.$off("onSave");
    this.$eventBus.$off("onDelete");
  },
  methods: {
    save() {
      this.$store.dispatch('courseCategoryModule/save').then(r => {
        this.$toast.success(this.$t('CATALOG.SAVED') + '');
        this.$router.push({ name: 'Home'});
      }).catch(e => {
        this.$toast.error(e);
      });
    },
    remove() {
      this.$alertify.confirmWithTitle(
          this.$t('DELETE'),
          this.$t('COMMON_AREAS.QUESTION_DELETE'),
          () => {
            this.$store.dispatch('courseCategoryModule/deleteCategory', this.id).then(r => {
              this.$toast.success(this.$t('DELETE_SUCCESS') + ''); 
              this.$router.push({ name: 'Home'});
            }).catch(e => {
              if(e==409) this.$toast.error((this.$t('COURSE.COURSE_CATEGORY.DELETE_ERROR1') + ''));
              else this.$toast.error(e);
            })
          },
          () => {},
      )
    }
  }
}
</script>

<template>
  <div class="d-flex w-100 align-items-center justify-content-center" v-if="loading">
    <spinner/>
  </div>
  <div class="FormView" v-else>
    <div class="w-100 d-flex flex-row Header">
      <h4>{{ $t('COURSE_CATEGORY.LABEL.DETAIL') }}</h4>
    </div>
    <div class="w-100">
      <ul class="nav nav-tabs ps-4 user-select-none hideOnPrint">
        <li class="nav-item" role="presentation">
          <router-link
              :to="{name: 'CreateView', params: {id: id}}"
              class="nav-link"
              :class="routeName === 'CreateView' ? 'active' : ''"
              id="info-tab"
          >
            <i class="fa fa-sitemap"></i>
            {{ $t('COMMON.BASIC_INFO') }}
          </router-link>
        </li>
        <li class="nav-item" role="presentation">
          <router-link
              :to="{name: 'CreateViewOrder', params: {id: id}}"
              class="nav-link"
              :class="routeName === 'CreateViewOrder' ? 'active' : ''"
              id="info-tab"
          >
            <i class="fa fa-sort"></i>
            {{ $t('COURSE_CATEGORY.LABEL.SORT') }}
          </router-link>
        </li>
      </ul>
    </div>
    <div class="tab-content bg-white p-0 pt-1 pb-1">
      <div class="tab-pane active">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.FormView {
  .Header {
    padding: 1rem;
    & > h4 {
      font-size: 22px;
      color: var(--color-neutral-darkest);
    }
  }

  .tab-pane {
    padding: 1rem;
  }
}
</style>
