import announcementFormModule from '../../../../announcement/store/module/announcementForm/module';
import announcementModule            from "../../../../announcement/store/module/announcementModule";
import announcementObservationModule from "../../../../announcement/store/module/announcementObservationModule";
import announcementTaskModule        from "../../../../announcement/store/module/announcementTaskModule";
import forumModule                   from "../../../../announcement/store/module/forumModule";
import chatModule                    from "../../../../announcement/store/module/chatModule";
import notificationModule            from "../../../../announcement/store/module/notificationModule";
import materialCourseModule          from "../../../../common/store/modules/materialCourseModule";
import taskCourseModule              from "../../../../common/store/modules/taskCourseModule";
import timeCounterModule					 from "../../../../common/store/modules/timeCounterModule";
import baseChatModule from "../../../../common/store/modules/baseChatModule";
import announcementReportModule from "../../../../announcement/store/module/announcementReportModule";

export default {
	announcementModule,
	announcementFormModule,
	announcementObservationModule,
	announcementReportModule,
	announcementTaskModule,
	forumModule,
	chatModule,
	materialCourseModule,
	taskCourseModule,
	notificationModule,
	timeCounterModule,
	baseChatModule
}
