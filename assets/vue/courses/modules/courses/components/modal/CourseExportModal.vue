<template>
  <div class="CourseExportModal">
    <BaseModal
      identifier="courseViewExportModal"
      size="modal-md"
      padding="0"
      :title="`${ $t('GENERATE_REPORT') }`"
    >
      <div class="row mx-0">
        <div class="col-12 p-4">
          <div class="row mx-0 p-3">
            {{ $t('COURSE.EXPORT.DESCRIPTION') }}
          </div>
        </div>
        <div class="col-12 pb-4 text-center">
          <button
              class="btn btn-sm btn-secondary"
              data-bs-dismiss="modal">
            {{ $t('CANCEL') }}
          </button>
          <button
            class="btn btn-sm btn-primary"
            data-bs-dismiss="modal"
            @click="exportMethod">
            {{ $t('COURSE.EXPORT.BUTTON') }}
          </button>
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script>
import BaseModal from "../../../../../base/BaseModal.vue";
import TaskQueueMixin from "../../../../../mixins/TaskQueueMixin";

export default {
  name: "CourseExportModal",
  components: { BaseModal },
  mixins: [TaskQueueMixin],
  methods: {
    async exportMethod() {
      try {
        await this.enqueueTask({
          url: '/admin/course/export-catalog',
          messages: {
            success: this.$t('COURSE.EXPORT.SUCCESS') || 'Solicitud realizada con éxito',
            error: this.$t('COURSE.EXPORT.ERROR') || 'Se ha producido un error'
          }
        });
      } catch (error) {
        console.error('Error:', error);
      }
    }
  }
}
</script>

