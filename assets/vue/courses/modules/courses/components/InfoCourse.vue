<template>
  <div class="InfoCourse">
    <div v-if="!loading">
      <section class="row my-4">
        <div class="col-lg-4">
          <div class="container-course-image">
            <img
              class="course-image"
              :src="course.image"
              :alt="course.name"
            />
          </div>
        </div>
        <div class="col-lg-8">
          <article class="mb-4">
            <h4>{{ $t('COURSE.DESCRIPTION') }} {{ course.name }}</h4>
            <div v-html="course.description" />
          </article>
          <article>
            <h4>{{ $t('COURSE.GENERAL_INFO') }}</h4>
            <div v-html="course.generalInformation" />
          </article>
        </div>
      </section>

      <section class="alert alert-info" role="alert">
        <span class="d-flex align-items-center justify-content-center gap-2">
          <i :class="courseFormat.icon" />
          {{ courseFormat.text }}
        </span>
      </section>

      <section class="row mb-4">
        <div class="col-lg-4">
          <p>
            {{ $t('CHAPTERS.LABEL.PLURAL') }}:
            <b>{{ course.totalChapters }}</b>
          </p>
        </div>
        <div class="col-lg-4">
          <p>
            {{ $t('TRANSLATIONS.LABEL_IN_PLURAL') }}:
            <b class="text-uppercase">{{ course.locale }}</b><span v-if="course.languages.length">, </span>
            <span
              v-for="(language, index) in course.languages"
              :key="index"
              class="text-uppercase translations"
              @click="goToView(language.idCourse, language.nameCourse)"
            >
              {{ language.locale }}
            <span v-if="index < course.languages.length - 1">, </span>
            </span>
          </p>
        </div>
        <div class="col-lg-4">
          <p>
            {{ $t('ANNOUNCEMENT.FORM.ENTITY.FORMATION_TIME') }}:
            <b>{{ hoursCourse }}</b>
          </p>
        </div>
      </section>

      <section>
        <header class="d-flex align-items-center gap-2">
          <i class="fa fa-bullhorn"></i>
          <h5 class="mb-0">{{ $t('COURSE.AUDIENCE.TITLE') }}</h5>
        </header>
        <hr class="my-2" />
        <div class="row my-4">
          <div class="col-lg-4">
            <p>
              {{ $t('COURSE.ACTIVE_LABEL') }}:
              <b>{{ course.active ? $t('YES') : $t('NO') }}</b>
            </p>
          </div>
          <div class="col-lg-4">
            <p>
              {{ $t('COURSE.BTN_OPEN.TITLE') }}:
              <b>{{ course.open ? $t('YES') : $t('NO') }}</b>
            </p>
          </div>
          <div class="col-lg-4">
            <p>
              {{ $t('COURSE.BTN_OPEN_CAMPUS.TITLE') }}:
              <b>{{ course.openVisible ? $t('YES') : $t('NO') }}</b>
            </p>
          </div>
        </div>
      </section>

      <section>
        <header class="d-flex align-items-center gap-2">
          <i class="fa fa-chart-bar"></i>
          <h5 class="mb-0">{{ $t('COURSE.STATS_TITLE') }}</h5>
        </header>
        <hr class="my-2" />
        <div class="course-stats my-4">
          <StatBox
            class="stat"
            icon="fas fa-star"
            :value="averageRating"
            :description="$t('COURSE.STAT_RATING_LABEL')"
            color="#a9c47e"
          />
          <StatBox
            class="stat"
            icon="fas fa-users"
            :value="course.usersStartCourse"
            :description="$t('COURSE.USERS_STARTED_LABEL')"
            color="#68bbb0"
          />
          <StatBox
            class="stat"
            icon="fas fa-graduation-cap"
            :value="course.usersFinishCourse"
            :description="$t('COURSE.USERS_FINISHED_LABEL')"
            color="#e4534e"
          />
          <StatBox
            class="stat"
            icon="fas fa-clock"
            :value="formatCourseDuration"
            :description="$t('COURSE.TOTAL_TIME_SPENT_LABEL')"
            color="#fa7a7e"
          />
          <StatBox
            class="stat"
            icon="fas fa-user-clock"
            :value="formatCourseAverageTimeSpent"
            :description="$t('COURSE.TOTAL_TIME_AVERAGE_SPENT_LABEL')"
            color="#f7be73"
          />
        </div>
      </section>
    </div>

    <div v-else class="d-flex align-items-center justify-content-center">
      <Spinner />
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Spinner from "../../../../admin/components/base/Spinner.vue";
import StatBox from "./StatBox.vue";
import ROUTE_NAMES from "../router/routeNames";

export default {
  name: "InfoCourse",
  components: { Spinner, StatBox },
  data() {
    return {
      ROUTE_NAMES,
      loading: true,
      error: null,
    };
  },
  computed: {
    course: get("coursesModule/getCourseInfo"),
    averageRating() {
      return this.course.averageRating ? Math.min(((this.course.averageRating || 0) / 2)).toFixed(2) : '--';
    },
    courseId() {
      return this.$route.params.id
    },
    hoursCourse() {
      return parseFloat((this.course.totalTimeCourse / 3600).toFixed(2));
    },
    formatCourseDuration() {
      const totalSeconds = this.course.totalTimeCourse;
      return this.formatTime(totalSeconds);
    },
    formatCourseAverageTimeSpent() {
      if (Number(this.course.usersStartCourse) === 0) {
        return "-";
      }
      const avgSeconds = this.course.totalTimeCourse / this.course.usersStartCourse;
      return this.formatTime(avgSeconds);
    },
    courseFormat() {
      return {
        icon: "text-primary " + this.course.icon,
        text: this.course.typeCourse,
      };
    },
  },
  methods: {
    formatTime(totalSeconds) {
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = Math.floor(totalSeconds % 60);
      let result = "";
      if (hours > 0) result += `${hours}h `;
      if (minutes > 0) result += `${minutes}m `;
      if (seconds > 0) result += `${seconds}s`;
      return result.trim() || "-";
    },
    goToView(id, name) {
      this.$router.push({ name: ROUTE_NAMES.VIEW_COURSE, params: { id, name } }).catch()
    },
  },
  async created() {
    try {
      await this.$store.dispatch("coursesModule/fetchCourseById", this.courseId);
    } catch (error) {
      this.error = error;
    } finally {
      this.loading = false;
    }
  },
};
</script>

<style scoped lang="scss">
.container-course-image {
  min-width: 100%;
}
.course-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.alert {
  max-width: fit-content;
}
.course-stats {
  $gap: 1rem;
  display: grid;
  gap: $gap;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  align-items: end;
}
.translations{
  color: hsl(198, 99%, 34%);
  cursor:pointer;
}
.translations:hover{
  text-decoration: underline;
}
</style>
