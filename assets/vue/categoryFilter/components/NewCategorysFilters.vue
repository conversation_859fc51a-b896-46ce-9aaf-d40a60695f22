<template>
  <div class="NewCategorysFilters">
    <div class="CategoryFilter--header col-12 d-flex align-items-center justify-content-end">
      <button @click="newFilter" type="button" class="btn btn-primary m-4"><i class="fa fa-plus"></i>{{ $t('FILTER.CREATE_TITLE') }}</button>
    </div>
    <div class="w-100" v-if="filterPages.length > 0">
      <table class="FilterCategory--table table p-4">
        <thead>
        <tr>
          <th> {{ $t('CATEGORYFILTER.SORT') }}</th>
          <th> {{ $t('FILTER.NAME') }} </th>
          <th style="text-align: right;">{{ $t("ACTIONS") }}</th>
        </tr>
        </thead>
        <tbody>
        <tr
            v-for="(c, index) in this.filterPages "
            :key="c.id"
            draggable
            @dragstart="startDrag($event, index)"
            @drop="onDrop(index)"
            @dragover.prevent
            @dragenter.prevent
        >
          <td><i class="fa fa-bars barsOrdes"></i></td>
          <td>
            <a @click="updateFilter(c, index)" class="link-primary">
            {{c.name}}
            </a>
          </td>
          <td>
            <div class="d-flex justify-content-end">
              <button type="button" class="btn btn-sm btn-primary  ms-2" @click="updateFilter(c, index)"><i class="fa fa-pencil"></i></button>
              <button type="button" class="btn btn-sm btn-danger  ms-2" @click="deleteFilter(c, index)" ><i class="fa fa-trash"></i></button>
            </div>
          </td>
        </tr>
        <tr v-if="this.filterDB.length>10">
          <td colspan="6">
            <div class="d-flex align-items-center justify-content-start"  >
              <jw-pagination
                  :items="this.filterDB"
                  @changePage="onChangePage"
                  :labels="customLabels"
                  name="filterDB"
              ></jw-pagination>
            </div>
          </td>
        </tr>
        </tbody>
      </table>
    </div>

    <div class="modal fade" id="new-NewCategorysFilters-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="new-question-modal" aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="btn-close" aria-label="Close" data-bs-dismiss="modal" @click="closeModalFilter()"></button>
          </div>
          <div class="modal-body">
              <filter-new-category :translations="this.translations" :filter_category_id="categoryFilterId" :filter_id="filter_id" @closeWindows="closeModalFilter"></filter-new-category>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import $ from "jquery";
import {get, sync} from "vuex-pathify";
//import LabelWithInfo from "../../common/components/ui/LabelWithInfo.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import FilterNewCategory from "./FilterNewCategory.vue";

import LabelWithInfo from "../../common/components/ui/LabelWithInfo.vue";
import JwPagination from "jw-vue-pagination";

const PAGE_SIZE = 10;
const customLabels = {
  first: '<<',
  last: '>>',
  previous: '<',
  next: '>'
};

export default {
  name: "NewCategorysFilters",
  components: {Spinner,FilterNewCategory, JwPagination},
  props: {
    value: {
      type: Object|Array,
      default: function () {
        return [];
      }
    },
    realtime: {
      type: Boolean,
      default: true
    },
    categoryFilterId: {
      type: Number|String,
      default: null
    },
    filterPages: {
      type: Array,
      default: null
    },
    filterDB: {
      type: Array,
      default: null
    },

  },
  data(){
    return{
      locale: 'es',
      translations:[],
      pages: {},
      filter_id: 0,
      customLabels,
      warningLocales: {},
      operation: null,
      antName: null,
      dragItem: null,
      dropItem: null,
      filtersSort: [],
    };
  }, computed:{
    ...get("categoryFilterModule", ["getLanguages","getUserlocale", "getFilterCategoryFilterDB"]),
    locales: get("localeModule/locales"),
    defaultLocale: get("localeModule/defaultLocale"),
    languages() {
      return this.getLanguages();
    },
    userlocale() {
      return this.getUserlocale();
    },
    filterCategoryFilterDB(){
      return this.getFilterCategoryFilterDB();
    }
  },
  created(){
    this.locale = this.userlocale;
    this.translations = this.initTranslation();
    this.filtersSort = this.filterPages;
  },
  methods: {
    onChangePage(filterPages) {
      this.filterPages = filterPages;
    },
    newFilter(){
      this.operation = 'create';
      this.filter_id = 0;
      this.translations = this.initTranslation();
      this.openModal();
    },
    updateFilter(filter, index) {
      this.operation = 'update';
      let isUpdate = this.$route.name === 'categoryFilterUpdate';
      this.translations = this.getTranslations(filter.translations);
      this.filter_id = 0;
      this.antName = filter.name;
      if(isUpdate){
        this.filter_id = filter.id;
      }
      this.openModal();
    },
    initTranslation(){
      let translations = [];
      const keys = Object.keys(this.locales);
      keys.forEach((k) => {
        translations.push({
          locale: k,
          name: "",
        });
      });

      return translations;
    },
    getTranslations(pagestranslations) {
      let translations = [];
      const keys = Object.keys(this.locales);
      keys.forEach((k) => {
        const translated = pagestranslations.find((e) => e.locale === k);
        translations.push({
          locale: k,
          name: translated?.name ?? "",
        });
      });

      return translations;
    },
    openModal() {
      $('#new-NewCategorysFilters-modal').modal({
        show  : true,
        static  : true,
        backdrop: false,
        keyboard: false
      });
    },
    async closeModalFilter() {
      $('#new-NewCategorysFilters-modal').modal('hide');
      let localFilters = await this.$store.dispatch('categoryFilterModule/getLocalFilters');
      if(localFilters && localFilters.name) this.updateFilterDb(localFilters);
      localFilters = null;
    },
    updateFilterDb(localFilters){
      if(this.operation === 'update' && localFilters.id !== 0){
        const index = this.filterPages.findIndex((item) => item.id === localFilters.id);
        this.filterPages[index].name = localFilters.name;
        this.filterPages[index].translations = localFilters.translations;
        this.filterPages[index].operation = 'update';
      }

      if(this.operation === 'update' && localFilters.id === 0){
        const index = this.filterPages.findIndex((item) => item.name === this.antName);
        this.filterPages[index].name = localFilters.name;
        this.filterPages[index].translations = localFilters.translations;
      }   
      if(this.operation === 'create'){
        let index = this.filterPages.findIndex((item) => item.name.toLowerCase().trim() === localFilters.name.toLowerCase().trim());
        if(index === -1){
          this.filterPages.push(localFilters);
        }

        index = this.filterDB.findIndex((item) => item.name.toLowerCase().trim() === localFilters.name.toLowerCase().trim());
        if(index === -1){
          this.filterDB.push(localFilters);
        }
      }
    },
    deleteFilter(filter, index){
      if(filter.id !== 0){
        let index = this.filterPages.findIndex((item) => item.id === filter.id);
        this.removeFilterCategory(filter.id);
        this.filterPages.splice(index, 1);
      }else{
        let index = this.filterPages.findIndex((item) => item.name === filter.name);
        this.filterPages.splice(index, 1);

        index = this.filterDB.findIndex((item) => item.name === filter.name);
        this.filterDB.splice(index, 1);
      }
    },
    startDrag(evt, index) {
      this.dragItem = index;
      evt.dataTransfer.dropEffect = "move";
      evt.dataTransfer.effectAllowed = "move";
    },

    async onDrop(index) {
      this.filtersSort = this.filterPages;
      let data = this.orderNewSort(this.dragItem, index);
      
      this.dropItem = this.filtersSort.splice(this.dragItem, 1)[0];
      this.filtersSort.splice(index, 0, this.dropItem);
      this.dropItem = null;

      this.setNewSort(data); 

      await this.$store.dispatch("categoryFilterModule/changeSort", {
        endpoint: `/admin/filter/changeSort`,
        requestData: { filters: data },
      });
    },
    setNewSort(data){
      data.forEach((item) => {       
        const index = this.filterPages.findIndex((filter) => filter.id === item.id);
        this.filterPages[index].sort = item.newSort;
      });
      this.filterPages = this.filtersSort;
    },

    //antIndexSort, newIndexSort--> posición en el vector que se muestra va de 0,9
    orderNewSort(antIndexSort, newIndexSort) {
      let newSortCategoryFilters = [];
      let sortPos = this.filtersSort[antIndexSort].sort;//sort actual en la base de datos
      let newSortPos = this.filtersSort[newIndexSort].sort;//newsort  en la base de datos

      if(sortPos > newSortPos){     
        newSortCategoryFilters = this.sortMayorMenor(sortPos, newSortPos, antIndexSort, newIndexSort);
      }

      if(sortPos < newSortPos){
        newSortCategoryFilters = this.sortMenorMayor(sortPos, newSortPos, antIndexSort, newIndexSort);
      }

      return newSortCategoryFilters;
    },
    sortMayorMenor(sortPos, newSortPos, antIndexSort, newIndexSort){
      let newSortCategoryFilters = [];

      let elmentTras = {
          id: this.filtersSort[antIndexSort].id,
          newSort: newSortPos,
      };
      newSortCategoryFilters.push(elmentTras);

      for(let index = newIndexSort; index < antIndexSort; index++){
        let element = {
          id: null,
          newSort: null,
        };  
        element.id = this.filtersSort[index].id;
        element.newSort =this.filtersSort[index].sort+1;
        
        newSortCategoryFilters.push(element);
      }

      return newSortCategoryFilters;
    },
    sortMenorMayor(sortPos, newSortPos, antIndexSort, newIndexSort){
      let newSortCategoryFilters = [];

      let elmentTras = {
          id: this.filtersSort[antIndexSort].id,
          newSort: newSortPos,
      };
      newSortCategoryFilters.push(elmentTras);

      for(let index = newIndexSort; index > antIndexSort; index--){
        let element = {
          id: null,
          newSort: null,
        };  
        element.id = this.filtersSort[index].id;
        element.newSort =this.filtersSort[index].sort-1;
      
        newSortCategoryFilters.push(element);
      }

      return newSortCategoryFilters;
    },
    removeFilterCategory(filter_id) {
      this.$alertify.confirmWithTitle(
          this.$t("DELETE"),
          this.$t("COMMON_AREAS.QUESTION_DELETE"),
          () => {
            this.$store
                .dispatch("categoryFilterModule/deleteCategoryFilter", {
                  endpoint: `/admin/categoryFilter/${filter_id}/delete_filter`,
                })
                .then((r) => {
                  this.$toast.success(this.$t("DELETE_SUCCESS") + "");
                })
                .catch((e) => {
                  this.$toast.error("DELETE_FAILED");
                })
          },
          () => {}
      );
    }

  },
}

</script>
<style lang="scss" scoped>
.dragBar {
  color: var(--color-primary);
}

.NewCategorysFilters {
  padding: 1rem;

  &--table {
    overflow: auto;
  }
  &--header {
    h3 {
      font-size: 22px;
    }
  }

  &--table {
    background-color: #ffffff;
    border: 0px solid $base-border-color;

    thead {
      border: 0 solid  $base-border-color;
      th {
        border: 0 solid  $base-border-color;
      }
    }

    tbody {
      td, th {
        border-top: 0 solid;
        border-left: 0 solid $base-border-color;
        border-right: 0 solid $base-border-color;
        border-bottom: 1px solid $base-border-color;
      }
    }
  }

  #new-NewCategorysFilters-modal {
    .modal-header {
      border: unset !important;
      color: #212121 !important;
    }
    .modal-body {
      padding: 0 !important;
    }

    .modal-header, .modal-body {
      background-color: #f4f5f6 !important;
    }
  }
  
  .barsOrdes {
    color: var(--color-primary);
  }
}
</style>