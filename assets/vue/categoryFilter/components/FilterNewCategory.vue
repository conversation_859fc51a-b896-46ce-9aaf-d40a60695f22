<template>
    <form class="FilterNew p-3" @submit.prevent="save()">
      <div class="FilterNew--question">
        <h4>{{ $t("COMMON.BASIC_INFO") }}</h4>
      </div>
      <div class="row align-items-start">
        <div class="col-4 p-3"></div>
        <Translation
            v-model="locale"
            :warning="warningLocales"
        >
          <template v-slot:content>
            <div class="form-group col-12" v-for="t in translations" :key="t.locale" v-if="locale === t.locale">
              <label>{{ $t("NAME") }}<span style="color: red">*</span></label>
              <input type="text" class="form-control" v-model="t.name" @input="setWarningLocales()" />
            </div>
          </template>
        </Translation>
      </div>
      <div class="p-2">
        <button type="submit" class="btn btn-primary">{{ $t('SAVE_SUBFILTERS') }}</button>
      </div>
    </form>
</template>
<script>
import Translation from "../../common/components/Translation.vue";
import {get} from "vuex-pathify";
export default{
  name:"FilterNewCategory",
  components: {Translation},
  props:{
    translations: null,
    filter_category_id: null,
    filter_id: 0,
  },
  data(){
    return {
      locale: 'es',
      warningLocales: {}
    }
  },
  computed: {
    ...get("categoryFilterModule",["menses"]),
    defaultLocale: get('localeModule/defaultLocale'),
    userLocale: get('localeModule/userLocale'),
    locales: get("localeModule/locales"),
  },
  created() {
    if (this.locales) {
      const keys = Object.keys(this.locales);
      keys.forEach((k) => {
        this.warningLocales[k] = false;
      });
    }
    this.setWarningLocales();
  },
  watch: {
    translations: {
      handler() {
        this.setWarningLocales();
      },
      deep: true
    }
  },
  methods:{
    setWarningLocales() {
      if (!this.translations) return;

      this.translations.forEach((t) => {
        this.warningLocales[t.locale] = t.name.length < 1;
      });
    },
    save() {
      const name = this.getTranslationName();
      if(!name){
        this.$toast.error(this.$t("ERROR_NAME_FIELD_REQUIRED") + "");
      }else{
        this.$store.dispatch('categoryFilterModule/saveLocalFilters', {requestData: this.dataFormSubmit()});
        this.closeWindows()
      }
    },
    dataFormSubmit(){
      return {
        id: this.filter_id,
        filter_category_id: this.filter_category_id,
        name: this.getTranslationName(),
        translations: this.translations
      };
    },
    getTranslationName() {
      let translationName = this.translations.filter(
          (translation) => translation.locale === this.userLocale
      );
      return translationName[0]?.name??'';
    },
    closeWindows(){
      this.$emit('closeWindows');
    }
  }
}
</script>
<style lang="scss" scoped>
.FilterNew {
  &--type {
    padding: 1rem;

    @include boxed-selector;
    .selector-container {
      border: none !important;
      background-color: #f4f5f6;

      button.selector {
        width: 100px !important;
        height: 100px !important;

        .type-icon {
          font-size: 40px !important;
        }

        &.active {
          background-color: #d8eef8;
        }
      }
    }
  }

  &--question {
    background-color: #FFFFFF;
    padding: 0.15rem;

    span.delete {
      padding: 0.3rem;
      color: red;
      cursor: pointer;
      &:hover {
        border: 1px solid $base-border-color;
      }
    }
  }

  &--footer {
    background-color: #f4f5f6;
    width: 100%;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: flex-end;
    padding: 1rem;
  }
}
</style>