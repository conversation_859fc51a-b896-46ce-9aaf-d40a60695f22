<template>
  <div class="d-flex w-100 align-items-center justify-content-center" v-if="loading">
    <spinner/>
  </div>
  <div class="FormView" v-else>
    <div class="p-0 pt-1 pb-1 m-5">
      <h5 class="CurrentStepSectionTitle">
        <span><i class="fa fa-folder" aria-hidden="true"></i></span>
        {{ $t("PAGES.LABEL.BASIC_INFORMATION") }}
      </h5>
      <div
        class="basic-info--translation"
      >

        <div class="row align-items-start">
          <div class="col-2 pe-3 border-right"></div>

          <Translation
            v-model="locale"
            :warning="warningLocales"
            v-for="t in translations"
            v-if="t.locale === locale"
          >
            <template v-slot:content>
              <div class="form-group col-12">
                <label
                  >{{ $t("NAME") }}<span style="color: red">*</span></label
                >
                <input type="text" class="form-control" v-model="t.name" />
              </div>
            </template>
          </Translation>
        </div>
        <div class="row align-items-start">
          <new-categorys-filters :filter-d-b="filterdb" :filter-pages="filterPages" :category-filter-id="filter_category_id" ></new-categorys-filters>
        </div>
      </div>
      <div class="col-12 d-flex align-items-center justify-content-end">
        <button type="submit" class="btn btn-primary" @click="submit()">
          {{ $t("SAVE") }}
        </button>
      </div>
    </div>
  </div>
  
</template>

<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../base/BaseSwitch.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import Translation from "../../common/components/Translation.vue";
import NewCategorysFilters from "../components/NewCategorysFilters.vue";
const PAGE_SIZE = 10;
export default {
  name: "Form",
  components: {BaseSwitch, BaseForm, Translation, Spinner, NewCategorysFilters},
  data() {
    return {
      locale: 'es',
      translations:[],
      pages: {},
      filterPages:[],
      filterdb: [],
      filter_category_id: 0,
      warningLocales: {},
    };
  },
  computed: {
    ...get("categoryFilterModule", ["loading", "getCategoryFilterDB",  "getLanguages","getUserlocale"]),
    locales: get("localeModule/locales"),
    defaultLocale: get("localeModule/defaultLocale"),
    categoryfilterdb() {
      return this.getCategoryFilterDB();
    },
    languages() {
      return this.getLanguages();
    },
    userlocale() {
      return this.getUserlocale();
    },
    froalaDescriptionConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 150,
        pluginsEnabled: [
          "align",
          "link",
          "url",
          "lists",
          "paragraphStyle",
          "paragraphFormat",
          "quote",
        ],
      };
    },
  },
  async created() {
    if(this.categoryfilterdb.length==0) {
      await this.$store.dispatch('categoryFilterModule/load', '/admin/categoryFilter/all');
    }
    let isUpdate = this.$route.name === 'categoryFilterUpdate';
    this.setButtons(isUpdate);
    //this.locale = this.defaultLocale;
    this.locale = this.userlocale;
    this.translations = this.initTranslation();

    if(isUpdate){
      this.pages = this.categoryfilterdb.find(c => c.id === this.$route.params.id);
      this.translations = this.getTranslations(this.pages.translations);
      this.filterdb = await this.$store.dispatch("categoryFilterModule/getFilterForCategoryFilterId",{
        endpoint: `/admin/categoryFilter/${this.pages?.id}/filter`,
      });
      this.filterPages = this.filterdb //? filterdb.slice(0,PAGE_SIZE):null;
      this.filter_category_id = this.$route.params.id;
    }

    this.setWarningLocales(this.translations);

    await this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: isUpdate ? this.$t('CATEGORY_FILTER.UPDATE') : this.$t('CATEGORY_FILTER.CREATE'),
        params: {}
      }
    });

  },
  mounted() {
    this.$eventBus.$on('onSubmit', () => {
      this.submit();
    });
    this.$eventBus.$on("onDelete", (e) => {
      this.remove();
    });
  },
  beforeDestroy() {
    this.$eventBus.$off('onSubmit');
  },
  methods: {
    setButtons(isUpdate){
      const actions = [];

      if(isUpdate){
        actions.push({
          name: this.$t("DELETE"),
          event: "onDelete",
          class: "btn btn-danger",
        });
      }  

      actions.push({
        name: this.$t("SAVE"),
        event: "onSubmit",
        class: "btn btn-primary",
      });
      
      this.$store.dispatch("contentTitleModule/setActions", {
        route: this.$route.name,
        actions,
      });
    },
    returnToList() {
      this.$router.push({name: 'Home', params: this.$route.params});
    }, 
    setWarningLocales(translations) {
      translations.forEach((t) => {
        let warning = t.locale !== this.locale;
        if (t.locale !== this.locale)
          warning = t.name.length < 1;

        this.warningLocales[t.locale] = warning;
      });
    },
    initTranslation(){
      let translations = [];
      const keys = Object.keys(this.locales);
      keys.forEach((k) => {
        translations.push({
          locale: k,
          name: "",
        });
      });      

      return translations;
    },
    getTranslations(pagestranslations) {
      let translations = [];
      const keys = Object.keys(this.locales);
      keys.forEach((k) => {
        const translated = pagestranslations.find((e) => e.locale === k);
        translations.push({
          locale: k,
          name: translated?.name ?? "",
        });
      });

      return translations;
    },
    getTranslationName() {
      let translationName = this.translations.filter(
        (translation) => translation.locale === this.userlocale
      );
      return translationName[0].name;
    },

    async submit() {
      let filtersDB = [];
      this.filterdb.forEach((item) => {
        if(item.hasOwnProperty("operation")){
          filtersDB.push(item)
        }
      })

      this.pages.name =  this.getTranslationName();
       if (!this.pages.name || this.pages.name.length < 1) {
        this.$toast.error(this.$t("ERROR_NAME_FIELD_REQUIRED") + "");
        return;
      } 

      if (this.$route.name === "categoryFilterCreate") {
        this.pages.active = true;
      }
      this.pages.translations = this.translations;

      let data = {
        pages : this.pages,       
        filters: filtersDB,
      }
      
      const update = this.$route.name === "categoryFilterUpdate";
      const endpoint = update
        ? "/admin/categoryFilter/update"
        : "/admin/categoryFilter/create";
      await this.$store.dispatch("categoryFilterModule/save", {
        endpoint: endpoint,
        requestData: data, 
      });

      this.returnToList();
    },
    remove() {
      this.$alertify.confirmWithTitle(
        this.$t("DELETE"),
        this.$t("COMMON_AREAS.QUESTION_DELETE"),
        () => {
          this.$store
            .dispatch("categoryFilterModule/deleteCategoryFilter", {
              endpoint: `/admin/categoryFilter/${this.pages?.id}/delete`,
            })
            .then((r) => {
              this.$toast.success(this.$t("DELETE_SUCCESS") + "");
            })
            .catch((e) => {
              this.$toast.error("DELETE_FAILED");
            })
            .finally(() => {
              this.returnToList();
            });
        },
        () => {}
      );
    },
  }
}
</script>

<style scoped lang="scss">
.FormView {
  .Header {
    padding: 1rem;
    & > h4 {
      font-size: 22px;
      color: var(--color-neutral-darkest);
    }
  }

  .tab-pane {
    padding: 1rem;
  }

  .CurrentStepSectionTitle {
    font-weight: bold;
    border-bottom: 1px solid $base-border-color;
    width: 100%;
    font-size: 20px;
    padding-bottom: 0.3rem;
  }
}
</style>
