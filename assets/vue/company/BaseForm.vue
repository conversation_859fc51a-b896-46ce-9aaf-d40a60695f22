<script>
import Translation from "../common/components/Translation.vue";
import LabelWithInfo from "../common/components/ui/LabelWithInfo.vue";
import {get} from "vuex-pathify";

export default {
  name: "BaseForm",
  components: {LabelWithInfo, Translation},
  props: {
    /**
     * @param value Current locale
     */
    value: { 
      type: String,
      default: null
    },
    withTranslations: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      currentView: 'basic',
    };
  },
  computed: {
    userLocale: get('localeModule/userLocale'),
    locales: get('localeModule/locales'),
    activeLocale: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  }
}
</script>

<template>
  <div class="BaseForm">
    <div class="w-100" v-if="withTranslations">
      <button @click="currentView = 'basic'" class="btn btn-sm"
              :class="currentView === 'basic' ? 'btn-primary' : 'btn-default'" type="button">{{ $t('COMMON.BASIC_INFO') }}</button>
      <button @click="currentView = 'translations'"
              class="btn btn-sm"
              :class="currentView === 'translations' ? 'btn-primary' : 'btn-default'"
              type="button">{{ $t('TRANSLATIONS.LABEL_IN_PLURAL') }}</button>
    </div>
    <div class="BaseForm--form" v-show="currentView === 'basic'">
      <slot name="form" />
    </div>

    <div class="BaseForm--translations"
         v-show="currentView === 'translations'">
      <h4 class="w-100 text-center">{{ $t('TRANSLATIONS.LABEL_IN_PLURAL') }} [{{ activeLocale }}]</h4>
      <translation v-model="activeLocale" direction="vertical">
        <template v-slot:content v-if="this.activeLocale">
          <slot name="translations" />
        </template>
      </translation>
    </div>
    <div class="col-12 d-flex flex-row flex-nowrap mt-3 justify-content-center align-items-center">
      <button @click="$emit('submit')" class="mr-1 btn btn-primary"><i class="fa fa-save"></i> {{ $t('SAVE') }}</button>
      <button @click="$emit('cancel')" class="ml-1 btn btn-warning"><i>&times;</i> {{ $t('CANCEL') }}</button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.BaseForm {
  background: #ffffff;
  border: 1px solid #212121;
  border-radius: 10px;
  padding: 1rem;
  &--form {
    width: 100%;
    &--basic, &--parameters {
      width: 100%;
    }

    @media #{min-medium-screen()} {
      display: grid;
      grid-template-columns: 300px 1fr;
    }

    @media #{min-large-screen()} {
      display: grid;
      grid-template-columns: 400px 1fr;
    }
  }
  &--translations {
    width: 100%;
  }
}
</style>
