<template>
  <div class="d-flex w-100 align-items-center justify-content-center" v-if="isLoadingLibraries">
    <loader :is-loaded="isLoadingLibraries"></loader>
  </div>
  <div class="CategoryView" v-else>
    <div class="CategoryView__content">
      <router-link v-for="library in libraries"
                   :key="library.id"
                   :to="{name: 'LibraryDetail', params: {category_id: categoryId, id: library.id, categoryName: category?.translations['' + defaultLocale] ?? category?.name}}"
      >
        <library-item :library="library" @on-delete-success="loadLibraries()"></library-item>
      </router-link>
    </div>
    <div class="col-12" v-if="libraries.length === 0">
      <base-not-result/>
    </div>
    <div class="CategoryView__footer" v-if="pagination.totalItems > pageSize">
      <pagination :total-items="pagination.totalItems"
                  :page-size="pageSize"
                  :prop-current-page="pagination.currentPage"
                  @current-page="onCurrentPage"
      ></pagination>
    </div>
  </div>
</template>

<script>
import LibraryItem from "../components/LibraryItem.vue";
import Loader from "../../admin/components/Loader.vue";
import Pagination from "../../admin/components/Pagination.vue";
import BaseNotResult from "../../base/BaseNotResult.vue";

export default {
  name: "CategoryView",
  components: {BaseNotResult, Pagination, Loader, LibraryItem},
  data() {
    return {
      category: null,
      categoryId: undefined,
      pagination: {
        totalItems: 0,
        currentPage: 1,
        query: ''
      },
      libraries: []
    };
  },
  computed: {
    isLoadingLibraries() {
      return this.$store.getters['libraryModule/isLoadingLibraries'];
    },
    pageSize() {
      return this.$store.getters['getPageSize'];
    },
    defaultLocale() {
      return this.$store.getters['localeModule/getDefaultLocale'];
    },
    useGlobalEventBus() {
      return this.$store.getters['contentTitleModule/getUseGlobalEventBus'];
    }
  },
  created() {
    this.setCategoryTitle();
    this.handleRouteParams();
    this.$store.dispatch('contentTitleModule/setActions', {
      route: this.$route.name,
      actions: [
        {
          name: this.$t('LIBRARY.NEW_CONTENT'),
          event: 'onNewContent',
          class: 'btn btn-info'
        }
      ]
    })
  },
  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on('search', e => {
        this.pagination.query = e;
        this.loadLibraries();
      });
      this.$eventBus.$on('onNewContent', e => {
        this.$router.push({ name: "NewLibrary", params: {id: this.$route.params.id, categoryName: this.category.name ?? 'Category'}});
      })
    }
  },
  beforeDestroy() {
    this.$eventBus.$off('search');
  },
  methods: {
    setCategoryTitle(title = null) {
      this.$store.dispatch('contentTitleModule/addRoute', {
        routeName: this.$route.name,
        params: {
          linkName: title ?? this.$t('CATEGORY'),
          params: {}
        }
      })
    },
    handleRouteParams() {
      const params = this.$route.params;
      this.categoryId = params.id;
      this.loadCategory();
      this.loadLibraries();
    },

    loadCategory() {
      this.$store.dispatch('categoryModule/getCategory', this.categoryId).then(r => {
        this.category = r.data;
        const categoryTranslationName = this.category.translations['' + this.defaultLocale] ?? this.category.name;
        this.setCategoryTitle(categoryTranslationName);
      })
    },

    loadLibraries() {
      if (!this.categoryId) return;
      this.$store.dispatch(
          'libraryModule/getLibraries',
          {
            categoryId: this.categoryId,
            currentPage: this.pagination.currentPage,
            query: this.pagination.query
          }).then(res => {
            this.libraries = res.items;
            this.pagination.totalItems = res['total-items'];
          });
    },
    onCurrentPage(page) {
      this.pagination.currentPage = page;
      this.loadLibraries();
    }
  }
}
</script>

 <style scoped lang="scss"> 
.CategoryView {
  &__content {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(270px, 270px));
    gap: 1rem;
    padding: 1rem;

    a {
      text-decoration: none;
      &:hover {
        text-decoration: none;
      }
    }

    .LibraryItem {
      margin: 0.25rem;
    }
  }
}
</style>
