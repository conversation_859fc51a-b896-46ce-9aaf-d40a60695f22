<template>
  <div
    class="loading d-flex w-100 align-items-center justify-content-center"
    v-if="isLoadingLibrary"
  >
    <loader :is-loaded="isLoadingLibrary"></loader>
  </div>
  <div class="LibraryView" v-else>
    <div class="LibraryView__general-info">
      <div
        class="thumbnail"
        :style="{
          'background-image':
            'url(' +
            (library?.thumbnail ?? 'assets/chapters/default-image.svg') +
            ')',
        }"
      >
        <div @click="openModalPlayer()">
          <span
            class="type fab fa-youtube"
            v-if="library.type === 'VIDEO'"
          ></span>
          <span
            class="type fa fa-play"
            v-else-if="library.type === 'AUDIO'"
          ></span>
          <span
            class="type fa fa-link"
            v-else-if="library.type === 'URL'"
          ></span>
          <span class="type fa fa-file-pdf-o" v-else></span>
          <span>{{ $t("LIBRARY.OPEN") }}</span>
        </div>
      </div>
      <div class="info">
        <h1 class="info__name">{{ library?.name }}</h1>
        <p class="info__description" v-html="library?.description"/>
        <span class="info_created_at">{{ library.createdAtText }}</span>
        <span class="info__rating"
          ><span
            class="fa fa-star"
            v-for="index in 5"
            :class="
              index <= library?.rating && library?.rating > 0 ? 'checked' : ''
            "
          ></span
        ></span>
        <span class="info__visualization">{{
          $t("LIBRARY.VIEWS", [library.totalViews])
        }}</span>
        <span class="info__comments"
          ><span class="fa fa-comments"></span>
          {{ library?.totalComments ?? 0 }}</span
        >
      </div>
    </div>
    <div class="LibraryView__content">
      <ul class="nav nav-tabs">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            :class="activePane === 'comments' ? 'active' : ''"
            id="comments-tab"
            @click="activePane = 'comments'"
          >
            <i class="fa fa-comments"></i> {{ $t("LIBRARY.COMMENTS.TITLE") }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            :class="activePane === 'filters' ? 'active' : ''"
            id="filters-tab"
            @click="activePane = 'filters'"
          >
            <i class="fa fa-filter"></i> {{ $t("LIBRARY.DISTRIBUTION") }}
          </button>
        </li>
      </ul>
      <div class="tab-content">
        <div
          class="tab-pane fade"
          :class="activePane === 'comments' ? 'active show' : ''"
          id="comments"
          role="tabpanel"
          aria-labelledby="comments-tab"
        >
          <comments :library="library"></comments>
        </div>
        <div
          class="tab-pane fade"
          :class="activePane === 'filters' ? 'active show' : ''"
          id="filters"
          role="tabpanel"
          aria-labelledby="filters-tab"
        >
          <category-filter
            :save-in-realtime="true"
            :use-rest-methods="true"
            :url-selected-filters="`/admin/library/${libraryId}/filters`"
            :url-add-filter="`/admin/library/${libraryId}/filter`"
            :url-remove-filter="`/admin/library/${libraryId}/filter`"
            :filters-add-all="$t('ADD_ALL')"
            :filters-remove-all="$t('REMOVE_ALL')"
            :filters-search="$t('ADD_REMOVE.PLACEHOLDER.FIND_SOURCE')"
          ></category-filter>
        </div>
      </div>
    </div>

    <viewer v-if="file" :base-path="basePath" :file="file" :custom="false" :modal="true" :open="openModal" @close="openModal = false"/>
    <viewer v-if="video" :base-path="basePath" :file="video" :custom="false" :modal="true" :open="openModal" @close="openModal = false"/>

<!--    <div-->
<!--      id="modal-player"-->
<!--      class="modal fade modal-player"-->
<!--      :class="library.type"-->
<!--    >-->
<!--      <div class="modal-dialog" :class="modalDialogClass">-->
<!--        <div class="modal-content">-->
<!--          <div class="w-100 Viewer__header">-->
<!--            <button type="button" class="close" data-dismiss="modal">-->
<!--              &times;-->
<!--            </button>-->
<!--          </div>-->
<!--          <div class="AudioPlayer" v-if="library.type === 'audio'">-->
<!--            <audio-player-->
<!--              :title="library.name"-->
<!--              :subtitle="library.description"-->
<!--              :src="library.libraryFile.filename"-->
<!--              :thumbnail="library.thumbnail"-->
<!--            ></audio-player>-->
<!--          </div>-->
<!--          <div class="PdfViewer" v-else-if="library.type === 'file'">-->
<!--            <div class="PdfViewer__header">-->
<!--              <button-->
<!--                class="btn btn-sm btn-info mr-1"-->
<!--                :disabled="pdfViewer.currentPage <= 1"-->
<!--                @click="pdfViewer.currentPage = pdfViewer.currentPage - 1"-->
<!--              >-->
<!--                Prev-->
<!--              </button>-->
<!--              {{ pdfViewer.currentPage }} / {{ pdfViewer.numPages }}-->
<!--              <button-->
<!--                class="btn btn-sm btn-info ml-1"-->
<!--                :disabled="pdfViewer.currentPage >= pdfViewer.numPages"-->
<!--                @click="pdfViewer.currentPage = pdfViewer.currentPage + 1"-->
<!--              >-->
<!--                Next-->
<!--              </button>-->
<!--            </div>-->
<!--            <div class="PdfViewer__content">-->
<!--              <pdf-->
<!--                :src="library.libraryFile.filename"-->
<!--                :page="pdfViewer.currentPage"-->
<!--                @num-pages="pdfViewer.numPages = $event"-->
<!--                @page-loaded="pdfViewer.currentPage = $event"-->
<!--              >-->
<!--              </pdf>-->
<!--            </div>-->
<!--          </div>-->
<!--          <div class="VideoPlayer" v-else-if="library.type === 'video'">-->
<!--            <div class="VideoPlayer__header"></div>-->
<!--            <div class="VideoPlayer__content">-->
<!--              <vimeo-player-->
<!--                ref="vimeo-player"-->
<!--                :video-url="library.libraryVideo.url"-->
<!--              ></vimeo-player>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
  </div>
</template>

<script>
import $ from "jquery";
import AudioPlayer from "../components/AudioPlayer.vue";
import CategoryFilter from "../../admin/components/CategoryFilter.vue";
import Comments from "../components/Comments.vue";
import Loader from "../../admin/components/Loader.vue";
import pdf from "vue-pdf";
import Viewer from "../../common/components/viewer/Viewer.vue";

export default {
  name: "LibraryView",
  components: {Viewer, AudioPlayer, Loader, Comments, CategoryFilter, pdf },
  props: ["categoryName"],
  $,
  data() {
    return {
      library: [],
      file: null,
      video: null,
      activePane: "comments",
      libraryId: null,
      filters: [],

      pdfViewer: {
        numPages: 0,
        currentPage: 1,
      },

      openModal: false
    };
  },
  computed: {
    config() {
      return this.$store.getters['configModule/getConfig'];
    },
    isLoadingLibrary() {
      return this.$store.getters["libraryModule/isLoadingLibrary"];
    },
    defaultLocale() {
      return this.$store.getters["localeModule/getDefaultLocale"];
    },
    modalDialogClass() {
      if (this.library.type === "file") {
        return `modal-lg`;
      } else if (
        this.library.type === "audio" ||
        this.library.type === "video"
      ) {
        return "modal-dialog-centered";
      }
      return "";
    },
    basePath() {
      return window.location.origin;
    },
    useGlobalEventBus() {
      return this.$store.getters['contentTitleModule/getUseGlobalEventBus'];
    }
  },
  watch: {
    $route(to, from) {
      this.handleRouteParams();
    },
  },
  created() {
    this.filters = this.config.defaultFilterStatus;
    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: this.$t('LIBRARY.LIBRARY_DETAILS'),
        params: this.$route.params
      }
    });

    this.$store.dispatch('contentTitleModule/setActions', {
      route: this.$route.name,
      actions: [
        {
          name: this.$t('LIBRARY.EDIT'),
          event: 'onEdit',
          class: 'btn btn-info'
        }
      ]
    })
    this.handleRouteParams();
  },
  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on('onEdit', e => {
        this.$store.dispatch('contentTitleModule/removeRouteFromContentTitle', this.$route.name);
        this.$router.replace({ name: 'EditLibrary', params: { id:this.$route.params.id }})
      });
    }
  },
  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off('onEdit');
    }
  },
  methods: {
    openModalPlayer() {
      console.log('openModalPlayer')
      if (
        (this.library.type === "AUDIO" || this.library.type === "PDF") &&
        !this.file
      ) {
        this.$toast.error(
          "No file has been provided. Please update the library and upload the file"
        );
        return;
      }
      if (this.library.type === "VIDEO" && !this.video) {
        this.$toast.error(
          "No video has been provided. Please update the library and upload the file"
        );
        return;
      }
      if (this.library.type === "URL") {
        const popup = window.open(this.library.url, "_blank");
        if (popup == null || typeof popup == "undefined") {
          this.$toast.error("Failed to open url. Check browser permissions");
        } else {
          popup.focus();
        }
        return;
      }
      this.openModal = true;
    },
    closeModalPlayer() {
      $("#modal-player").modal("hide");
    },
    handleRouteParams() {
      const params = this.$route.params;
      this.libraryId = params.id;
      this.loadLibrary(params.id);
    },
    loadLibrary(id) {
      this.$store.dispatch("libraryModule/getLibrary", id).then((r) => {
        const { error, data } = r;
        if (error) {
          this.$toast.error(data);
          this.$router.go(-1);
        } else {
          this.library = data.library;
          this.file = data.file;
          this.video = data.video;
        }
      });
    },
  },
};
</script>

 <style scoped lang="scss"> 
.LibraryView {
  // border: 1px solid #CBD5E1;
  &__general-info {
    display: grid;
    flex-flow: row nowrap;
    margin: 1rem;
    grid-template-columns: 320px auto;
    .thumbnail {
      width: 300px;
      height: 300px;
      background-size: cover;
      background-position: center center;
      display: flex;
      align-items: center;
      justify-content: center;
      div {
        width: 95px;
        display: flex;
        flex-flow: column;
        align-items: center;
        justify-content: center;
        background: rgba(#212121, 0.1);
        border-radius: 10px;
        cursor: pointer;
        transition: all ease 0.5s;
        padding: 10px;
        &:hover {
          transform: scale(1.1);
        }

        span {
          font-size: 15px;
          color: #ffffff;
        }

        span.type {
          font-size: 45px;
        }
      }
    }

    .info {
      flex-grow: 1;
      margin-left: 1rem;
      display: flex;
      flex-flow: column;

      &__name {
        font-size: 22px;
      }

      &__description {
        font-size: 17px;
      }

      &__created_at {
        font-size: 12px;
        color: #808080;
      }

      &__rating {
        margin-top: auto;
        .fa-star {
          color: #212529;
          &:hover {
            color: #212529;
          }

          &.checked {
            color: #009bdb;
          }
        }
      }

      &__visualization {
        font-weight: bold;
        margin-top: 0.25rem;
      }

      &__comments {
        margin-top: 0.25rem;
      }
    }
  }

  &__content {
    margin-top: 3rem;

    .nav-link {
      color: #707070;
      border-color: transparent;
      &.active {
        border-color: #fff;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        color: #1e293b;
      }
    }

    .tab-content {
      background-color: #ffffff;
      border: none;

      .tab-pane {
        padding: 1rem 3rem;
      }
      :deep(.nav-tabs) {
        border-bottom: none;
        .nav-link {
          color: #999;

          &.active {
            border: none;
            border-bottom: solid 3px black;
          }
          &:hover{
            border-color: transparent;
          }
        }
      }
      .card {
        box-shadow: none;
      }
    }
  }
}

#modal-player {
  &.audio {
    .modal-dialog {
      max-width: 600px !important;
    }
  }

  &.video {
    .modal-dialog {
      max-width: 640px !important;
    }
  }

  .Viewer__header {
    background-color: #32383e;
    color: #ffffff;
    padding: 0.25rem 1rem;
  }

  .PdfViewer {
    &__header {
      width: 100%;
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: center;
      background-color: #32383e;
      color: #ffffff;
      padding: 0.25rem;
    }
    &__content {
      padding: 0 0.25rem 0.25rem;
      background-color: #32383e;
    }
  }
}
</style>
