<template>
  <div class="HomeView">
    <div class="HomeView__Header">
      <div class="HomeView__Header_content">
        <h1>{{ $t('LIBRARY.HOME.TITLE') }}</h1>
        <p v-html="$t('LIBRARY.HOME.DESCRIPTION')"/>
      </div>
      <div class="HomeView__Header_banner">
        <img src="/assets/imgs/library_home.svg" alt="">
      </div>
    </div>
    <div class="HomeView__Content d-flex flex-column">
      <div class="w-100">
        <button class="btn btn-info" @click="newCategoryModal()">{{ $t('LIBRARY.NEW_CATEGORY') }}</button>
      </div>
      <div class="loader w-100 d-flex align-items-center justify-content-center" v-if="isLoadingCategories">
        <loader :is-loaded="isLoadingCategories"></loader>
      </div>
      <div class="table-container" v-else>
        <table class="table">
          <thead>
          <tr>
            <th>{{ $t('LIBRARY.CATEGORY') }}</th>
            <th class="text-center">{{ $t('LIBRARY.ACTIVE') }}</th>
            <th class="text-center">{{ $t('LIBRARY.CONTENT') }}</th>
            <th></th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="category in categories" :key="category.id">
            <td><router-link :to="{name: 'CategoryView', params: { id: category.id }}">{{ category.translations['' + defaultLocale]??category.name }}</router-link></td>
            <td class="text-center">
              <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input" :id="'switch_' + category.id" v-model="category.active" @change="activateCategory(category.id)">
                <label class="custom-control-label" :for="'switch_' + category.id"></label>
              </div>
            </td>
            <td class="text-center"><span class="badge badge-secondary">{{ category.total }}</span></td>
            <td>
              <div class="actions">
                <button class="btn btn-sm btn-info" @click="editCategoryModal(category)"><i class="fa fa-pencil"></i></button>
                <button class="btn btn-sm btn-danger" @click="deleteCategory(category)"><i class="fa fa-trash"></i></button>
              </div>
            </td>
          </tr>
          <tr>
            <td colspan="4" v-if="categories.length === 0">
              <base-not-result />
            </td>
          </tr>
          </tbody>
        </table>
        <div class="col-12" v-if="totalItems > pageSize">
          <pagination :page-size="pageSize" :total-items="totalItems" @current-page="onCurrentPage" :prop-current-page="currentPage"></pagination>
        </div>
      </div>
    </div>

    <div id="modal-category" class="modal fade">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <new-library-category
              :category="category"
              @on-success="onNewCategorySuccess()"
              @on-cancel="closeCategoryModal()"
          ></new-library-category>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Loader from "../../admin/components/Loader.vue";
import NewLibraryCategory from "../components/NewLibraryCategory.vue";
import $ from 'jquery';
import Pagination from "../../admin/components/Pagination.vue";
import BaseNotResult from "../../base/BaseNotResult.vue";

export default {
  name: "HomeView",
  components: {BaseNotResult, Pagination, NewLibraryCategory, Loader},
  $,
  data() {
    return {
      categories: [],
      category: null,
      currentPage: 1,
      totalItems: 0,
      query: '',
    };
  },
  computed: {
    isLoadingCategories() {
      return this.$store.getters['categoryModule/isLoadingCategories'];
    },
    pageSize() {
      return this.$store.getters['getPageSize'];
    },
    defaultLocale() {
      return this.$store.getters['localeModule/getDefaultLocale'];
    },
    useGlobalEventBus() {
      return this.$store.getters['contentTitleModule/getUseGlobalEventBus'];
    }
  },
  created() {
    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: this.$t('LIBRARY.MAIN_TITLE'),
        params: this.$route.params
      }
    });

    this.$store.dispatch('contentTitleModule/setActions', {
      route: this.$route.name,
      actions: [
        {
          name: this.$t('LIBRARY.NEW_CATEGORY'),
          event: 'onNewCategory',
          class: 'btn btn-info'
        }
      ]
    })

    this.loadCategories();
  },
  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on('search', e => {
        this.query = e;
        this.currentPage = 1;
        this.loadCategories();
      });
      this.$eventBus.$on('onNewCategory', e => {
        this.showCategoryModal();
      });
    }
  },
  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off('search');
      this.$eventBus.$off('onNewCategory');
    }
  },
  methods: {
    onCurrentPage(page) {
      this.currentPage = page;
      this.loadCategories();
    },

    activateCategory(id) {
      const category = this.categories.find(item => item.id === id);
      this.$store.dispatch('categoryModule/activateCategory', { id: category.id, active: category.active }).then(r => {
        const { error, data } = r;
        if (error) {
          this.$toast.error(data);
        } else {
          this.$toast.success(data);
        }
      })
    },

    loadCategories() {
      this.$store.dispatch('categoryModule/getCategories', {
        currentPage: this.currentPage,
        query: this.query
      }).then(res => {
        this.totalItems = res['total-items'];
        this.categories = res.items;
      });
    },
    editCategoryModal(category) {
      this.category = category;
      this.showCategoryModal();
    },
    deleteCategory(category) {
      this.$alertify.confirmWithTitle(
          this.$t('LIBRARY.DELETE.CONFIRM_CATEGORY_DELETE'),
          this.$t('LIBRARY.DELETE.CONFIRM_CATEGORY_DELETE_INFO'),
          () => {
            this.$store.dispatch('categoryModule/deleteCategory', category.id).then(r => {
              const { data, error } = r;
              if (error) this.$toast.error(data);
              else {
                this.$toast.success(data);
                this.loadCategories();
              }
            })
          },
          () => {}
      );
    },
    newCategoryModal() {
      this.category = null;
      this.showCategoryModal();
    },
    showCategoryModal() {
      $('#modal-category').modal('show');
    },
    closeCategoryModal() {
      $('#modal-category').modal('hide');
      this.category = null;
    },
    onNewCategorySuccess() {
      this.loadCategories();
      this.closeCategoryModal();
    }
  }
}
</script>

 <style scoped lang="scss"> 
.HomeView {
  &__Header {
    padding: 2rem 1rem 3rem 3rem;
    background: #FFFFFF;
    display: flex;
    flex-flow: row nowrap;
    gap: 3rem;
    &_content {
      width: 46%;
      h1 {
        font-size: 22px;
        color: #1E293B;
      }

      p {
        margin-top: 1rem;
        font-size: 17px;
        color: #808080;
      }
    }

    &_banner {
      width: 375px;
      position: relative;
      img {
        width: 100%;
        position: absolute;
        top: -2rem;
      }
    }
  }

  &__Content {
    background-color: #F6F7F8;
    padding: 2rem 5rem 2rem 5rem;
    .table-container {
      margin-top: 2rem;

      .table {
        .actions {
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
          justify-content: flex-end;
          button {
            margin: 0.15rem;
          }
        }
      }
    }
  }
}
</style>
