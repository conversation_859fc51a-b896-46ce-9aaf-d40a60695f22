import initStore from "../../common/store";
import categoryModule from './modules/category';
import libraryModule from './modules/library';

export default initStore(
    {
        state: {
            pageSize: 10,
            saving: false
        },
        getters: {
            getPageSize(state) { return state.pageSize; },
            getIsSaving(state) { return state.saving; },
        },
        mutations: {
            SET_IS_SAVING(state, saving) {
                state.saving = saving;
            }
        },
        actions: {
            setIsSaving({ commit }, saving) {
                commit('SET_IS_SAVING', saving);
            }
        },
        modules: {
            categoryModule,
            libraryModule
        }
    }
)
