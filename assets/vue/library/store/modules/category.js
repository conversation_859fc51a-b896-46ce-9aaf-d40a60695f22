import axios from 'axios';

export const getters = {
    isLoadingCategories(state) { return state.isLoading }
};

const mutations = {
    SET_IS_LOADING(state, loading) {
        state.isLoading = loading;
    }
};

export const actions = {
    async getCategories({ commit }, { currentPage, query }) {
        commit('SET_IS_LOADING', true);
        try {
            let url = `/admin/library/categories/${ currentPage }`;
            if (query.length > 0) {
                url += `?query=${ query }`;
            }

            const result = await axios.get(url);
            const { error, data } = result.data;
            if (!error) return data;
            return [];
        } catch (e) {
            console.log(e);
            return [];
        } finally {
            commit('SET_IS_LOADING', false);
        }
    },

    async newCategory({commit}, formData) {
        try {
            const url = '/admin/library/category';
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const result = await axios.post(url, formData, {
                headers
            });
            return result.data;
        } catch (e) {
            return Promise.reject(e);
        }
    },

    async updateCategory({commit}, {categoryId, formData}) {
        try {
            const url = `/admin/library/category/${categoryId}`;
            const headers = {
                'Content-Type': 'x-www-form-urlencoded'
            };
            const result = await axios.post(url, formData, {
                headers
            });
            return result.data;
        } catch (e) {
            return Promise.reject(e);
        }
    },

    async getCategory({ commit }, categoryId) {
        try {
            const url = `/admin/library/category/${categoryId}`;
            const result = await axios.get(url);
            return result.data;
        } finally {

        }
    },

    async deleteCategory({ commit }, id) {
        try {
            const url = `/admin/library/category/${id}`;
            const result = await axios.delete(url);
            return result.data;
        } finally {

        }
    },

    async activateCategory({ commit }, { id, active }) {
        try {
            const url = `/admin/library/category/${id}/activate`;
            const result = await axios.post(url, {active});
            return result.data;
        } finally {

        }
    }
};

export default {
    namespaced: true,
    state: {
        isLoading: true
    },
    getters,
    mutations,
    actions
}
