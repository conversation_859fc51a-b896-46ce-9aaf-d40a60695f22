import {make} from "vuex-pathify";
import axios from "axios";

const state = {
    contents: [],
    loading: true
};

const mutations = {
    ...make.mutations(state)
};

export const getters = {
    ...make.getters(state)
};

export const actions = {
    ...make.actions(state),
    async getChapterContents({ commit }, id) {
        commit('SET_LOADING', true);
        try {
            const result = await axios.get(`/admin/course/chapter/${id}/contents`);
            const { data } = result.data;
            commit('SET_CONTENTS', data);
        } finally {
            commit('SET_LOADING', false);
        }
    },

    async deleteChapterContent({ commit, getters }, { chapterId, contentId }) {
        commit('SET_LOADING', true);
        try {
            const result = await axios.delete(`/admin/course/chapter/${chapterId}/contents/${contentId}`);
            const { error } = result.data;
            if (!error) {
                const contents = getters['contents'];
                const result = contents.filter(content => content.id !== parseInt(contentId));
                commit('SET_CONTENTS', result);
            }
            return result.data;
        } finally {
            commit('SET_LOADING', false);
        }
    },

    async createChapterContent({ commit, getters }, { chapterId, formData }) {
        const headers = {
            'Content-Type': 'multipart/form-data'
        };
        const result = await axios.post(`/admin/course/chapter/${chapterId}/contents/new`, formData, { headers });
        const { error, data } = result.data;

        if (!error) {
            const contents = getters['contents'] ?? [];
            contents.push(data);
            commit('SET_CONTENTS', contents);
        }

        return result.data;
    },

    async updateChapterContent({ commit, getters }, { chapterId, chapterContentId, formData }) {
        const headers = {
            'Content-Type': 'application/json'
        };
        const result = await axios.post(`/admin/course/chapter/${chapterId}/content/${chapterContentId}/update`, formData, { headers })
        const { error, data } = result.data;

        if (!error) {
            const contents = getters['contents'] ?? [];
            console.log(contents)
            console.log(data)
            const index = contents.findIndex(c => c.id === data.id);
            if (index >= 0) {
                contents[index] = data;
                commit('SET_CONTENTS', contents);
            }
        }

        return result.data;
    },

    async increaseChapterContentPosition({ commit }, contentId) {
        const result = await axios.patch(`/admin/chapter/content/${contentId}/increase-position`);
        const { error, data } = result.data;
        if (!error) {
            commit('SET_CONTENTS', data);
        }

        return result.data;
    },

    async decreaseChapterContentPosition({ commit }, contentId) {
        const result = await axios.patch(`/admin/chapter/content/${contentId}/decrease-position`);
        const { error, data } = result.data;
        if (!error) {
            commit('SET_CONTENTS', data);
        }

        return result.data;
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}
