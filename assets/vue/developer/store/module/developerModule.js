import { make } from "vuex-pathify";
import axios from "axios";

const getDefaultState = () => ({
  loading: false,
  catalogs: [],
});

const state = () => getDefaultState();

export const getters = {
  getCatalogs: (state) => () => state.catalogs,
};

export const mutations = {
  ...make.mutations(state),
};

export const actions = {
  ...make.actions(state),

  async fetchCatalogs({ commit }) {
    try {
      commit("SET_LOADING", true);
      const { data, error } = await axios.get("admin/developer/catalogs/all");

      commit("SET_CATALOGS", data?.data);

      if (error) {
        throw new Error(data?.message);
      }

    } catch (err) {
      console.log(err);
    }
    finally {
      commit("SET_LOADING", false);
    }
  },

  async executeServices({ commit }, { endpoint, requestData }) {
    try {
      commit("SET_LOADING", true);
      const { data, error } = await axios.post(endpoint, requestData);

      if (error) {
        throw new Error(data?.message);
      }

      commit("VALUE_SERVICES", data?.data);
    } catch (err) {
      console.log(err);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async executeAllServices({ commit }, { endpoint }) {
    try {
      commit("SET_LOADING", true);
      const { data, error } = await axios.get(endpoint);

      console.log(data);

    /*   if (error) {
        throw new Error(data?.message);
      }
 */
     // commit("VALUE_SERVICES", data?.data);
    } catch (err) {
      console.log(err);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async executeUpdateUserCourse({ commit }, {endpoint}){
    try{
      commit("SET_LOADING", true);
      const { data, error } = await axios.get(endpoint);
      console.log(data);
    }catch(err){
      console.log(err);
    }finally {
      commit('SET_LOADING', false);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
