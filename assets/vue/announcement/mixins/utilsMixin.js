export const UtilsMixin = {
  data() {
    return {
      currentUtilsLocale: 'es-Es'
    }
  },
  methods: {
    getDate(dateStr) {
      const formattedDateStr = dateStr?.replace(
        /^(\d{2})-(\d{2})-(\d{4}) (\d{2}):(\d{2}):(\d{2})$/,
        "$3-$2-$1T$4:$5:$6"
      );
      if (
        !formattedDateStr ||
        !formattedDateStr.length ||
        isNaN(parseInt(`${new Date(formattedDateStr).getTime()}`))
      )
        return "--";

      const dateObj = new Date(formattedDateStr);
      const date = `${dateObj.getDate()}`.padStart(2, '0');
      const month = `${dateObj.getMonth() + 1}`.padStart(2, '0');

      return `${date}/${month}/${dateObj.getFullYear()}`;
    },
    getTime(dateStr) {
      const formattedDateStr = dateStr?.replace(
        /^(\d{2})-(\d{2})-(\d{4}) (\d{2}):(\d{2}):(\d{2})$/,
        "$3-$2-$1T$4:$5:$6"
      );
      if (
        !formattedDateStr ||
        !formattedDateStr.length ||
        isNaN(parseInt(`${new Date(formattedDateStr).getTime()}`))
      )
        return "--";

      const dateObj = new Date(formattedDateStr);
      const hour = `${dateObj.getHours()}`.padStart(2, '0');
      const minutes = `${dateObj.getMinutes()}`.padStart(2, '0');

      return `${hour}:${minutes}`;
    },
    getDateTime(dateStr) {
      if (!dateStr || !dateStr.length || isNaN(parseInt(`${new Date(dateStr).getTime()}`)))
        return '--';

      const dateObj = new Date(dateStr);
      const hour = `${dateObj.getHours()}`.padStart(2, '0');
      const date = `${dateObj.getDate()}`.padStart(2, '0');
      const month = `${dateObj.getMonth() + 1}`.padStart(2, '0');
      const minutes = `${dateObj.getMinutes()}`.padStart(2, '0');
      const seconds = `${dateObj.getSeconds()}`.padStart(2, '0');

      return `${dateObj.getFullYear()}-${month}-${date} ${hour}:${minutes}:${seconds}`;
    },
    getDateText(dateStr, onlyDate = false) {
      if (!dateStr || !dateStr.length || isNaN(parseInt(`${new Date(dateStr).getTime()}`)))
        return '--';

      const dateObj = new Date(dateStr);
      const dayName = dateObj.toLocaleDateString(this.currentUtilsLocale, { weekday: 'long' });
      const date = `${dateObj.getDate()}`.padStart(2, '0');
      const month = dateObj.toLocaleDateString(this.currentUtilsLocale, { month: 'short' });
      const hour = `${dateObj.getHours()}`.padStart(2, '0');
      const minutes = `${dateObj.getMinutes()}`.padStart(2, '0');
      return onlyDate
        ? `${dayName}, ${date} de ${month} de ${dateObj.getFullYear()}`
        : `${dayName}, ${date} de ${month} de ${dateObj.getFullYear()}, ${hour}:${minutes} h`;
    },
    getOnlyDateText(dateStr) {
      if (!dateStr || !dateStr.length || isNaN(parseInt(`${new Date(dateStr).getTime()}`)))
        return '--';

      const dateObj = new Date(dateStr);
      const date = `${dateObj.getDate()}`.padStart(2, '0');
      const month = dateObj.toLocaleDateString(this.currentUtilsLocale, { month: 'long' });
      return `${date} de ${month} de ${dateObj.getFullYear()}`;
    },
    getTodayAsText() {
      const dateObj = new Date();
      const date = `${dateObj.getDate()}`.padStart(2, '0');
      const month = dateObj.toLocaleDateString(this.currentUtilsLocale, { month: 'long' });
      return `${date} de ${month} de ${dateObj.getFullYear()}`;
    },

    cleanString(text) {
      return text.replace(/\s+/g, ' ').trim()
    },

    async showReport(user, announcementId) {
      const data = {
        userId: user.id,
        announcementId: announcementId,
      };

      const pdfReport = await this.$store.dispatch(
        'announcementModule/downloadAnnouncementUserReport',
        data
      );

      const link = document.createElement('a');
      link.href = pdfReport;
      link.download = `report-${user.name}.pdf`;
      link.click();
    },
  },
};
