<template>
  <div
    class="d-flex align-items-center justify-content-center"
    v-if="loadingAnnouncementTutors"
  >
    <loader :is-loaded="loadingAnnouncementTutors" />
  </div>
  <div class="AnnouncementTutors" v-else>
    <tutor
      v-for="tutor in tutors"
      :key="tutor.id"
      :tutor="tutor"
      @upload-cv="open"
      @refresh="loadAnnouncementTutors()"
    ></tutor>

    <div
      class="modal fade"
      id="tutorCvModal"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="tutorCvModalLabel"
      aria-hidden="true"
    >
      <div
        class="modal-dialog"
        ref="tutorCvModalDialog"
        id="tutorCvModalDialog"
      >
        <div class="modal-content" id="tutorCvModalContent">
          <div class="modal-header">
            <h5 class="modal-title" id="tutorCvModalLabel">
              {{ $t("ANNOUNCEMENT.TUTOR.UPLOAD_CV") }}
            </h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              @click="close()"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <form action="" @submit.prevent :id="formId">
              <file-selector
                :title="null"
                :multiple="false"
                name="user-cv"
                accept="application/pdf"
                preview-default-class="no-background-size"
                default-image="assets/file_selector/add.svg"
                :apply-extra-actions-style="true"
                btn-selector-value="FILE_UPLOAD.UPLOAD_PDF"
              >
                <template v-slot:extra-actions>
                  <button
                    class="btn btn-primary ml-1"
                    @click="upload()"
                    type="button"
                  >
                    {{ $t("SAVE") }}
                  </button>
                </template>
              </file-selector>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from "jquery";
import { get } from "vuex-pathify";
import FileSelector from "../../../common/components/FileSelector.vue";
import Loader from "../../../admin/components/Loader.vue";
import Tutor from "../../components/Tutor.vue";
import {
  A4_WIDTH,
  A4_HEIGHT,
} from "../../../common/components/FileSelectorRender.vue";

export default {
  name: "AnnouncementTutors",
  components: { Loader, FileSelector, Tutor },
  data() {
    return {
      formId: "tutor-upload-form",
      currentTutorCv: null,
    };
  },
  computed: {
    announcement: get("announcementModule/announcement"),
    tutors: get("announcementModule/announcementTutors"),
    loadingAnnouncementTutors: get(
      "announcementModule/loadingAnnouncementTutors"
    ),
  },
  methods: {
    upload() {
      const form = document.forms[this.formId];
      const formData = new FormData(form);
      const files = document.getElementById("user-cv").files;
      if (files.length === 0) {
        this.$toast.error(this.$t("FILE_UPLOAD.NO_FILE_SELECTED") + "");
        return;
      }
      formData.delete("user-cv");
      formData.append("file", files[0]);

      this.$alertify.confirmWithTitle(
        this.$t("FILE_UPLOAD.CONFIRM_UPLOAD.TITLE"),
        this.$t("FILE_UPLOAD.CONFIRM_UPLOAD.DESCRIPTION"),
        () => {
          this.$store
            .dispatch("userModule/uploadUserCV", {
              id: this.currentTutorCv.id,
              formData,
            })
            .then((res) => {
              const { error, data } = res;
              if (error) this.$toast.error(data);
              else {
                this.$toast.success(this.$t("FILE_UPLOAD.SUCCESS") + "");
                this.$emit("refresh");
                this.loadAnnouncementTutors();
                this.close();
              }
            });
        },
        () => {}
      );
    },
    open(tutor) {
      this.currentTutorCv = tutor;
      $("#tutorCvModal").modal({
        show: true,
        static: true,
        backdrop: false,
        keyboard: false,
      });

      // Make modal A4 format for rendering pdf file
      const modalDialog = document.getElementById("tutorCvModalDialog");

      const modalHeight = modalDialog.clientHeight;
      const modalWidth = (A4_WIDTH / A4_HEIGHT) * modalHeight;
      modalDialog.style.width = `${modalWidth}px`;

      const userCv = document.getElementById("user-cv-dropzone");
      const userCvWidth = userCv.clientWidth;
      const userCvHeight = (A4_HEIGHT / A4_WIDTH) * userCvWidth;
      userCv.style.height = `${userCvHeight}px`;
    },
    close() {
      this.currentTutorCv = null;
      $("#tutorCvModal").modal("hide");
    },

    loadAnnouncementTutors() {
      this.$store.dispatch(
        "announcementModule/loadAnnouncementTutors",
        this.announcement.id
      );
    },
  },
};
</script>

 <style scoped lang="scss"> 
.AnnouncementTutors {
  width: 100%;
  &--tutors {
  }

  #tutorCvModal {
    .modal-content {
      height: calc(100% - 3rem);
    }

    :deep(.FileSelector__preview) {
      width: 100% !important;
      margin-left: auto;
      margin-right: auto;
    }
  }
}
</style>
