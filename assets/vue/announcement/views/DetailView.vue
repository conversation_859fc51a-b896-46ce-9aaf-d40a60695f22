<template>
  <div v-if="loading" class="d-flex w-100 align-content-center justify-content-center">
    <spinner />
  </div>
  <div class="AnnouncementDetailView" v-else>
    <div class="col-12">
      <div
          class="hideOnPrint col-12 d-flex justify-content-between align-content-center p-4 flex-column flex-md-row"
      >
        <div>
          <b
          ><i class="fa fa-calendar"></i>
            {{ $t("ANNOUNCEMENT.GENERAL.NAME", [announcement?.course?.name]) }}</b
          >
          <span class="text-detail"
          >({{ announcementStartAt }} - {{ announcementFinishAt }})</span
          >
        </div>
      </div>
    </div>
    <div>
      <ul class="nav nav-tabs ps-4 user-select-none hideOnPrint">
        <li class="nav-item" role="presentation" style="margin: -1px;">
          <button
            class="nav-link"
            :class="activePane === 'info' ? 'active' : ''"
            id="info-tab"
            @click="activePane = 'info'"
          >
            <i class="fa fa-file"></i> {{ $t("ANNOUNCEMENT.INFO") }}
          </button>
        </li>
      </ul>

      <router-view />
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>

<script>
  import { get, sync } from "vuex-pathify";
  import axios from "axios";
  import Spinner from "../../base/BaseSpinner.vue";
  import { ANNOUNCEMENT_SOURCE } from "../store/module/announcementForm/common";
  
  export default {
    name: "DetailView",
    components: { Spinner },
    data() {
      return {
        activeTab: 'info',
        startAt: undefined,
        finishAt: undefined,
        loadingTutors: true,
        tutors: [],
        loadingTasks: true,
        tasks: [],
        loading: true
      };
    },
    computed: {
      activePane: sync('announcementModule/activePane'),
      announcement: get('announcementModule/announcement'),
      groupSelected: get('announcementModule/groupSelected'),
      isLoading: get('announcementModule/isLoading'),
      useGlobalEventBus: get('contentTitleModule/getUseGlobalEventBus'),
      refresh: get('announcementModule/refresh'),
      materialsEnabled: get('configModule/config@materialsEnabled'),
      tasksEnabled: get('configModule/config@tasksEnabled'),
      groupStudents: get('announcementModule/calledUsers'),
      uncompletedUserProfile: get("announcementModule/uncompletedUserProfile"),
      announcementStartAt() {
        return this.dateFormat(this.announcement?.startAt || "");
      },
      announcementFinishAt() {
        return this.dateFormat(this.announcement?.finishAt || "");
      }
    },
    watch: {
      announcement: {
        handler() {
          this.initView();
          this.announcement.dateTo = this.dateFormat(this.announcement?.finishAt || "");
          this.announcement.dateFrom = this.dateFormat(this.announcement?.startAt || "");
        },
        deep: true,
      },
      refresh: {
        handler() {
          this.handleRefresh();
        },
        deep: true,
        immediate: true,
      }
    },
    async created() {
      if (!this.announcement || this.announcement.id !== this.$route.params.id || this.refresh.action === "announcement") {
        await this.loadAnnouncement();
      }
      this.loading = false;
      this.$store.dispatch('announcementModule/getFundaeCatalogs');
    },
    mounted() {
      if (this.useGlobalEventBus) {
        this.$eventBus.$on("onEditAnnouncement", () => {
          const routeName = this.announcement?.course?.source?.toUpperCase() === ANNOUNCEMENT_SOURCE.EXTERN.toUpperCase()
            ? 'UpdateAnnouncementExtern'
            : 'UpdateAnnouncement';
          this.$router.push({
            name: routeName,
            params: { id: this.$route.params.id }
          });
        });
      }
    },
    beforeDestroy() {
      if (this.useGlobalEventBus) {
        this.$eventBus.$off("onEditAnnouncement");
      }
    },
    methods: {
      initView() {
        this.$store.dispatch("contentTitleModule/addRoute", {
          routeName: this.$route.name,
          params: {
            linkName: this.announcement.course.name,
            params: this.$route.params,
          },
        });
        const actions = [];
        if (this.$isGranted("ROLE_MANAGER"))
          actions.push({
            name: this.$t("EDIT"),
            event: "onEditAnnouncement",
            class: "btn btn-primary",
          });
        this.$store.dispatch("contentTitleModule/setActions", {
          route: this.$route.name,
          actions,
        });
        this.loadAnnouncementDetails();
      },
      async handleRefresh() {
        if (!this.refresh.refresh) return;
        if (this.refresh.action === null && this.refresh.refresh) {
          await this.loadAnnouncement();
          this.initView();
        } else {
          switch (this.refresh.action) {
            case "users":
              await this.$store.dispatch("announcementModule/loadAnnouncementCalledUsers", this.$route.params.id);
              break;
            case "tutors":
              await this.$store.dispatch("announcementModule/loadAnnouncementTutors", this.$route.params.id);
              break;
            case "materials":
              await this.$store.dispatch("materialCourseModule/getAnnouncementMaterials", this.$route.params.id);
              break;
            case "tasks":
              await this.$store.dispatch("announcementModule/loadAnnouncementTasks", this.$route.params.id);
              break;
          }
        }
      },
      loadAnnouncementDetails() {
        this.$store.dispatch("announcementModule/loadAnnouncementCalledUsers", this.$route.params.id);
        this.$store.dispatch("announcementModule/loadAnnouncementTutors", this.$route.params.id);
        if (this.materialsEnabled)
          this.$store.dispatch("materialCourseModule/getAnnouncementMaterials", this.$route.params.id);
        if (this.tasksEnabled)
          this.$store.dispatch("announcementModule/loadAnnouncementTasks", this.$route.params.id);
      },
      async loadAnnouncement() {
        await this.$store.dispatch("announcementModule/loadAnnouncement", this.$route.params.id);
        this.$store.dispatch("contentTitleModule/addRoute", {
          routeName: this.$route.name,
          params: {
            linkName: this.announcement?.course?.name,
            params: this.$route.params,
          },
        });
      },
      dateFormat(date) {
        if (!date || !date.length || isNaN(parseInt(`${new Date(date).getTime()}`))) return "";
        const dateObj = new Date(date);
        return `${String(dateObj.getDate()).padStart(2, "0")}/${String(dateObj.getMonth() + 1).padStart(2, "0")}/${dateObj.getFullYear()}`;
      }
    }
  };
  </script>
  