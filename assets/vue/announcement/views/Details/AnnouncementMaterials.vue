<template>
  <div class="AnnouncementMaterials">
    <div
      class="w-100 d-flex align-items-center justify-content-center flex-column"
      v-if="loading"
    >
      <spinner />
    </div>
    <div v-show="!loading">
      <div class="AnnouncementMaterials--header mt-3">
        <button class="btn btn-primary" @click="openMaterialCourseModal()">
          <i class="fa fa-plus mr-2"></i>{{ $t("ANNOUNCEMENT.ADD_MATERIAL") }}
        </button>
      </div>
      <div class="AnnouncementMaterials--content mb-3 pb-3">
        <DataNotFound
          :hide-on="!files.length"
          :text="$t('ANNOUNCEMENT.MATERIALSTAB.NOT_FOUND') || ''"
          icon="fa-book"
          :banner="true"
        />
        <table class="table table-condensed" v-show="files.length">
          <thead>
            <tr>
              <th style="width: 50px"></th>
              <th>{{ $t("NAME") }}</th>
              <th class="text-center text-nowrap">
                {{ $t("FILE_UPLOAD.FILE_TYPE") }}
              </th>
              <th class="text-center text-nowrap">{{ $t("DATE") }}</th>
              <th class="text-center">{{ $t("ACTIVE") }}</th>
              <th class="text-center">{{ $t("LIBRARY.COMMENTS.PREVIEW") }}</th>
              <th class="text-center">{{ $t("DOWNLOADABLE") }}</th>
              <th class="text-center" style="width: 90px">
                {{ $t("ACTIONS") }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(file, index) in files" :key="'fileMaterial-' + file.id">
              <td>
                <button
                  type="button"
                  class="btn btn-info"
                  @click="openIndex = index"
                >
                  <i class="fa" :class="fileIcons[file.type]"></i>
                </button>
                <viewer
                  :custom="false"
                  :base-path="basePath"
                  :file="file"
                  :modal="true"
                  :open="openIndex === index"
                  @close="openIndex = -1"
                />
              </td>
              <td>{{ file.name }}</td>
              <td class="text-center text-nowrap">
                {{ fileTypes[file.type]
                }}<span class="text-uppercase">{{ getExtension(index) }}</span>
              </td>
              <td class="text-center text-nowrap">
                <DateTimeTag :datetime="file.createdAt" />
              </td>
              <td class="text-center">
                <div class="custom-control custom-switch">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    :id="'switch_active_' + file.id"
                    v-model="file.isActive"
                    @change="changeActiveStatus(file)"
                  />
                  <label
                    class="custom-control-label"
                    :for="'switch_active_' + file.id"
                  ></label>
                </div>
              </td>
              <td class="text-center">
                <div
                  v-if="!file?.disableVisible"
                  class="custom-control custom-switch">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    :id="'switch_visibility_' + file.id"
                    v-model="file.isVisible"
                    :disabled="!file.isActive"
                    @change="changeVisibilityStatus(file)"
                  />
                  <label
                    class="custom-control-label"
                    :for="'switch_visibility_' + file.id"
                  ></label>
                </div>
                <span v-else>-</span>
              </td>
              <td class="text-center">
                <div
                  v-if="!file?.disableDownload"
                  class="custom-control custom-switch">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    :id="'c' + file.id"
                    v-model="file.isDownload"
                    :disabled="!file.isActive"
                    @change="changeDownloadStatus(file)"
                  />
                  <label
                    class="custom-control-label"
                    :for="'c' + file.id"
                  ></label>
                </div>
                <span v-else>-</span>
              </td>
              <td>
                <div class="d-flex align-items-center justify-content-evenly">
                  <button
                    type="button"
                    class="btn btn-danger ml-1"
                    @click="deleteMaterialCourse(file)"
                    v-if="file.typeOrigen !== 'CLONE_MATERIALS_COURSE'"
                  >
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Modal -->
    <div
      v-if="showModal"
      class="modal fade"
      id="materialCourseModal"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="materialCourseModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="materialCourseModalLabel">
              {{ $t("ANNOUNCEMENT.ADD_MATERIAL") }}
            </h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              @click="closeMaterialCourseModal()"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div id="materialCourse">
              <add-announcement-material
                :id="1"
                @cancel="closeMaterialCourseModal()"
                @success="onUploadSuccess()"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from "jquery";
import "bootstrap";
import { get } from "vuex-pathify";

import AddAnnouncementMaterial from "../../components/AddAnnouncementMaterial.vue";
import FilePreview from "../../../common/components/FilePreview.vue";
import Loader from "../../../admin/components/Loader.vue";
import Viewer from "../../../common/components/viewer/Viewer.vue";
import DateTimeTag from "../../components/details/dateTimeTag";
import DataNotFound from "../../components/details/DataNotFound";
import Spinner from "../../../admin/components/base/Spinner";

export default {
  name: "AnnouncementMaterials",
  components: {
    Spinner,
    DataNotFound,
    DateTimeTag,
  
    FilePreview,
    Viewer,
    Loader,
    AddAnnouncementMaterial,
  },
  data() {
    return {
      openIndex: -1,
      showModal: false,
      fileTypes: [
        "",
        "lectura ",
        "video ",
        "comprimido ",
        "imagen ",
        "documento ",
        "texto ",
      ],
      fileIcons: [
        "",
        "fa-file-pdf",
        "fa-file-video",
        "fa-file-archive",
        "fa-file-image",
        "fa-file-word",
        "fa-file-alt",
      ],
    };
  },
  computed: {
    announcement: get("announcementModule/announcement"),
    files: get("materialCourseModule/materials"),
    loading: get("materialCourseModule/loading"),
    basePath: get("materialCourseModule/fileBasePath"),
    canCreate() {
      if (this.announcement) {
        const current = new Date();
        const finishAt = new Date(this.announcement.finishAt);
        return current < finishAt;
      }
      return false;
    },
  },
  methods: {
    loadFiles() {
      this.$store.dispatch(
        "materialCourseModule/getAnnouncementMaterials",
        this.announcement.id
      );
    },

    changeDownloadStatus(file) {
      this.$store
        .dispatch("materialCourseModule/setMaterialCourseDownloadable", {
          id: file.id,
          downloadable: file.isDownload,
        })
        .then((res) => {
          const { error } = res;
          if (error) this.$toast.error( this.$t("MATERIAL_COURSE.DOWNLOADABLE.FAILED") );
          else this.$toast.success( this.$t("MATERIAL_COURSE.DOWNLOADABLE.SUCCESS"));
        });
    },
    
    async changeActiveStatus(file) {
      try {
        await this.$store.dispatch("materialCourseModule/setMaterialCourseActive", {
          id: file.id,
          isActive: file.isActive,
          announcement: this.announcement.id
        });
        await this.activePreviewAndDownload(file);
        this.$toast.success(this.$t("MATERIAL_COURSE.DOWNLOADABLE.SUCCESS"));
      } catch (error) {
        this.$toast.error(this.$t("MATERIAL_COURSE.DOWNLOADABLE.FAILED"));
      }
    },

    async changeVisibilityStatus(file) {

      try{

        await this.$store.dispatch("materialCourseModule/setMaterialCourseVisibitily", {
          id: file.id,
          visibitily: file.isVisible,
          announcement: this.announcement.id
        })

        this.$toast.success(this.$t("MATERIAL_COURSE.DOWNLOADABLE.SUCCESS"));
      }catch (error){
        this.$toast.error(this.$t("MATERIAL_COURSE.DOWNLOADABLE.FAILED"));
      }

    },

    deleteMaterialCourse(file) {
      this.$alertify.confirmWithTitle(
        this.$t("MATERIAL_COURSE.DELETE.CONFIRM.TITLE"),
        this.$t("MATERIAL_COURSE.DELETE.CONFIRM.DESCRIPTION"),
        () => {
          this.$store
            .dispatch("materialCourseModule/deleteMaterialCourse", file.id)
            .then((res) => {
              const { error, data } = res;
              if (error) {
                this.$toast.error(
                  this.$t("MATERIAL_COURSE.DELETE.FAILED") + ""
                );
              } else {
                this.$toast.success(
                  this.$t("MATERIAL_COURSE.DELETE.SUCCESS") + ""
                );
                this.loadFiles();
              }
            });
        },
        () => {}
      );
    },

    openMaterialCourseModal() {
      this.showModal = true;
      this.$nextTick(() => {
        $("#materialCourseModal").modal({
            show: true,
            static: true,
            backdrop: false,
            keyboard: false,
        });
      });
    },

    closeMaterialCourseModal() {
      this.showModal = false;
      $("#materialCourseModal").modal("hide");
    },

    onUploadSuccess() {
      this.closeMaterialCourseModal();
      this.loadFiles();
    },

    getExtension(index) {
      const splitted = (this.files[index].name || "").split(".");
      return splitted[splitted.length - 1] || "";
    },
    async activePreviewAndDownload(file){
      try {
        await this.$store.dispatch("materialCourseModule/setMaterialCourseVisibitily", {
          id: file.id,
          visibitily: file.isActive,
          announcement: this.announcement.id
        })

        const data = {
          id: file.id,
          downloadable: file.isActive,
        };

        await this.$store.dispatch("materialCourseModule/setMaterialCourseDownloadable", data);

        $("#switch_visibility_" + file.id).prop('checked', file.isActive);
        $("#c" + file.id).prop('checked', file.isActive);
      } catch (error) {
        this.$toast.error( this.$t("MATERIAL_COURSE.DOWNLOADABLE.FAILED"));
      }
    },
  },
};
</script>

 <style scoped lang="scss"> 
.AnnouncementMaterials {
  width: 100%;

  &--header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .AnnouncementMaterials--content {
    overflow-x: auto;
  }

  tr > th {
    white-space: nowrap;
  }

  .btn-info {
    background-color: var(--color-primary-light) !important;

    &:hover {
      color: var(--color-primary) !important;
    }
  }

  .subtitle {
    color: var(--color-neutral-mid-darker);
    font-size: 0.9rem;
  }

  &--content {
    margin-top: 1rem;

    &--files {
      display: grid;
      grid-template-columns: repeat(auto-fit, 200px);
      gap: 0.25rem;
    }
  }
}
</style>
