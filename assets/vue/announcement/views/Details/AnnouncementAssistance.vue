<template>
<div class="AnnouncementAssistance oversize">
  <div class="w-100 d-flex align-items-center justify-content-center flex-column" v-if="loading">
    <spinner />
  </div>
  <DataNotFound
      :hide-on="!loading && !assistanceUsers.length"
      :text="$t('ANNOUNCEMENT.STUDENTTAB.NOT_FOUND') || ''"
      icon="fa-users"
      :banner="true" />
  <div class="row" v-show="!loading && assistanceUsers.length">
    <accordion-content
        v-show="group.sessions.length"
        v-for="(group, index) in assistanceUsers"
        icon="fa-users"
        :group-id="group.groupInfo?.id"
        :title="group.name || ''"
        :badge="`${$t('ANNOUNCEMENT.SESSIONS')}: ${group.sessions.length}`"
        :show-report-button="false"
        :opened="!index"
        :key="index"
        >
      <assistance-table
          :session-list="group.sessions"
          class="col-12 pb-3"
          :is-virtual="isVirtual"
          :group-id="group.groupInfo?.id"
          :tag="'group' + (index + 1) + '-assistance'"/>
    </accordion-content>
  </div>
</div>
</template>

<script>
import AccordionContent from "../../components/details/accordionContent";
import AssistanceTable  from "../../components/details/assistanceTable";
import {get}            from "vuex-pathify";
import Spinner          from "../../../admin/components/base/Spinner";
import DataNotFound     from "../../components/details/DataNotFound";
export default {
  name: "AnnouncementAssistance",
  components: {DataNotFound, Spinner, AssistanceTable, AccordionContent},
  computed: {
    announcement: get('announcementModule/announcement'),
    assistanceUsers: get('announcementModule/assistanceUsers'),
    loading: get('announcementModule/loadingAssistanceUsers'),
    typeCourse() {
      // [1] Teleformación [2] Presencial [3] Mixto [4] Aula Virtual
      return (this.announcement?.course) ? this.announcement.course.typeID : 1;
    },
    isVirtual() {
      return this.typeCourse === 4
    },
  },
  mounted() {
    this.$store.dispatch('announcementModule/loadAnnouncementAssistanceUsers', this.$route.params.id);
  },
  methods: {

  }
}
</script>

 <style scoped lang="scss"> 
.AnnouncementAssistance {
  margin: 0 auto;

  .assistanceTable {
    padding: 0 3rem;
  }

  .row {
    gap: 1rem;
    @media (max-width: 576px) {
      margin: 0 auto;
    }
  }
}
</style>
