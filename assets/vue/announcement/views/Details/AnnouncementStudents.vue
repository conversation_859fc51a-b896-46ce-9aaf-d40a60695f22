<template>
<div class="AnnouncementStudents">
  <div class="w-100 d-flex align-items-center justify-content-center flex-column" v-if="loading">
    <spinner />
  </div>
  <DataNotFound
      :hide-on="!loading && !groupStudents.length"
      :text="$t('ANNOUNCEMENT.STUDENTTAB.NOT_FOUND') || ''"
      icon="fa-users"
      :banner="true" />
  <fragment-content
      tag="studentsList"
      :title="$t('ANNOUNCEMENT.NUMBER_OF_USER_CALLED_NO_NUMBER') || ''"
      :details="[]"
      class="oversize"
      :badge="totalUsers"
      v-show="!loading && groupStudents.length"
  >
    <div class="row">
      <accordion-content
          v-for="(group, index) in groupStudents"
          icon="fa-users"
          :group-id="group.groupInfo?.id || 0"
          :title="group.name || ''"
          :badge="`${$t('ANNOUNCEMENT.PEOPLE')}: ${group.total}`"
          :search="true"
          @on-search="searchStudentByGroup(group.groupInfo.id, $event)"
          :opened="!index"
          :hasReportZip="hasReportZip"
          >
        <template v-slot:extra-actions v-if="hasReportZip">
          <button type="button" class="btn btn-sm btn-info mr-1"
                  data-bs-toggle="modal"
                  :data-bs-target="`#modal-announcement-group-${group.groupInfo?.id}-reports`">
            {{ $t('REPORT') }}
          </button>
        </template>
        <template v-slot:default>
          <students-table
              :student-list="group.users || []"
              :query="queries['g' + group.groupInfo?.id]"
              class="col-12 pb-3"
              :tag="'group' + (index + 1) + '-students'"/>
        </template>
      </accordion-content>
    </div>
  </fragment-content>

  <BaseModal
      v-for="(group, index) in groupStudents"
      :identifier="`modal-announcement-group-${group.groupInfo?.id}-reports`"
      :title="$t('REPORT') + ''"
  >
    <div class="AnnouncementModalGroupReports">
      <table class="table">
        <thead>
        <tr>
          <th>{{ $t('FILE') }}</th>
          <th>{{ $t('CREATED_BY') }}</th>
          <th>{{ $t('CREATED_AT') }}</th>
          <th>{{ $t('STATUS') }}</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="r in groupsReports[group.groupInfo?.id + '']" :key="`group-report-${group.groupInfo?.id}-report-${r.taskId}`">
          <td>
            <a v-if="zipIsCompleted(r.status)" :href="`/files/${r.filename}`" target="_blank">{{ r.originalName }}</a>
            <span v-else>
              {{ r.originalName }}
            </span>
          </td>
          <td>{{ r.createdByName }}</td>
          <td>{{ getDateTimeFormatted(r.createdAt) }}</td>
          <td class="text-center">
            <loader style="padding: 0 !important;" v-if="zipIsInProgress(r.status) || zipIsPending(r.status)" :is-loaded="zipIsInProgress(r.status)" />
            <span v-if="zipIsCompleted(r.status)" class="badge bg-success text-white">{{ $t('STATUS_INFORMATION.COMPLETED') }}</span>
            <span v-if="zipIsFailed(r.status)" class="badge bg-danger text-white">{{ $t('ERROR') }}</span>
          </td>
        </tr>
        <tr v-if="(groupsReports[group.groupInfo?.id + ''] ?? []).length === 0">
          <td colspan="3" class="text-center">
            {{ $t('NO_DATA') }}
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  </BaseModal>
</div>
</template>

<script>
import zipFileTaskStatusMixin from "../../../common/constants/ZipFileTaskStatus";

import StudentsTable    from "../../components/details/studentsTable";
import AccordionContent from "../../components/details/accordionContent";
import FragmentContent  from "../../components/details/fragmentContent";
import {get, sync} from "vuex-pathify";
import Spinner          from "../../../admin/components/base/Spinner";
import DataNotFound     from "../../components/details/DataNotFound";

import Loader from "../../../admin/components/Loader.vue";
import dateTimeFormatterMixin from "../../../common/mixins/dateTimeFormatterMixin";
export default {
  name: "AnnouncementStudents",
  components: {Loader,  DataNotFound, Spinner, FragmentContent, AccordionContent, StudentsTable},
  mixins: [zipFileTaskStatusMixin, dateTimeFormatterMixin],
  data() {
    return {
      intervalId: null,
      queries: {}
    };
  },
  computed: {
    announcement: get('announcementModule/announcement'),
    groupStudents: get('announcementModule/calledUsers'),
    loading: get('announcementModule/loadingCalledUsers'),
    groupsReports: get('announcementReportModule/groupsReports'),
    totalUsers () {
      return `${(this.groupStudents || []).reduce((acc, cur) => acc + cur.total, 0)}`
    },

    hasReportZip(){
      return this.announcement?.hasReportZip ?? false;
    }
  },
  watch: {
    loading: {
      immediate: true,
      handler: function () {
        if (this.intervalId == null && !this.loading) {
          this.$store.dispatch('announcementReportModule/loadAnnouncementGroupReports', this.announcement.id);
          this.intervalId = setInterval(() => {
            this.$store.dispatch('announcementReportModule/loadAnnouncementGroupReports', this.announcement.id);
          }, 2 * 60 * 1000); // Every 2 minutes
        }
      }
    }
  },
  beforeDestroy() {
    if (this.intervalId != null) clearInterval(this.intervalId);
  },
  methods: {
    // getGroupReports(id) {
    //   const groupInfo = this.groupsReports.find(g => g.id === id);
    //   return groupInfo?.reports ?? [];
    // },
    downloadFile(id) {
      if (id == null || id < 1) return;
      this.$store.dispatch('announcementModule/downloadAnnouncementGroupReport', id)
    },

    searchStudentByGroup(groupId, e) {
      const t = structuredClone(this.queries);
      t['g' + groupId] = e;
      this.queries = t;
    }
  }
}
</script>

 <style scoped lang="scss"> 
.AnnouncementStudents {
  .row {
    gap: 1rem;
  }

  :deep(.accordionBody) {
    min-height: 12rem;
  }
}
</style>
