<template>
<div class="AnnouncementTaskDetails p-4">
  <div class="w-100 d-flex align-items-center justify-content-center flex-column" v-if="loading">
    <spinner />
  </div>
  <div v-show="!loading">
    <div class="d-flex align-items-center justify-content-between mb-4 flex-column flex-sm-row">
      <h5><i class="fa fa-calendar"></i> <PERSON><PERSON><PERSON> de {{ user.name }}</h5>

      <router-link :to="{ name: 'ViewAnnouncement', params: { id: announcement?.id }}">
        <button class="btn btn-sm btn-primary"><i class="fa fa-chevron-left mr-2"></i> Volver a alumnado</button>
      </router-link>
    </div>
    <TaskDetails :tasks="tasks" :user-id="user.id" :allow-open="true"/>
    <PreviewModal name="task" :file="fileSelected"/>
  </div>
</div>
</template>

<script>
import {get}         from "vuex-pathify";
import TaskAccordion from "../../components/details/taskAccordion";
import TaskDetails   from "../../components/details/taskDetails";
import Spinner      from "../../../admin/components/base/Spinner";
import PreviewModal from "../../../common/components/visors/previewModal";

export default {
  name: "AnnouncementTaskDetails",
  components: {PreviewModal, Spinner, TaskDetails, TaskAccordion},
  computed: {
    announcement: get('announcementModule/announcement'),
    refresh: get('announcementModule/refresh'),
    loading: get('announcementModule/loadingCalledUsers'),
    groupStudents: get('announcementModule/calledUsers'),
    user: get('announcementModule/userSelected'),
    fileSelected: get('announcementModule/fileSelected'),
    tasks() {
      const { comunications } = this.user;
      return comunications ? comunications.tasks : [];
    },
  },
  watch: {
    refresh: {
      handler: function (val, oldVal) {
        this.handleRefresh();
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    if (!this.announcement || this.announcement.id !== this.$route.params.id || this.refresh.action === 'announcement') {
      this.loadAnnouncement();
    } else this.setUserSelected();
  },
  methods: {
    async loadAnnouncement() {
      await this.$store.dispatch('announcementModule/loadAnnouncement', this.$route.params.id);
      await this.$store.dispatch('announcementModule/loadAnnouncementCalledUsers', this.$route.params.id);
      this.setUserSelected();
    },

    async handleRefresh() {
      if (!this.refresh.refresh) return;

      if (this.refresh.action === null && this.refresh.refresh) {
        await this.loadAnnouncement();
      } else {
        switch (this.refresh.action) {
          case 'users':
            await this.$store.dispatch('announcementModule/loadAnnouncementCalledUsers', this.$route.params.id);
            break;
          case 'tutors':
            await this.$store.dispatch('announcementModule/loadAnnouncementTutors', this.$route.params.id);
            break;
          case 'materials':
            await this.$store.dispatch('materialCourseModule/getAnnouncementMaterials', this.$route.params.id);
            break;
          case 'tasks':
            await this.$store.dispatch('announcementModule/loadAnnouncementTasks', this.$route.params.id);
            break;
        }
      }
    },

    setUserSelected() {
      this.groupStudents.find((group) => {
        return group.users.findIndex((user) => {
          if (`${user.id}` === `${this.$route.params.studentId}`) {
            this.$store.dispatch("announcementModule/setUserSelected", {...user});
            return true;
          }
          return false
        }) > -1;
      });
    }
  }
}
</script>

 <style scoped lang="scss"> 
.AnnouncementTaskDetails { }
</style>
