<template>
  <div class="AnnouncementShare p-3 p-sm-0">
    <div class="w-100 d-flex align-items-center justify-content-center flex-column" v-if="loading">
      <spinner />
    </div>
    <div v-show="!loading">
      <DataNotFound :hide-on="!managers.length" :text="$t('ANNOUNCEMENT.SHARETAB.NOT_FOUND') || ''" icon="fa-link"
        :banner="true" />
      <div class="row">
       <!--  <div v-for="opinion in opinions" :key="'opinion_' + opinion.id + opinion.created_at"
          class="col-lg-4 col-md-6 col-sm-12 pt-3">
          <Opinion :opinion="opinion" @on-opinion-visibility-change="$emit('on-opinion-visibility-change', $event)"
            @on-opinion-highlight-change="$emit('on-opinion-highlight-change', $event)"
            @open-details="setCurrentOpinion" />
        </div> -->
      </div>
      <OpinionDetails :opinion="currentOpinion" />
    </div>
  </div>
</template>

<script>
import Opinion from "../../../common/components/opinions/Opinion";
import OpinionDetails from '../../../admin/components/Opinions/OpinionDetails.vue'
import { get } from "vuex-pathify";
import DataNotFound from "../../components/details/DataNotFound";
import Spinner from "../../../admin/components/base/Spinner";

export default {
  name: "AnnouncementShare",
  components: { Spinner, DataNotFound, Opinion, OpinionDetails },
  data() {
    return {
      loading: true,
      opinions: [],
      managers: [],
      currentOpinion: {},
    };
  },
  computed: {
    announcement: get("announcementModule/announcement"),
  },
  created() {
    //this.loadOpinions();
    this.loadAnnouncementManagers();
  },
  methods: {

    async loadAnnouncementManagers() {
      this.loading = true;
      try {
        const { data, error } = await this.$store.dispatch(
          "announcementModule/loadAnnouncementManagers",
          this.announcement.id
        );
        if (data.items) {
          this.managers = data.items;
        }
      } catch (error) {
        console.error(error);
      } finally {
        this.loading = false;
      }
    },
    /* async loadOpinions() {
      this.loading = true;
      try {
        const { data, error } = await this.$store.dispatch(
          "announcementModule/loadAnnouncementOpinions",
          this.announcement.id
        );
        this.opinions = data.items.map(item => {
          item.visibleOptions = !(!item.text.length && data.hideEmptyOpinions)
          return item;
        })
      } finally {
        this.loading = false;
      }
    }, */
  },
};
</script>

<style scoped lang="scss">
.AnnouncementShare {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 1rem;

  @media (max-width: 576px) {
    margin: 0 auto;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}
</style>
