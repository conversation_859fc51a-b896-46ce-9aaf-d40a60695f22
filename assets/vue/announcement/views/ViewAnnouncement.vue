<template>
  <div
    class="d-flex align-items-center justify-content-center"
    v-if="isLoading"
  >
    <spinner />
  </div>
  <div class="ViewAnnouncement" v-else>
    <div
      class="hideOnPrint col-12 d-flex justify-content-between align-content-center p-4 flex-column flex-md-row"
    >
      <div>
        <b
          ><i class="fa fa-calendar"></i>
          {{ $t("ANNOUNCEMENT.GENERAL.NAME", [announcement?.course?.name]) }}</b
        >
        <span class="text-detail"
          >({{ announcement?.dateFrom }} - {{ announcement?.dateTo }})</span
        >
      </div>
      <button
        class="btn btn-primary btn-sm align-self-center"
        data-bs-toggle="modal"
        v-if="announcement.notifiedAt == null"
        data-bs-target="#modalActivateAnnouncement"
      >
        {{ $t("ANNOUNCEMENT.GENERAL.ACTIVATE") }}
      </button>
      <button
        class="btn btn-primary btn-sm align-self-center"
        data-bs-toggle="modal"
        v-if="isValidateCancel"
        data-bs-target="#modalCancelAnnouncement"
      >
        {{ $t("ANNOUNCEMENT.GENERAL.CANCEL") }}
      </button>
    </div>

    <div class="ViewAnnouncement--content">
      <ul class="nav nav-tabs ps-4 user-select-none hideOnPrint">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            :class="activePane === 'info' ? 'active' : ''"
            id="info-tab"
            @click="activePane = 'info'"
          >
            <i class="fa fa-file"></i> {{ $t("ANNOUNCEMENT.INFO") }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            :class="activePane === 'students' ? 'active' : ''"
            id="students-tab"
            @click="activePane = 'students'"
          >
            <i class="fa fa-user"></i>
            <span class="mx-2">{{ $t("ANNOUNCEMENT.STUDENTS") }}</span>
            <i class="fa fa-exclamation text-danger" v-if="usersWithAlerts"></i>
          </button>
        </li>
        <li class="nav-item" role="presentation" v-if="!isTeleformation">
          <button
            class="nav-link"
            :class="activePane === 'presential' ? 'active' : ''"
            id="presential-tab"
            @click="activePane = 'presential'"
          >
            <i class="fa fa-user-check"></i> {{ $t("ANNOUNCEMENT.ATTENDANCE") }}
          </button>
        </li>
        <li class="nav-item" role="presentation" v-if="showTasks">
          <button
            class="nav-link"
            :class="activePane === 'tasks' ? 'active' : ''"
            id="tasks-tab"
            @click="activePane = 'tasks'"
          >
            <i class="fa fa-tasks"></i> {{ $t("ANNOUNCEMENT.TASKS") }}
          </button>
        </li>
        <li class="nav-item" role="presentation" v-if="showMaterials">
          <button
            class="nav-link"
            :class="activePane === 'materials' ? 'active' : ''"
            id="materials-tab"
            @click="activePane = 'materials'"
          >
            <i class="fa fa-book"></i> {{ $t("ANNOUNCEMENT.MATERIAL") }}
          </button>
        </li>
        <li class="nav-item" role="presentation" v-if="showForum">
          <button
            class="nav-link"
            :class="activePane === 'forum' ? 'active' : ''"
            id="forum-tab"
            @click="activePane = 'forum'"
          >
            <i class="fa fa-comments"></i> {{ $t("ANNOUNCEMENT.FORUM") }}
          </button>
        </li>
        <li class="nav-item" role="presentation" v-if="showChat">
          <button
            class="nav-link"
            :class="activePane === 'chat' ? 'active' : ''"
            id="chat-tab"
            @click="activePane = 'chat'"
          >
            <i class="fa fa-comments"></i> {{ $t("ANNOUNCEMENT.INFOTAB.CHAT") }}
          </button>
        </li>
        <li class="nav-item" role="presentation" v-if="showNotification">
          <button
            class="nav-link"
            :class="activePane === 'notifications' ? 'active' : ''"
            id="notifications-tab"
            @click="activePane = 'notifications'"
          >
            <i class="fa fa-bell"></i> {{ $t("ANNOUNCEMENT.NOTIFICATIONS") }}
          </button>
        </li>
        <li class="nav-item" role="presentation" v-if="showStats">
          <button
            class="nav-link"
            :class="activePane === 'stats' ? 'active' : ''"
            id="stats-tab"
            @click="activePane = 'stats'"
          >
            <i class="fa fa-bar-chart"></i> {{ $t("ANNOUNCEMENT.STATS") }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            :class="activePane === 'opinions' ? 'active' : ''"
            id="opinions-tab"
            @click="activePane = 'opinions'"
          >
            <i class="fa fa-star"></i> {{ $t("ANNOUNCEMENT.OPINIONS") }}
          </button>
        </li>
        <li class="nav-item" role="presentation" v-if="showShare">
          <button
            class="nav-link"
            :class="activePane === 'share' ? 'active' : ''"
            id="share-tab"
            @click="activePane = 'share'"
          >
            <i class="fa fa-link"></i> {{ $t("ANNOUNCEMENT.SHARE") }}
          </button>
        </li>
      </ul>

      <div class="tab-content bg-white p-1 p-sm-4" v-if="announcement.id">
        <div
          class="tab-pane fade config"
          :class="activePane === 'info' ? 'active show' : ''"
        >
          <AnnouncementInfo />
        </div>
        <div
          class="tab-pane fade students"
          :class="activePane === 'students' ? 'active show' : ''"
        >
          <AnnouncementStudents />
        </div>
        <div
          v-if="!isTeleformation"
          class="tab-pane fade presential"
          :class="activePane === 'presential' ? 'active show' : ''"
        >
          <AnnouncementAssistance />
        </div>
        <div
          v-if="showTasks"
          class="tab-pane fade materials"
          :class="activePane === 'tasks' ? 'active show' : ''"
        >
          <AnnouncementTasks />
        </div>
        <div
          v-if="showMaterials"
          class="tab-pane fade materials"
          :class="activePane === 'materials' ? 'active show' : ''"
        >
          <AnnouncementMaterials />
        </div>
        <div
          v-if="showForum"
          class="tab-pane fade materials"
          :class="activePane === 'forum' ? 'active show' : ''"
        >
          <AnnouncementForum />
        </div>
        <div
          v-if="showChat"
          class="tab-pane fade materials"
          :class="activePane === 'chat' ? 'active show' : ''"
        >
          <AnnouncementChat />
        </div>
        <div
          v-if="showNotification"
          class="tab-pane fade materials"
          :class="activePane === 'notifications' ? 'active show' : ''"
        >
          <AnnouncementNotifications />
        </div>
        <div
          v-if="showStats"
          class="tab-pane fade materials"
          :class="activePane === 'stats' ? 'active show' : ''"
        >
          <CourseStatsDetails
            :course_id="announcement.course.id"
            :course_data="announcement.course.courseDataDetailStats"
            :announcementId="announcement.id"
          />
        </div>
        <div
          class="tab-pane fade materials"
          :class="activePane === 'opinions' ? 'active show' : ''"
        >
          <AnnouncementOpinions
            @on-opinion-visibility-change="updateOpinion($event)"
            @on-opinion-highlight-change="highlightOpinion($event)"
          />
        </div>
         <div
          class="tab-pane fade materials"
          :class="activePane === 'share' ? 'active show' : ''"
        >
         <AnnouncementShare />
        </div>
      </div>

      <ModalUserConection />
      <ModalUserDetails />
      <ModalAlerts />
      <ModalChats />
      <ModalUserNotifications />
      <ModalUserAssistances />
      <ModalUserProgress />
      <ModalActivateAnnouncement />
      <user-fields-fundae-modal />
      <user-email-validation-account-modal />
      <ModalInspectorReports
        :announcement-id="announcement.id"
        :group-id="groupSelected.id"
        :announcement-type="announcement.type"
      />
      <ModalSessionAssistanceFiles />
      <modalCancelAnnouncement />
    </div>
  </div>
</template>

<script>
import { get, sync } from "vuex-pathify";
import axios from "axios";

import AnnouncementConfig from "../components/AnnouncementConfig.vue";
import AnnouncementForum from "./Details/AnnouncementForum.vue";
import AnnouncementMaterials from "./Details/AnnouncementMaterials.vue";
import AnnouncementOpinions from "./Details/AnnouncementOpinions.vue";
import AnnouncementTasks from "./Details/AnnouncementTasks.vue";
import AnnouncementTutors from "./ViewTabs/AnnouncementTutors.vue";
import AnnouncementShare from "./Details/AnnouncementShare.vue";
import Observations from "../components/Observations.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import AnnouncementInfo from "./Details/AnnouncementInfo";
import AnnouncementStudents from "./Details/AnnouncementStudents";
import ModalUserConection from "../components/details/modals/modalUserConection";
import ModalUserDetails from "../components/details/modals/modalUserDetails";
import ModalAlerts from "../components/details/modals/modalAlerts";
import ModalChats from "../components/details/modals/modalChats";
import AnnouncementAssistance from "./Details/AnnouncementAssistance";
import ModalUserAssistances from "../components/details/modals/modalUserAssistances";
import ModalUserProgress from "../components/details/modals/modalUserProgress";
import AnnouncementNotifications from "./Details/AnnouncementNotifications";
import ModalUserNotifications from "../components/details/modals/modalUserNotifications";
import ModalActivateAnnouncement from "../components/details/modals/modalActivateAnnouncement";
import ModalCancelAnnouncement from "../components/details/modals/modalCancelAnnouncement";
import ModalInspectorReports from "../../inspectorFundae/components/modalInspectorReports";

import UserFieldsFundaeModal from "../components/UserFieldsFundaeModal.vue";
import UserEmailValidationAccountModal from "../components/UserEmailValidationAccountModal";

import AnnouncementChat from "./Details/AnnouncementChat.vue";
import ModalSessionAssistanceFiles from "../components/details/ModalSessionAssistanceFiles.vue";
import { ANNOUNCEMENT_SOURCE } from "../store/module/announcementForm/common";

import CourseStatsDetails from "../../admin/course/CourseStatsDetails.vue";

export default {
  name: "ViewAnnouncement",
  components: {
    UserFieldsFundaeModal,
    UserEmailValidationAccountModal,
    ModalSessionAssistanceFiles,

    AnnouncementChat,
    ModalInspectorReports,
    ModalActivateAnnouncement,
    ModalCancelAnnouncement,
    ModalUserNotifications,
    AnnouncementNotifications,
    ModalUserProgress,
    ModalUserAssistances,
    AnnouncementAssistance,
    ModalChats,
    ModalAlerts,
    ModalUserDetails,
    ModalUserConection,
    AnnouncementStudents,
    AnnouncementInfo,
    AnnouncementConfig,
    AnnouncementForum,
    AnnouncementMaterials,
    AnnouncementOpinions,
    AnnouncementTasks,
    AnnouncementTutors,
    AnnouncementShare,
    Observations,
    Spinner,
    CourseStatsDetails,
  },

  data() {
    return {
      startAt: undefined,
      finishAt: undefined,

      loadingTutors: true,
      tutors: [],

      loadingTasks: true,
      tasks: [],
    };
  },

  computed: {
    activePane: sync("announcementModule/activePane"),
    announcement: get("announcementModule/announcement"),
    groupSelected: get("announcementModule/groupSelected"),
    isLoading: get("announcementModule/isLoading"),
    useGlobalEventBus: get("contentTitleModule/getUseGlobalEventBus"),
    refresh: get("announcementModule/refresh"),
    materialsEnabled: get("configModule/config@materialsEnabled"),
    tasksEnabled: get("configModule/config@tasksEnabled"),
    shareEnabled: get(""),
    groupStudents: get("announcementModule/calledUsers"),
    uncompletedUserProfile: get("announcementModule/uncompletedUserProfile"),
    typeCourse() {
      // [1] Teleformación [2] Presencial [3] Mixto [4] Aula Virtual
      return this.announcement?.course ? this.announcement.course.typeID : 1;
    },
    isTeleformation() {
      return this.typeCourse === 1;
    },
    isTeleformationOrMixte() {
      return this.typeCourse === 1 || this.typeCourse === 3;
    },
    showForum() {
      return (
        this.isTeleformationOrMixte && this.announcement?.comunications?.FORUM
      );
    },
    showChat() {
      return (
        this.isTeleformationOrMixte && this.announcement?.comunications?.CHAT
      );
    },
    showNotification() {
      return (
        this.isTeleformationOrMixte &&
        this.announcement?.comunications?.NOTIFICATION
      );
    },
    showStats() {
      return this.isTeleformationOrMixte;
    },

    showTasks() {
      return this.isTeleformationOrMixte && this.announcement?.hasTasks;
    },

    showMaterials() {
      return this.isTeleformationOrMixte && this.announcement?.hasMaterials;
    },

    showShare() {
      return this.announcement?.shareEnabled;
    },

    usersWithAlerts() {
      return (this.groupStudents || []).some((group) =>
        (group.users || []).some((user) => user.comunications?.alerts?.length)
      );
    },
    isValidateCancel() {
      const start_date = new Date(this.announcement?.start_at);
      const hoy = new Date();
      let validate = false;
      if (
        hoy < start_date &&
        this.announcement?.notifiedAt != null &&
        this.announcement?.status === "ACTIVE"
      ) {
        validate = true;
      }
      return validate;
    },
  },

  watch: {
    announcement: {
      handler: function (val, oldVal) {
        this.initView();
        this.announcement.dateTo = this.dateFormat(
          this.announcement?.finishAt || ""
        );
        this.announcement.dateFrom = this.dateFormat(
          this.announcement?.startAt || ""
        );
      },
      deep: true,
    },
    refresh: {
      handler: function (val, oldVal) {
        this.handleRefresh();
      },
      deep: true,
      immediate: true,
    },
  },

  async created() {
    if (
      !this.announcement ||
      this.announcement.id !== this.$route.params.id ||
      this.refresh.action === "announcement"
    ) {
      await this.loadAnnouncement();
    }
    this.$store.dispatch("announcementModule/getFundaeCatalogs");
  },

  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on("onDeleteAnnouncement", (e) => {
        console.log("delete");
      });
      this.$eventBus.$on("onEditAnnouncement", (e) => {
        const routeName =
          this.announcement.course.source.toUpperCase() ===
          ANNOUNCEMENT_SOURCE.EXTERN.toUpperCase()
            ? "UpdateAnnouncementExtern"
            : "UpdateAnnouncement";

        this.$router.push({
          name: routeName,
          params: { id: this.$route.params.id },
        });
      });
      this.$eventBus.$on("onReportAnnouncement", (e) => {
        this.downloadReport();
      });
    }
  },

  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off("onDeleteAnnouncement");
    }
    if (this.useGlobalEventBus) {
      this.$eventBus.$off("onEditAnnouncement");
    }
    if (this.useGlobalEventBus) {
      this.$eventBus.$off("onReportAnnouncement");
    }
  },
  methods: {
    initView() {
      this.$store.dispatch("contentTitleModule/addRoute", {
        routeName: this.$route.name,
        params: {
          linkName: this.$route.params?.isFromCourseDetails ? this.$t('ANNOUNCEMENT.TITLE') : this.announcement.course.name,
          params: this.$route.params,
        },
      });

      const actions = [];
      if (this.$isGranted("ROLE_MANAGER"))
        actions.push({
          name: this.$t("EDIT"),
          event: "onEditAnnouncement",
          class: "btn btn-primary",
        });

      this.$store.dispatch("contentTitleModule/setActions", {
        route: this.$route.name,
        actions,
      });

      this.loadAnnouncementDetails();
    },

    async handleRefresh() {
      if (!this.refresh.refresh) return;

      if (this.refresh.action === null && this.refresh.refresh) {
        await this.loadAnnouncement();
        this.initView();
      } else {
        switch (this.refresh.action) {
          case "users":
            await this.$store.dispatch(
              "announcementModule/loadAnnouncementCalledUsers",
              this.$route.params.id
            );
            break;
          case "tutors":
            await this.$store.dispatch(
              "announcementModule/loadAnnouncementTutors",
              this.$route.params.id
            );
            break;
          case "materials":
            await this.$store.dispatch(
              "materialCourseModule/getAnnouncementMaterials",
              this.$route.params.id
            );
            break;
          case "tasks":
            await this.$store.dispatch(
              "announcementModule/loadAnnouncementTasks",
              this.$route.params.id
            );
            break;

            /* case "share" ? */
        }
      }
    },

    loadAnnouncementDetails() {
      this.$store.dispatch(
        "announcementModule/loadAnnouncementCalledUsers",
        this.$route.params.id
      );
      this.$store.dispatch(
        "announcementModule/loadAnnouncementTutors",
        this.$route.params.id
      );
      if (this.materialsEnabled)
        this.$store.dispatch(
          "materialCourseModule/getAnnouncementMaterials",
          this.$route.params.id
        );
      if (this.tasksEnabled)
        this.$store.dispatch(
          "announcementModule/loadAnnouncementTasks",
          this.$route.params.id
        );

      /* if (this.shareEnabled) */
    },

    deleteAnnouncement() {
      this.$alertify.confirmWithTitle(
        this.$t("ANNOUNCEMENT.DELETE.CONFIRM.TITLE"),
        this.$t("ANNOUNCEMENT.DELETE.CONFIRM.DESCRIPTION"),
        () => {
          this.$store
            .dispatch(
              "announcementModule/deleteAnnouncement",
              this.$route.params.id
            )
            .then((res) => {
              const { error } = res;
              if (error)
                this.$toast.error(this.$t("ANNOUNCEMENT.DELETE.FAILED") + "");
              else {
                this.$toast.error(this.$t("ANNOUNCEMENT.DELETE.SUCCESS") + "");
                if (this.useGlobalEventBus) {
                  this.$eventBus.$emit("go-back");
                }
              }
            });
        },
        () => {}
      );
    },

    async downloadReport() {
      const pdf = await this.$store.dispatch(
        "announcementModule/downloadAnnouncementReport",
        this.$route.params.id
      );

      const link = document.createElement("a");
      link.href = pdf;
      link.download = `${this.$t("ANNOUNCEMENT.TITLE")}.pdf`;
      link.click();
    },

    async loadAnnouncement() {
      if (this.$route.params.origin !== undefined) {
        await this.$store.dispatch("contentTitleModule/addRoute", {
          routeName: "Home",
          params: {
            linkName: this.$t("ANNOUNCEMENTS"),
            params: {},
          },
        });
      }

      await this.$store.dispatch(
        "announcementModule/loadAnnouncement",
        this.$route.params.id
      );
    },

    dateFormat(date) {
      if (
        !date ||
        !date.length ||
        isNaN(parseInt(`${new Date(date).getTime()}`))
      )
        return "";
      const dateObj = new Date(date);
      return `${String(dateObj.getDate()).padStart(2, "0")}/${String(
        dateObj.getMonth() + 1
      ).padStart(2, "0")}/${dateObj.getFullYear()}`;
    },

    updateOpinion(data) {
      axios
        .patch(`/admin/nps/opinion/${data.id}/to-post`, {
          toPost: data.visible,
        })
        .then((r) => {
          const { error } = r.data;
          this.$toast.clear();
          if (error) this.$toast.error(this.$t("OPINION.TO_POST.FAILED") + "");
          else this.$toast.success(this.$t("OPINION.TO_POST.SUCCESS") + "");
        });
    },

    highlightOpinion(data) {
      axios
        .patch(`/admin/nps/opinion/${data.id}/highlight`, {
          toPost: data.highlight,
        })
        .then((r) => {
          const { error } = r.data;
          this.$toast.clear();
          if (error)
            this.$toast.error(this.$t("OPINION.HIGHLIGHT.FAILED") + "");
          else this.$toast.success(this.$t("OPINION.HIGHLIGHT.SUCCESS") + "");
        });
    },
  },
};
</script>

<style lang="scss">
@media print {
  .main-sidebar,
  .hideOnPrint,
  .main-header,
  .content-header {
    display: none !important;
  }
  .AnnouncementInfo {
    color-adjust: exact !important;
    -webkit-print-color-adjust: exact !important;
    background-color: white;

    .row,
    .chapterDetails,
    .shortAnswerTag,
    .fragmentContent {
      page-break-inside: avoid !important;
    }
  }
}
</style>

<style scoped lang="scss">
.ViewAnnouncement {
  & > .d-flex {
    gap: 1rem;
  }

  .tab-pane {
    max-width: 1400px;
    margin: 0 auto;
  }
  ::v-deep {
    tr {
      border-color: var(--color-neutral-mid);
    }

    .text-detail {
      font-size: 0.8rem;
      color: var(--text-color-light);
    }

    .bg-info {
      color: var(--color-primary) !important;
      background-color: var(--color-primary-lightest) !important;
      border: 1px solid var(--color-primary-lighter);
    }

    .bg-danger {
      background-color: var(--color-dashboard-3) !important;
    }

    .underline {
      text-decoration: underline;
      &.danger {
        text-decoration-color: var(--color-dashboard-3) !important;
      }
    }

    .oversize {
      margin: 0 -1.5rem;

      @media (max-width: 576px) {
        margin: 0 auto;
      }
    }

    .fragmentContent.oversize {
      .fragementHeader,
      .accordionBody {
        padding: 0 1.5rem;
      }
    }

    .table th,
    .table tbody {
      border: none !important;
    }

    .modal-header {
      background-color: var(--color-neutral-darker);
      align-items: center;

      .modal-title {
        color: white;
      }
      .btn-close {
        filter: invert(1) grayscale(100%) brightness(200%);
      }
    }

    .text-gray {
      color: var(--color-neutral-mid-dark);
    }
  }
}
</style>
