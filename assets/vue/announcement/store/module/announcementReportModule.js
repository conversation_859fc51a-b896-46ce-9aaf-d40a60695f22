import {make} from "vuex-pathify";
import axios from "axios";
import TaskQueueService from '../../../common/services/TaskQueueService';

const state = {
    groupsReports: {},// Generated group reports
    announcementReports: [],
    checkingGroupReports: false,

    loading: false,
    loadingParticipants: false,
    reports: {
        page: 1,
        totalItems: 0,
        items: []
    },
    participantReports: {
        page: 1,
        totalItems: 0,
        items: []
    }
};

const mutations = {
    ...make.mutations(state),
    checkGroupReports(state, check = false) {
        state.checkingGroupReports = check;
    },

    setAllReports(state, { items, totalItems }) {
        state.reports.items = items;
        state.reports.totalItems = totalItems;
    },
    
    setAllParticipantReports(state, { items, totalItems }) {
        state.participantReports.items = items;
        state.participantReports.totalItems = totalItems;
    }
};

export const getters = {
    ...make.getters(state),
};

export const actions = {
    ...make.actions(state),
    updateReports({ commit }, { items, totalItems }) {
        commit('setAllParticipantReports', { items, totalItems });
    },
    loadAnnouncementGroupReports({ commit, getters }, announcementId) {
        const { checkingGroupReports } = getters;
        if (checkingGroupReports) return;
        commit('checkGroupReports', true);
        axios.get(`/admin/announcement/${announcementId}/groups-reports`).then(r => {
            const { data } = r.data;
            commit('SET_GROUPS_REPORTS', data);
        }).finally(() => {
            commit('checkGroupReports', false);
        });
    },

    getAllAnnouncementsReports({ commit }, { page = 1, pageSize = 10, type = null }) {
        const url = new URL(window.location.origin + '/admin/announcement/reports' + (type ? '/' + type : ''));
        url.searchParams.set('page', `${page}`);
        url.searchParams.set('pageSize', `${pageSize}`);
        commit('SET_LOADING', true);
        axios.get(url.toString())
        .then(r => {
            const { data, error } = r.data;
            if (!error) {
                const { items, totalItems } = data;
                if (type === 'participants') {
                    commit('setAllParticipantReports', { items, totalItems });
                } else {
                    commit('setAllReports', { items, totalItems });
                }
            } else {
                console.error('Error: ', error);
            }
        })
        .catch(error => {
            console.error('Error: ', error);
        })
        .finally(() => {
            commit('SET_LOADING', false);
        });
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    getters,
    actions
};
