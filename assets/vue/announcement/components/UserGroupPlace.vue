<script>


import DaySelection from "../../common/components/DaySelection.vue";

export default {
  name: "UserGroupPlace",
  components: {DaySelection},
  data() {
    return {
      activeDays: {}
    };
  }
}
</script>

<template>
  <div class="UserGroupPlace">
    <div class="UserGroupPlace--header">
      <h1><i class="fa fa-users"></i> <span class="GroupName">Nombre del grupo</span> <span class="badge bg-secondary">25 personas</span></h1>
      <button type="button" class="btn btn-primary"><i class="fa fa-eye"></i></button>
    </div>
    <div class="UserGroupPlace--body">
      <div class="form-group col-4">
        <label>Centro formador</label>
        <input type="text" class="form-control">
      </div>
      <div class="form-group col-2">
        <label>CIF</label>
        <input type="text" class="form-control">
      </div>
      <div class="form-group col-4">
        <label>Ubicacion</label>
        <input type="text" class="form-control">
      </div>
      <div class="form-group col-2">
        <label>Aula/Sala</label>
        <input type="text" class="form-control">
      </div>
      <div class="form-group col-2 warning">
        <label>N° de Sesiones</label>
        <input type="number" min="0" class="form-control">
      </div>
      <div class="form-group col-3">
        <label>Duracion de la sesion (min)</label>
        <input type="number" min="0" class="form-control">
      </div>
      <day-selection v-model="activeDays"/>
    </div>
  </div>
</template>

<style scoped lang="scss">
.UserGroupPlace {
  width: 100%;
  display: flex;
  flex-flow: column;

  &.warning {
    .UserGroupPlace--header {
      background-color: $color-secondary;
      span.GroupName:after {
        content: '!';
        color: #FFFFFF;
        position: absolute;
        right: -1rem;
      }
    }
  }

  &--header {
    width: 100%;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: flex-start;
    background-color: var(--color-neutral-mid-dark);
    padding: 2rem 1rem;
    color: #ffffff;

    h1 {
      color: #FFFFFF;
      font-size: 18px;
      font-weight: bold;
      position: relative;

      &>span.GroupName {
        position: relative;
      }

      i {
        margin-right: .5rem;
      }

      .badge {
        color: var(--color-neutral-darkest);
        margin-left: 3rem;

        &.bg-secondary {
          background-color: #cad4df !important;
        }
      }
    }

    button {
      margin-left: auto;
    }
  }

  &--body {
    width: 100%;
    display: flex;
    flex-flow: row wrap;
    padding: 1rem;
  }
}
</style>
