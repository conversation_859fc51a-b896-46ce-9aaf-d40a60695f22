<template>
  <div class="Observations">
    <div class="col-12 d-flex flex-row align-items-center justify-content-end m-1">
      <router-link :to="{name: 'NewObservation', params: {id: id}}" class="btn btn-primary">{{ $t('COMMON.CREATE') }}</router-link>
    </div>
    <div class="col-12">
      <div class="col-12 d-flex align-items-center justify-content-center" v-if="isLoading">
        <loader :is-loaded="isLoading" />
      </div>
      <div class="observations-container mt-3">
        <table class="table datagrid" v-if="observations && observations.length > 0">
          <thead>
          <tr>
            <th></th>
            <th>{{ $t('NAME') }}</th>
            <th class="text-center">{{ $t('DATE') }}</th>
            <th class="text-right">{{ $t('ACTIONS') }}</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="observation in observations" :key="observation.id">
            <td>
              <div class="avatar" :style="{
                  'background-image': `url(/uploads/users/avatars/${observation.avatar ?? 'default.svg'})`
                }"></div>
            </td>
            <td>{{ observation.firstName }} {{ observation.lastName }}</td>
            <td class="text-center">{{ observation.createdAt }}</td>
            <td class="text-right">
              <router-link :to="{name: 'ViewObservation', params: { id: observation.id }}" class="btn btn-info btn-sm"><i class="fa fa-eye"></i></router-link>
              <router-link :to="{name: 'UpdateObservation', params: { id: observation.id }}" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></router-link>
              <button @click="deleteObservation(observation)" type="button" class="btn btn-danger btn-sm"><i class="fa fa-trash"></i></button>
            </td>
          </tr>
          </tbody>
        </table>
        <BaseNotResult v-else/>
      </div>
    </div>
  </div>
</template>

<script>
import Loader  from "../../admin/components/Loader.vue";


export default {
  name: "Observations",
  components: {Loader},
  props: {
    id: {
      type: Number|String,
      required: true
    }
  },
  data() {
    return {
      observations: []
    };
  },
  computed: {
    isLoading() {
      return this.$store.getters['announcementObservationModule/isLoading'];
    }
  },
  created() {
    this.getObservations();
  },
  methods: {
    async getObservations() {
      const { data, error } = await this.$store.dispatch('announcementObservationModule/getObservations', this.id);
      this.observations = data.items;
    },

    deleteObservation(observation) {
      this.$alertify.confirmWithTitle(
          this.$t('ANNOUNCEMENT_OBSERVATION.CONFIRM_DELETE.TITLE'),
          this.$t('ANNOUNCEMENT_OBSERVATION.CONFIRM_DELETE.DESCRIPTION'),
          () => {
            this.$store.dispatch('announcementObservationModule/deleteObservation', observation.id).then(res => {
              const { error } = res;
              if (error) {
                this.$toast.error(this.$t('ANNOUNCEMENT_OBSERVATION.DELETE.FAILED') + '');
              } else {
                this.$toast.success(this.$t('ANNOUNCEMENT_OBSERVATION.DELETE.SUCCESS') + '');
                this.getObservations();
              }
            })
          },
          () => {},
      );
    }
  }
}
</script>

 <style scoped lang="scss"> 
.Observations {
  .avatar {
    @include avatar;
    width: 50px !important;
    height: 50px !important;
  }
}
</style>
