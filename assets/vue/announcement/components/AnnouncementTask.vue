<template>
  <div class="d-flex align-items-center justify-content-center" v-if="loading">
    <loader :is-loaded="loading"></loader>
  </div>
  <div class="AnnouncementTask" v-else>
    <form
      action=""
      class="AnnouncementTask--form"
      @submit.prevent
      id="form-new-task"
    >
      <div class="AnnouncementTask--form--info">
        <div class="form-group required">
          <label for="">{{ $t("TASK_COURSE.TITLE") }} </label>
          <input
            id="title"
            name="title"
            type="text"
            class="form-control"
            v-model="task.title"
          />
        </div>
        <div class="form-group required">
          <label>{{ $t("TASK_COURSE.DESCRIPTION") }}</label>
          <froala
            tag="textarea"
            v-model="task.description"
            :config="froalaDescriptionConfig"
          ></froala>
        </div>

        <div class="form-group">
          <label for="">{{ $t("TASK_COURSE.GROUP") }}</label>

          <Multiselect
            v-model="task.groups"
            :options="groups"
            :multiple="true"
            track-by="name"
            label="name"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
            :deselectLabel="$t('MULTISELECT.DESELECT_LABEL')"
          ></Multiselect>
        </div>
      </div>
      <div class="AnnouncementTask--form--date">
        <div class="form-group required">
          <label for="start_date">{{ $t("TASK_COURSE.START_DATE") }}</label>
          <input
            id="start_date"
            name="start_date"
            type="datetime-local"
            class="form-control"
            v-model="task.startAt"
          />
        </div>
        <div class="form-group required">
          <label for="deadline">{{ $t("TASK_COURSE.DEADLINE") }}</label>
          <input
            id="deadline"
            name="deadline"
            type="datetime-local"
            class="form-control"
            v-model="task.deadline"
          />
        </div>
        <button-with-description
          title="TASK_COURSE.VISIBLE.TITLE"
          description="TASK_COURSE.VISIBLE.DESCRIPTION"
          v-model="task.visible"
          icon="fa fa-eye"
        ></button-with-description>
      </div>
    </form>
  </div>
</template>

<script>
import ButtonWithDescription from "../../common/components/ButtonWithDescription.vue";
import Loader from "../../admin/components/Loader.vue";
import Multiselect from "vue-multiselect";

export default {
  name: "AnnouncementTask",
  components: { Loader, ButtonWithDescription, Multiselect },
  data() {
    return {
      loading: true,
      task: {
        title: "",
        description: "",
        startAt: "",
        deadline: "",
        visible: false,
        groups: [],
      },
      groups: [],
      groupsValue: [],
    };
  },
  computed: {
    froalaDescriptionConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 250,
        pluginsEnabled: [
          "align",
          "link",
          "url",
          "lists",
          "paragraphStyle",
          "paragraphFormat",
          "quote",
        ],
      };
    },
    useGlobalEventBus() {
      return this.$store.getters["contentTitleModule/getUseGlobalEventBus"];
    },
  },
  created() {
    this.handleRouteParams();
    this.getGroupsAnnouncement();
  },
  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on("onTaskSave", () => {
        this.submit();
      });
    }
    const deadline = document.getElementById("deadline");
  },
  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off("onTaskSave");
    }
  },
  methods: {
    handleRouteParams() {
      const isUpdating = this.$route.name === "UpdateTaskCourse";
      this.$store.dispatch("contentTitleModule/addRoute", {
        routeName: this.$route.name,
        params: {
          linkName: isUpdating
            ? this.$t("TASK_COURSE.UPDATE_TASK")
            : this.$t("TASK_COURSE.NEW_TASK"),
          params: this.$route.params,
        },
      });

      this.$store.dispatch("contentTitleModule/setActions", {
        route: this.$route.name,
        actions: [
          {
            name: this.$t("SAVE"),
            event: "onTaskSave",
            class: "btn btn-primary",
          },
        ],
      });

      if (isUpdating) {
        this.loading = true;
        this.$store
          .dispatch("taskCourseModule/loadTaskCourse", this.$route.params.id)
          .then((res) => {
            const { error, data } = res;
            if (error) {
              this.$toast.error("TASK_COURSE.FAILED_TO_LOAD");
              this.$router.go(-1);
            } else {
              this.task = data;
              this.task.deadline = this.task.deadline.slice(0, 16);
              this.task.startAt = this.task.startAt.slice(0, 16);
              this.loading = false;
            }
          });
      } else {
        this.loading = false;
      }
    },

    submit() {
      const currentForm = document.forms["form-new-task"];
      const formData = new FormData(currentForm);

      if (
        formData.get("title") === undefined ||
        formData.get("title").length < 1
      ) {
        this.$toast.error(this.$t("TASK_COURSE.TITLE_REQUIRED") + "");
        return;
      }

      if (this.task.description.length < 1) {
        this.$toast.error(this.$t("TASK_COURSE.DESCRIPTION_REQUIRED") + "");
        return;
      }

      if (
        formData.get("start_date").length < 1 ||
        formData.get("deadline").length < 1
      ) {
        this.$toast.error(this.$t("TASK_COURSE.START_DEADLINE_REQUIRED") + "");
        return;
      }

      formData.append("visible", this.task.visible);
      formData.append("description", this.task.description);

      const isUpdating = this.$route.name === "UpdateTaskCourse";
      this.$alertify.confirmWithTitle(
        this.$t(
          "TASK_COURSE." + (isUpdating ? "UPDATE" : "CREATE") + ".CONFIRM.TITLE"
        ),
        this.$t(
          "TASK_COURSE." +
            (isUpdating ? "UPDATE" : "CREATE") +
            ".CONFIRM.DESCRIPTION"
        ),
        () => {
          this.saveTask(formData, isUpdating);
        },
        () => {}
      );
    },

    saveTask(formData, update = false) {
      const self = this;

      //parsear los grupos
      formData.append("groups", JSON.stringify(this.task.groups));

      function store() {
        if (update)
          return self.$store.dispatch(
            "announcementTaskModule/updateAnnouncementTask",
            {
              announcementId: self.$route.params.announcementId,
              taskId: self.$route.params.id,
              formData,
            }
          );
        else
          return self.$store.dispatch(
            "announcementTaskModule/addNewTaskToAnnouncement",
            {
              id: self.$route.params.id,
              formData,
            }
          );
      }

      store().then((res) => {
        const { data, error } = res;
        this.$toast.clear();
        if (error) {
          this.$toast.error(this.$t(data) + "");
        } else {
          this.$toast.success(
            this.$t(`TASK_COURSE${update ? ".UPDATE" : ".CREATE"}.SUCCESS`) + ""
          );
          this.$store.dispatch(
            "contentTitleModule/removeRouteFromContentTitle",
            this.$route.name
          );
          this.$store.dispatch("routerModule/setDeleteLastRoute", true);
          this.$store.dispatch("announcementModule/refreshAction", "tasks");
          this.$router.replace({
            name: "ViewTaskCourse",
            params: { id: data.id, announcementId: data.announcementId },
          });
        }
      });
    },

    async getGroupsAnnouncement() {
      const idAnnouncement = this.$route.params?.announcementId
        ? this.$route.params?.announcementId
        : this.$route.params.id;

      const groups = await this.$store.dispatch(
        "announcementTaskModule/getGroupsAnnouncement",
        idAnnouncement
      );

      this.groups = groups;
    },
  },
};
</script>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

 <style scoped lang="scss"> 
.AnnouncementTask {
  width: 100%;

  &--form {
    width: 100%;
    display: flex;
    flex-flow: row wrap;
    padding: 1rem;

    @media #{min-small-screen()} {
      display: grid;
      grid-template-columns: auto 320px;
    }

    div {
      padding: 0.25rem;
    }
  }
}
</style>
