<template>
  <div class="taskAccordion px-2 px-sm-5">
    <div
      class="taskAccordionHeader d-flex justify-content-between align-items-center mt-2 mb-2 flex-wrap user-select-none"
      @click="toggleAccordion"
      :class="{ 'cursor-pointer pb-2 position-relative pr-4': allowOpen }"
    >
      <p class="d-flex align-items-center mb-0 mr-1">
        <span class="iconContainer mr-2">
          <i class="fa fa-list-ul"></i>
        </span>
        {{ task.title }}
      </p>
      <div
        class="d-flex justify-content-between align-items-center flex-wrap flex-sm-nowrap my-2 ml-auto mr-0"
      >
        <p class="mb-0 font-small mr-1 ml-1">
          <DateTimeTag :datetime="task.startAt" />
        </p>
        <p class="mb-0 font-small mr-1 ml-1">
          <b>{{ $t("DELIVER") }} </b>
          <span class="ml-0"> {{ $t("DELIVER_DATE", [expireDate]) }}</span>
        </p>
        <i class="fa fa-paperclip mr-2"></i>
        <span :class="className"
          >{{ status }} <i class="ml-2 fa" :class="statusClass"></i
        ></span>
      </div>
      <i v-if="allowOpen" class="toggleIcon fa position-absolute" :class="isOpened ? 'fa-caret-up' : 'fa-caret-down'"></i>
    </div>
    <div class="taskContent border-bottom border-top mb-3" v-show="isOpened">
      <div class="row my-3">
        <div class="col-md-6 col-xs-12">
          <p class="font-weight-bold">
            {{ $t("ANNOUNCEMENT.MODALS.PROGRESS_TASK_DESC") }}
          </p>
          <div v-html="task.description || ''"></div>
        </div>
        <div class="col-md-6 col-xs-12">
          <p class="font-weight-bold">
            {{ $t("ANNOUNCEMENT.MODALS.PROGRESS_MATERIALS") }}
          </p>
          <div
            v-for="(material, index) in task.filesTask"
            :key="'taskMaterial' + index"
            class="task-files"
          >
            <a
              :href="material.urlFile"
              target="_blank"
              :download="material.fileOriginalName"
              class="cursor-pointer text-decoration-none d-flex align-items-center mb-2"
            >
            <span class="iconContainer mr-2"
            ><i class="fa fa-file-alt"></i
            ></span>
              {{ material.fileOriginalName }}
            </a>
          </div>
        </div>
      </div>
      <div
        class="row mt-4 bg-info pb-3 pt-3 justify-content-between"
        v-if="taskUser.files.length"
      >
        <div
          class="col-md-6 col-sm-12 d-flex flex-wrap align-self-start userFilesContainer"
        >
          <div
            v-for="(file, index) in taskUser.files"
            class="p-3 bg-white userFiles cursor-pointer"
            @click="setFileSelected(index)"
            data-bs-toggle="modal"
            data-bs-target="#previewModal_task"
          >
            <div class="d-flex align-items-center">
              <span class="iconContainerAlt mr-3">
                <i class="fa fa-file-invoice"></i>
              </span>
              <p class="mb-0">
                <span>{{ file.fileOriginalName }}</span
                ><br />
                <span class="subtitle text-uppercase">{{
                  getExtension(file)
                }}</span>
              </p>
            </div>
            <p class="subtitle mt-2 mb-0">
              <i class="fa fa-calendar"></i> {{ getDateText(file.createdAt) }}
            </p>
          </div>
        </div>
        <div class="col-md-5 col-sm-12 text-right">
          <textarea
            rows="10"
            class="form-control text-left mb-3"
            v-model="comment"
          ></textarea>
          <button class="btn btn-sm btn-danger" @click="rejectTask">
            {{ $t("ANNOUNCEMENT.MODALS.PROGRESS_TASK_REJECT") }}
          </button>
          <button class="btn btn-sm btn-primary ml-2" @click="acceptTask">
            {{ $t("ANNOUNCEMENT.MODALS.PROGRESS_TASK_VALIDATE") }}
          </button>
        </div>
      </div>
      <div class="row mt-4 bg-white pt-3 justify-content-between">
        <p class="font-weight-bold fs-5 my-0 pb-3">
          <i class="fa fa-comments"></i> {{ $t("LIBRARY.COMMENTS.TITLE") }}
        </p>
        <div
          class="col-12 py-3 userComments border-top"
          v-for="(comment, index) in taskUser.comments"
        >
          <img class="userAvatar" :src="comment.user.avatar || ''" alt=" " />
          <div>
            <p
              class="d-flex flex-wrap align-items-center justify-content-between mb-0"
            >
              <span class="font-weight-bold">{{ comment.user.fullName }}</span>
              <span class="subtitle">{{ getDateTime(comment.createdAt) }}</span>
            </p>
            <span>{{ comment.comment }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { UtilsMixin } from "../../mixins/utilsMixin";
import DateTimeTag from "./dateTimeTag";
import { get } from "vuex-pathify";

export default {
  name: "taskAccordion",
  components: { DateTimeTag },
  mixins: [UtilsMixin],
  props: {
    task: {
      type: Object,
      default: () => ({}),
    },
    opened: {
      type: Boolean,
      default: false,
    },
    allowOpen: {
      type: Boolean,
      default: true,
    },
    userId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      isOpened: false,
      comment: "",
      taskStatus: {
        PENDIENT: {
          title: this.$t("OBSERVATION_INVOICE_STATUS.2"),
          className: "pending",
          icon: "fa-redo",
        },
        DELIVERED: {
          title: this.$t("HISTORY_DELIVERY_TASK.DELIVERED"),
          className: "pending",
          icon: "fa-redo",
        },
        REVISION: {
          title: this.$t("HISTORY_DELIVERY_TASK.REVISION"),
          className: "delivered",
          icon: "fa-check",
        },
        REJECTED: {
          title: this.$t("HISTORY_DELIVERY_TASK.REJECTED"),
          className: "rejected",
          icon: "fa-times",
        },
        APROVED: {
          title: this.$t("HISTORY_DELIVERY_TASK.APPROVED"),
          className: "approved",
          icon: "fa-check",
        },
      },
    };
  },
  computed: {
    announcement: get("announcementModule/announcement"),
    groupStudents: get("announcementModule/calledUsers"),
    currentState() {
      return (
        this.taskStatus[this.task.descriptiveState] || this.taskStatus.DELIVERED
      );
    },
    className() {
      return this.currentState.className;
    },
    status() {
      return this.currentState.title;
    },
    statusClass() {
      return this.currentState.icon;
    },
    expireDate() {
      return this.getDate(this.task.deliveryAt);
    },
    taskUser() {
      return this.task.taskUser || { files: [], comments: [] };
    },
  },
  mounted() {
    this.isOpened = this.allowOpen && this.opened;
  },
  methods: {
    getDateTime(dateStr) {
      if (!dateStr || !dateStr.length || isNaN(parseInt(`${new Date(dateStr).getTime()}`)))
        return '--';
      
      const dateObj = new Date(dateStr);
      const hour = `${dateObj.getHours()}`.padStart(2, '0');
      const date = `${dateObj.getDate()}`.padStart(2, '0');
      const month = `${dateObj.getMonth() + 1}`.padStart(2, '0');
      const minutes = `${dateObj.getMinutes()}`.padStart(2, '0');
      const seconds = `${dateObj.getSeconds()}`.padStart(2, '0');
      
      return `${date}-${month}-${dateObj.getFullYear()} ${hour}:${minutes}:${seconds}`;
    },
    getDateText(createdAt) {
      if (
        !createdAt ||
        !createdAt.length ||
        isNaN(parseInt(`${new Date(createdAt).getTime()}`))
      )
        return "";

      const dateObj = new Date(createdAt);
      const date = `${dateObj.getDate()}`.padStart(2, "0");
      const month = `${dateObj.getMonth() + 1}`.padStart(2, "0");
      const hour = `${dateObj.getHours()}`.padStart(2, "0");
      const minutes = `${dateObj.getMinutes()}`.padStart(2, "0");

      return this.$t("ANNOUNCEMENT.MODALS.PROGRESS_TASK_DELIVERED", [
        `${date}-${month}-${dateObj.getFullYear()}`,
        `${hour}:${minutes}`,
      ]);
    },
    toggleAccordion() {
      if (this.allowOpen) {
        this.isOpened = !this.isOpened;
      } else this.isOpened = false;
    },
    getExtension(file) {
      const { fileMimeType, fileOriginalName } = file;
      if ((fileMimeType || "").length) return fileMimeType.split("/")[1];

      if (!fileOriginalName) return "";

      const fileSplitted = fileOriginalName.split(".");
      return fileSplitted[fileSplitted.length - 1];
    },
    rejectTask() {
      this.saveReview(false);
    },
    acceptTask() {
      this.saveReview(true);
    },
    saveReview(accept) {
      if (!this.allowOpen || !this.userId) return;
      const state = accept ? 4 : 3;

      this.comment = this.cleanString(this.comment);
      if (this.comment.length < 2) {
        this.$toast.clear();
        this.$toast.error(
          `${this.$t("ANNOUNCEMENT.MODALS.PROGRESS_REVIEW_ERROR")}`
        );
        return;
      }

      this.$alertify.confirmWithTitle(
        this.$t("HELP_TEXT.SAVE.CONFIRM.TITLE"),
        this.$t(
          "ANNOUNCEMENT.MODALS.PROGRESS_SAVE_TEXT" + (accept ? "1" : "2")
        ),
        () => {
          this.$toast.clear();
          this.$toast.info(
            `${this.$t("ANNOUNCEMENT.MODALS.PROGRESS_SAVE_INFO")}`
          );
          this.$store
            .dispatch("announcementTaskModule/updateReviewTask", {
              idUser: this.userId,
              idTask: this.task.id,
              comment: this.comment,
              state,
            })
            .then(() => {
              this.task.state = state;
              this.task.descriptiveState = accept ? "APROVED" : "REJECTED";
              this.$toast.clear();
              this.$toast.success(
                `${this.$t("ANNOUNCEMENT.MODALS.PROGRESS_SAVE_SUCCESS")}`
              );
              this.comment = "";
              this.reloadData();
            })
            .catch((e) => {
              this.$toast.clear();
              this.$toast.error(`${this.$t("ANNOUNCEMENT.STUDENTTAB.ERROR")}`);
            });
        },
        () => {}
      );
    },
    reloadData() {
      this.$store
        .dispatch(
          "announcementModule/loadAnnouncementCalledUsers",
          this.announcement.id
        )
        .then(() => {
          this.setUserSelected();
        });
    },
    setUserSelected() {
      this.groupStudents.find((group) => {
        return (
          group.users.findIndex((user) => {
            if (`${user.id}` === `${this.userId}`) {
              this.$store.dispatch("announcementModule/setUserSelected", {
                ...user,
              });
              return true;
            }
            return false;
          }) > -1
        );
      });
    },
    setFileSelected(index) {
      this.$store.dispatch("announcementModule/setFileSelected", {
        ...this.taskUser.files[index],
      });
    },
  },
};
</script>

 <style scoped lang="scss"> 
.taskAccordion {
  .taskAccordionHeader {
    gap: 0.5rem 0;

    & > div {
      gap: 1rem 2rem;
    }
  }

  .font-small {
    font-size: 0.8rem;
  }

  .bg-info,
  .delivered,
  .approved {
    background-color: var(--color-primary-lighter) !important;
  }

  .bg-info {
    gap: 1rem;
    @media (max-width: 576px) {
      margin: 0 auto;
    }
  }

  .subtitle,
  ::v-deep .subtitle {
    font-size: 0.8rem;
    color: var(--color-neutral-mid-dark);
  }

  .iconContainer,
  .iconContainerAlt {
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    border-radius: 5px;
  }

  .iconContainer {
    width: 2rem;
    height: 2rem;
    background-color: var(--color-primary-lighter);
    color: var(--color-primary);
  }

  .iconContainerAlt {
    background-color: var(--color-primary);
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.5rem;
  }

  .pending,
  .delivered,
  .approved,
  .rejected {
    padding: 0.25rem 1rem;
    font-size: 0.8rem;
    border-radius: 1rem;
    background-color: white;
    border: 1px solid var(--color-neutral-mid-darker);
    white-space: nowrap;
  }

  .delivered,
  .approved {
    i {
      color: var(--color-primary);
    }

    border: 1px solid var(--color-primary-light);
  }

  .rejected {
    color: var(--color-dashboard-4);
  }

  .userFilesContainer {
    gap: 1rem;
  }

  .userFiles {
    border-radius: 7px;
  }

  .userComments {
    display: grid;
    grid-template-columns: 3rem auto;
    gap: 1rem;
    .userAvatar {
      position: relative;
      overflow: hidden;
      object-position: center;
      object-fit: cover;
      border-radius: 100%;
      width: 60px;
      height: 60px;
    }
  }
  
  .toggleIcon {
    right: 0;
  }
  
  .task-files a {
    width: fit-content;
  }
}
</style>
