<template>
<div class="dateTimeTag">
  <span>{{ getDate(datetime) }}</span>
  <span class="ml-1 subtitle">{{ getTime(datetime) }}</span>
</div>
</template>

<script>
import {UtilsMixin} from "../../mixins/utilsMixin";

export default {
  name: "dateTimeTag",
  mixins: [UtilsMixin],
  props: {
    datetime: {
      type: String,
      default: ''
    }
  }
}
</script>

 <style scoped lang="scss"> 
.dateTimeTag {

  .subtitle {
    color: var(--color-neutral-mid-darker);
    font-size: 0.9rem;
  }
}
</style>
