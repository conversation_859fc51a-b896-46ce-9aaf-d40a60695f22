<template>
<div class="emojiSelector user-select-none" v-click-outside="hideEmojiBox">
  <div class="iconContainer cursor-pointer" @click="showEmojiList =! showEmojiList">
    <i class="fa fa-smile-o"></i>
  </div>
  <div class="emojiBox" v-show="showEmojiList">
    <div class="emojiGroups d-flex">
      <div class="emojiContainer cursor-pointer"
           v-for="(emoji, index) in emojiList"
           :key="'emojiHeader' + index"
           :class="{'selected': currentPage === index}"
           @click="setPage(index)">
        <span class="cursor-pointer">{{ emoji.icon }}</span>
      </div>
    </div>
    <div class="emojiList d-grid">
      <div class="emojiContainer"
           v-for="(emoji, index) in emojiList[currentPage].list"
           :key="'emojiHeader' + index"
           @click="setEmoji(emoji)">
        <span class="cursor-pointer">{{ emoji }}</span>
      </div>
    </div>
  </div>
</div>
</template>

<script>
export default {
  name: "emojiSelector",
  data() {
    return {
      currentPage: 0,
      showEmojiList: false,
      emojiList: [
        {
          icon: '😀',
          list: [
            '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
            '😇', '🙂', '🙃', '😉', '😍', '😘', '😗', '😙', '😋',
            '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
            '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️',  '😣',
            '😖', '😫', '😩', '😢', '😭', '😤', '😠', '😡', '🤬',
            '🤯', '😳', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔',
            '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯',
            '😦', '😧', '😮', '😲', '😴', '🤤', '😪', '😵', '🤐',
            '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈',
            '👿', '👹', '👺', '🤡', '👻', '💀', '👽', '👾', '🤖',
            '🎃', '😺', '😸', '😹', '😻', '😼', '😽', '🙀', '😿',
            '😾'
          ]
        },
        {
          icon: '🤲',
          list: [
            '🤲', '👐', '🙌', '👏', '🤝',
            '👍', '👊', '✊', '🤛', '🤜',
            '🤞', '✌️',  '🤟', '🤘', '👌',
            '👈', '👉', '👆', '👇', '☝️',
            '✋', '🤚', '🖐',  '🖖', '👋',
            '🤙', '💪', '✍️',  '🙏', '❤️'
          ]
        },
        {
          icon: '🧥',
          list: [
            '🧥', '👚', '👕', '👖', '👔',
            '👗', '👙', '👘', '👠', '👡',
            '👢', '👞', '👟', '🧦', '🧤',
            '🧣', '🎩', '🧢', '👒', '🎓',
            '⛑',  '👑', '👝', '👛', '👜',
            '💼', '🎒', '👓', '🕶',  '🌂'
          ]
        },
        {
          icon: '🐶',
          list: [
            '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨',
            '🐯', '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉',
            '🙊', '🐒', '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆',
            '🦅', '🦉', '🦇', '🐺', '🐗', '🐴', '🦄', '🐝', '🐛',
            '🦋', '🐌', '🐚', '🐞', '🐜', '🦗', '🕷',  '🕸',  '🦂',
            '🐢', '🐍', '🦎', '🦖', '🦕', '🐙', '🦑', '🦐', '🦀',
            '🐡', '🐠', '🐟', '🐬', '🐳', '🐋', '🦈', '🐊', '🐅',
            '🐆', '🦓', '🦍', '🐘', '🦏', '🐪', '🐫', '🦒', '🐃',
            '🐂', '🐄', '🐎', '🐖', '🐏', '🐑', '🐐', '🦌', '🐕',
            '🐩', '🐈', '🐓', '🦃', '🕊',  '🐇', '🐁', '🐀', '🐿',
            '🦔', '🐾', '🐉', '🐲'
          ]
        },
        {
          icon: '🌵',
          list: [
            '🌵', '🎄', '🌲',  '🌳', '🌴', '🌱',  '🌿', '☘️',
            '🍀', '🎍', '🎋',  '🍃', '🍂', '🍁',  '🍄', '🌾',
            '💐', '🌷', '🌹',  '🥀', '🌺', '🌸',  '🌼', '🌻',
            '🌞', '🌝', '🌛',  '🌜', '🌚', '🌕',  '🌖', '🌗',
            '🌘', '🌑', '🌒',  '🌓', '🌔', '🌙',  '🌎', '🌍',
            '🌏', '💫', '⭐️',  '🌟', '✨', '⚡️☄️', '💥', '🔥',
            '🌪',  '🌈', '☀️',   '🌤',  '⛅️', '🌥☁️',  '🌦',  '🌧',
            '⛈',  '🌩',  '🌨',   '❄️',  '☃️',  '⛄️',  '🌬',  '💨',
            '💧', '💦', '☔️☂️', '🌊', '🌫',  '🍏',  '🍎', '🍐',
            '🍊', '🍋', '🍌',  '🍉', '🍇', '🍓',  '🍈', '🍒',
            '🍑', '🍍', '🥥',  '🥝', '🍅', '🍆',  '🥑', '🥦',
            '🥒', '🌶',  '🌽',  '🥕'
          ]
        },
        {
          icon: '🥔',
          list: [
            '🥔', '🍠', '🥐', '🍞', '🥖', '🥨', '🧀', '🥚',
            '🍳', '🥞', '🥓', '🥩', '🍗', '🍖', '🌭', '🍔',
            '🍟', '🍕', '🥪', '🥙', '🌮', '🌯', '🥗', '🥘',
            '🥫', '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟',
            '🍤', '🍙', '🍚', '🍘', '🍥', '🥠', '🍢', '🍡',
            '🍧', '🍨', '🍦', '🥧', '🍰', '🎂', '🍮', '🍭',
            '🍬', '🍫', '🍿', '🍩', '🍪', '🌰', '🥜', '🍯',
            '🥛', '🍼', '☕️', '🍵', '🥤', '🍶', '🍺', '🍻',
            '🥂', '🍷', '🥃', '🍸', '🍹', '🍾', '🥄', '🥄',
            '🍴', '🍽',  '🥣', '🥡', '🥢'
          ]
        },
        {
          icon: '⚽️',
          list: [
            '⚽️', '🏀', '🏈', '⚾️', '🎾', '🏐', '🏉',
            '🎱', '🏓', '🏸', '🥅', '🏒', '🏑', '🏏',
            '⛳️', '🏹', '🎣', '🥊', '🥋', '🎽', '⛸',
            '🥌', '🛷', '🎿', '⛷',  '🏂', '🏆', '🥇',
            '🥈', '🥉', '🏅', '🎖',  '🏵',  '🎗',  '🎫',
            '🎟',  '🎪', '🎭', '🎨', '🎬', '🎤', '🎧',
            '🎼', '🎹', '🥁', '🎷', '🎺', '🎸', '🎻',
            '🎲', '🎯', '🎳', '🎮', '🎰'
          ]
        },
        {
          icon: '🚗',
          list: [
            '🚗', '🚕', '🚙', '🚌', '🚎', '🏎',  '🚓', '🚑', '🚒',
            '🚐', '🚚', '🚛', '🚜', '🛴', '🚲', '🛵', '🏍',  '🚨',
            '🚔', '🚍', '🚘', '🚖', '🚡', '🚠', '🚟', '🚃', '🚋',
            '🚞', '🚝', '🚄', '🚅', '🚈', '🚂', '🚆', '🚇', '🚊',
            '🚉', '✈️',  '🛫', '🛬', '🛩',  '💺', '🛰',  '🚀', '🛸',
            '🚁', '🛶', '⛵️', '🚤', '🛥',  '🛳',  '⛴',  '🚢', '⚓️',
            '⛽️', '🚧', '🚦', '🚥', '🚏', '🗺',  '🗿', '🗽', '🗼',
            '🏰', '🏯', '🏟',  '🎡', '🎢', '🎠', '⛲️', '⛱',  '🏖',
            '🏝',  '🏜',  '🌋', '⛰',  '🏔',  '🗻', '🏕',  '⛺️', '🏠',
            '🏡', '🏘',  '🏚',  '🏗',  '🏭', '🏢', '🏬', '🏣', '🏤',
            '🏥', '🏦', '🏨', '🏪', '🏫', '🏩', '💒', '🏛',  '⛪️',
            '🕌', '🕍', '🕋', '⛩',  '🛤',  '🛣',  '🗾', '🎑', '🏞',
            '🌅', '🌄', '🌠', '🎇', '🎆', '🌇', '🌆', '🏙',  '🌃',
            '🌌', '🌉', '🌁'
          ]
        },
        {
          icon: '⌚️',
          list: [
            '⌚️', '📱', '📲', '📴', '📳', '💻', '⌨️',  '🖥',  '🖨',
            '🖱',  '🖲',  '🕹',  '🗜',  '💽', '💾', '💿', '📀', '📼',
            '📷', '📸', '📹', '📽',  '🎞',  '📞', '☎️',  '📟', '📠',
            '📺', '📻', '🎙',  '🎚',  '🎛',  '⏱',  '⏲',  '⏰', '🕰',
            '⌛️', '⏳', '📡', '🔋', '🔌', '💡', '🔦', '🕯',  '🗑',
            '🛢',  '💸', '💵', '💴', '💶', '💷', '💰', '💳', '💎',
            '⚖️',  '🔧', '🔨', '⚒',  '🛠',  '⛏',  '🔩', '⚙️',  '⛓',
            '🔫', '💣', '🔪', '🗡',  '⚔️',  '🛡',  '🚬', '⚰️',  '⚱️',
            '🏺', '🔮', '📿', '💈', '⚗️',  '🔭', '🔬', '🕳',  '💊',
            '💉', '🌡',  '🚽', '🚰', '🚿', '🛁', '🔑', '🗝',  '🚪',
            '🛋',  '🛏',  '🛌', '🖼',  '🛍',  '🛒', '🎁', '🎈', '🎏',
            '🎀', '🎊', '🎉', '🎎', '🏮', '🎐', '✉️',  '📩', '📨',
            '📧', '💌', '📥', '📤', '📦', '🏷',  '📪', '📫', '📬',
            '📭', '📮', '📯', '📜', '📃', '📄', '📑', '📊', '📈',
            '📉', '🗒',  '🗓',  '📆', '📅', '📇', '🗃',  '🗳',  '📋',
            '📁', '📂', '🗂',  '🗞',  '📰', '📓', '📔', '📒', '📕',
            '📗', '📘', '📙', '📚', '📖', '🔖', '🔗', '📎', '🖇',
            '📐', '📏', '📌', '📍', '✂️',  '📝', '✏️',  '🔍', '🔎',
            '🔏', '🔐', '🔒', '🔓', '📣', '📢', '🔈', '🔇', '🔉',
            '🔊', '🔔', '🎵', '🎶', '☢️',  '☣️',  '✔️',  '❌'
          ]
        }
      ]
    }
  },
  methods: {
    setPage(page) {
      this.currentPage = page;
    },
    setEmoji(emoji) {
      this.$emit('clicked', emoji);
    },
    hideEmojiBox() {
      if (this.showEmojiList) this.showEmojiList = false;
    }
  }
}
</script>

 <style scoped lang="scss"> 
.emojiSelector {
  position: relative;

  .iconContainer {
    width: 2.4rem;
    height: 2.4rem;
  }

  .emojiContainer, .iconContainer {
    min-width: 2rem;
    min-height: 2rem;
    display: grid;
    place-items: center;
    margin: 0;
    background-color: white;

    &:hover {
      background-color: var(--color-primary-lighter);
    }
  }

  .emojiGroups {
    .emojiContainer {
      border: 1px solid var(--color-neutral-mid-dark);
      border-radius: 3px 3px 0 0;
      margin-bottom: -1px;

      &.selected {
        background-color: var(--color-primary-light);
        border-width: 1px 1px 0;
      }
    }
  }

  .emojiList {
    grid-template-columns: repeat(auto-fit, minmax(2rem, 1fr));
    grid-template-rows: repeat(auto-fit, 2rem);
    border: 1px solid var(--color-neutral-mid-dark);
    background-color: white;
    overflow: auto;
    height: 9rem;
  }

  .iconContainer {
    background-color: var(--color-primary-lighter);
    border-radius: 2rem;
  }

  .emojiBox {
    position: absolute;
    width: 21rem;
    bottom: 3rem;
  }
}
</style>
