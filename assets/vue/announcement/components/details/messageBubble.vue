<template>
  <div class="messageBubble" :class="{'rightBubble': sendByMe}">
    <span class="content">{{ content }}<span>{{ time }}<i v-if="sendByMe" class="fa fa-check-double ml-2" :class="{'text-primary': seen}"></i></span>
    </span>
    <span class="time float-end" :class="{'seen': seen}">
      {{ time }} <i v-if="sendByMe" class="fa fa-check-double ml-2" :class="{'text-primary': seen}"></i>
    </span>
  </div>
</template>

<script>
import {get} from "vuex-pathify";

export default {
  name: "messageBubble",
  props: {
    userID: {
      type: Number,
      default: 0,
    },
    content: {
      type: String,
      default: '',
    },
    createdAt: {
      type: String,
      default: '',
    },
    seen: {
      type: Boolean,
      default: false,
    },
    tutorId: {
      type: Number,
      default: undefined
    }
  },
  computed: {
    id: get("userModule/user@id"),
    sendByMe() {
      return `${this.userID}` === `${(this.tutorId || this.id)}`;
    },
    time() {
      if (!this.createdAt || !this.createdAt.length || isNaN(parseInt(`${new Date(this.createdAt).getTime()}`)))
        return '';
      const dateObj = new Date(this.createdAt);
      const hour = `${dateObj.getHours()}`.padStart(2, '0');
      const minutes = `${dateObj.getMinutes()}`.padStart(2, '0');
      return `${hour}:${minutes}`;
    }
  }
}
</script>

 <style scoped lang="scss"> 
.messageBubble {
  position: relative;
  align-self: flex-start;
  min-width: 100px;
  max-width: 600px;
  margin: 0.5rem 0;
  padding: 1rem;
  border-radius: 7px;
  background-color: white;

  &.rightBubble {
    background-color: var(--color-primary-lighter);
    align-self: flex-end;
  }

  .content {
    overflow-wrap: break-word;
    span {
      visibility: hidden;
    }
  }

  .time {
    position: absolute;
    right: 0.7rem;
    bottom: 0.7rem;
    white-space: nowrap;
    font-size: 0.8rem;
    color: var(--color-neutral-mid-dark)
  }
}
</style>
