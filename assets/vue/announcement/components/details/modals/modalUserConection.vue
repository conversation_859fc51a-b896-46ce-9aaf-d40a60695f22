<template>
<div class="modalUserConection">
  <BaseModal
      :identifier="tag + 'userConection'"
      :title="$t('CONNECTIONS') || ''"
      padding="2rem"
  >
    <div class="modalTitle text-center text-sm-start d-flex justify-content-center justify-content-sm-start flex-wrap align-items-center pt-0 pl-4 pb-2 pr-4">
      <img class="userAvatar" :src="user.avatar || ''" alt=" ">
      <h3 class="font-weight-bold title">{{ user.name }}</h3>
    </div>
    <p class="pl-0 pl-sm-4 pb-2">{{ $t('ANNOUNCEMENT.MODALS.CONNECTIONS', [connections.length]) }}</p>
    <div class="container">
      <table class="table table-condensed" v-show="connections.length">
        <thead>
        <tr>
          <th class="font-weight-bold">{{ $t('DATE_TEXT') }}</th>
          <th class="font-weight-bold ip text-center">IP</th>
          <th class="font-weight-bold bigCell">{{ $t('ANNOUNCEMENT.MODALS.TIME_CONNECTIONS') }}</th>
          <th class="font-weight-bold text-center dateContent">{{ $t('ANNOUNCEMENT.MODALS.START_END') }}</th>
        </tr>
        </thead>
        <tbody>
        <tr
            v-for="(connection, index) in connections"
            :key="tag + index"
        >
          <td>{{ connection.day }}</td>
          <td class="ip">{{ connection.ip }}</td>
          <td class="bigCell">{{ connection.time }}</td>
          <td class="text-center dateContent">{{ connection.initFin }}</td>
        </tr>
        </tbody>
      </table>
      <DataNotFound :hide-on="!connections.length" :text="$t('ANNOUNCEMENT.MODALS.CONNECTIONS_NOT_FOUND') || ''"/>
    </div>
  </BaseModal>
</div>
</template>

<script>

import {get}        from "vuex-pathify";
import DataNotFound from "../DataNotFound";
import BaseModal from "../../../../base/BaseModal.vue";

export default {
  name: "modalUserConection",
  components: { DataNotFound, BaseModal},
  props: {
    tag: { type: String, default: '' },
  },
  computed: {
    user: get('announcementModule/userSelected'),
    connections() {
      return (this.user?.conexions || [])
    }
  },
}
</script>

 <style scoped lang="scss"> 
.modalUserConection {
  .userAvatar {
    width: 3rem;
    height: 3rem;
  }

  .modalTitle {
    gap: 1rem;
  }

  .container {
    padding: 1rem;
    border-radius: 7px;
    background-color: white;
    overflow-x: auto;
  }

  table {
    font-size: 0.9rem;
  }

  ::v-deep {
    .modal-body {
      background-color: var(--color-neutral-lighter);
    }
  }

  tbody {
    display: block !important;
    height: 100%;
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  thead, tbody, tr {
    display: table;
    width: 100%;
    table-layout: fixed;
  }

  tr td, tr td, th {
    width: 100px;
  }

  .ip {
    width: 100px;
    max-width: 250px !important;
  }

  .bigCell {
    text-align: center;
    width: 170px;
    max-width: 170px;
  }

  thead {
    width: calc(100% - 1rem);
  }
  
  .dateContent {
    width: 150px;
    max-width: 150px;
  }
}
</style>
