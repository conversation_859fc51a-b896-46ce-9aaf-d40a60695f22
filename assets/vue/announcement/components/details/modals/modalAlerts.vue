<template>
  <div class="modalAlerts">
    <BaseModal
      :identifier="tag + 'userAlerts'"
      :title="$t('ANNOUNCEMENT.MODALS.PERSONAL_INFORMATION') || ''"
      padding="0.5rem 2rem 2rem 2rem"
      size="modal-md"
    >
      <div class="col-12 d-flex justify-content-end">
        <button
          type="button"
          class="btn btn-sm btn-primary px-4"
          @click="showReport(user, announcement.id)"
        >
          <i class="fa fa-download mr-2"></i
          >{{ $t("ANNOUNCEMENT.STUDENTTAB.VIEW_REPORT") }}
        </button>
      </div>
      <div class="d-flex flex-column align-items-center">
        <div class="d-flex align-items-center UserAvatar--container">
          <img class="userAvatar" :src="user.avatar || ''" alt=" " />
          <p class="alertCounter" v-show="alerts.length">
            <i class="fa fa-exclamation-triangle text-warning ml-3"></i>
            <span>{{ alerts.length }}</span>
          </p>
        </div>
        <h4
          class="text-center w-100 font-weight-bold title font-weight-bold title my-3"
          :class="!user.validated ? 'text-danger' : ''"
        >
          {{ user.name }}
        </h4>
        <h4 class="text-danger" v-if="!user.validated">
          <i class="fa fa-exclamation-triangle"></i>
          {{ $t("USER_ACCOUNT.NOT_VALIDATED") }}
        </h4>

        <a
          v-if="!user.validated"
          class="btn btn-sm btn-primary"
          data-bs-toggle="modal"
          data-bs-target="#user-email-validation-account"
          @click="fillUserProfile(user)"
        >
          {{ $t("USER_ACCOUNT.SEND_ACTIVATION_EMAIL") }}
        </a>
      </div>
      <div class="container my-3">
        <div class="row">
          <div class="col-md-6 col-xs-12 font-weight-bold">
            <i class="fa fa-id-card"></i> {{ mainIdentification }}:
          </div>
          <div class="col-md-6 col-xs-12 line-break-anywhere">
            {{ user.dni }}
          </div>
        </div>
        <div class="row mt-2 mb-2">
          <div class="col-md-6 col-xs-12 font-weight-bold">
            <i class="fa fa-envelope"></i>
            {{ $t("ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_EMAIL") }}:
          </div>
          <div class="col-md-6 col-xs-12">
            <a
              :href="'mailto:' + user.email"
              class="link line-break-anywhere"
              >{{ user.email }}</a
            >
          </div>
        </div>
      </div>
      <div class="container">
        <ul class="mb-0" v-show="alerts.length">
          <li
            v-for="(alert, index) in alerts"
            :key="tag + '-alert-' + index"
            class="col-12"
            :class="{ 'mt-2': index }"
          >
            {{ alert }}
          </li>
        </ul>
        <DataNotFound
          :hide-on="!alerts.length"
          :text="$t('ANNOUNCEMENT.MODALS.ALERTS_NOT_FOUND') || ''"
          icon="fa-check-circle"
        />
      </div>
    </BaseModal>
  </div>
</template>

<script>
import { get, sync } from "vuex-pathify";
import DataNotFound from "../DataNotFound";
import { UtilsMixin } from "../../../mixins/utilsMixin";
import BaseModal from "../../../../base/BaseModal.vue";

export default {
  name: "modalAlerts",
  components: { DataNotFound, BaseModal },
  mixins: [UtilsMixin],
  computed: {
    user: get("announcementModule/userSelected"),
    selectedUserProfile: sync("announcementModule/selectedUserProfile"),
    announcement: get("announcementModule/announcement"),

    alerts() {
      return this.user.comunications?.alerts || [];
    },

    mainIdentification() {
      return this.announcement?.mainIdentification?.name || "DNI";
    }
  },
  props: {
    tag: { type: String, default: "" },
  },
  methods: {
    fillUserProfile(user) {
      this.selectedUserProfile = user;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.modalAlerts {
  .UserAvatar--container {
    position: relative;
  }
  .userAvatar {
    width: 5rem;
    height: 5rem;
  }

  .container {
    padding: 1rem;
    border-radius: 7px;
    background-color: white;
  }

  .alertCounter {
    position: absolute;
    right: -2rem;
    top: 2rem;

    span {
      font-size: 0.8rem;
      vertical-align: super;
      font-weight: bold;
    }
  }

  ::v-deep {
    .modal-body {
      background-color: var(--color-neutral-lighter);
    }
  }
}
</style>
