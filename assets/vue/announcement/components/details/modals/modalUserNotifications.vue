
<template>
  <div class="modalUserNotifications">
    <BaseModal
      :identifier="tag + 'userNotifications'"
      :title="$t('ANNOUNCEMENT.NOTIFICATIONS') || ''"
      padding="2rem"
      size="modal-lg"
    >
      <div class="d-flex flex-column align-items-center">
        <div class="d-flex align-items-center">
          <img class="userAvatar" :src="user.avatar || ''" alt=" " />
          <p v-show="notifications.length" class="mb-0">
            <i class="fa fa-exclamation-triangle text-warning ml-2"></i>
            <span class="ml-2">{{ notifications.length }}</span>
          </p>
        </div>
        <h3 class="font-weight-bold title font-weight-bold title my-3">
          {{ user.name }}
        </h3>
      </div>
      <div class="container">
        <ul v-show="notifications.length">
          <li
            v-for="(value, index) in notifications"
            :key="tag + '-notification-' + index"
            class="col-12"
          >
            {{ value }}
          </li>
        </ul>
        <DataNotFound
          :hide-on="!notifications.length"
          :text="$t('ANNOUNCEMENT.NOTIFICATIONTAB.NOT_FOUND') || ''"
          icon="fa-check-circle"
        />
      </div>
    </BaseModal>
  </div>
</template>

<script>

import { get } from "vuex-pathify";
import DataNotFound from "../DataNotFound";
import BaseModal from "../../../../base/BaseModal.vue";

export default {
  name: "modalUserNotifications",
  components: { DataNotFound, BaseModal },
  computed: {
    user: get("announcementModule/userSelected"),
    notifications() {
      return this.user.comunications?.notifications || [];
    },
  },
  props: {
    tag: { type: String, default: "" },
  },
};
</script>

<style  scoped lang="scss">
.modalUserNotifications {
  .userAvatar {
    width: 5rem;
    height: 5rem;
  }

  .container {
    padding: 1rem;
    border-radius: 7px;
    background-color: white;
  }

  ::v-deep {
    .modal-body {
      background-color: var(--color-neutral-lighter);
    }
  }
}
</style>
