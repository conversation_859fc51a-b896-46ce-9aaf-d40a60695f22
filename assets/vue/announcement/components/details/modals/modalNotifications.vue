<template>
  <div class="modalNotifications">
    <BaseModal
      :identifier="tag + 'notifications'"
      title="Notificaciones"
      padding="2rem"
      size="modal-xl"
    >
      <div class="form-group required">
        <label for="start_date"
          >{{ $t("TASK_COURSE.START_DATE") }} [{{
            announcement.timezone
          }}]</label
        >
        <input
          id="start_date"
          name="start_date"
          type="datetime-local"
          class="form-control"
          v-model="notification.sentAt"
        />
      </div>
      <p class="mb-0">{{ $t("CONTENT.CONFIGUREFIELD.CONTENT") }}</p>
      <div class="w-100 mb-5">
        <froala
          tag="textarea"
          v-model="notification.text"
          :config="froalaConfig"
        />
      </div>

      <div class="w-100 mb-5">
        <label for="">{{ $t("TASK_COURSE.GROUP") }}</label>
        <Multiselect
          v-model="notification.groups"
          :options="groups"
          :multiple="true"
          track-by="name"
          label="name"
          :placeholder="$t('MULTISELECT.PLACEHOLDER')"
          :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
          :deselectLabel="$t('MULTISELECT.DESELECT_LABEL')"
        ></Multiselect>
      </div>

      <div class="w-100 text-right">
        <button
          class="btn btn-primary pl-2 pr-2"
          @click="saveNotification"
          :disabled="savingData"
        >
          {{ $t("SAVE") }}
        </button>
      </div>
    </BaseModal>
  </div>
</template>

<script>
import Multiselect from "vue-multiselect";
export default {
  name: "modalNotifications",
  components: { Multiselect },
  props: {
    tag: { type: String, default: "" },
    item: { type: Object, default: () => ({}) },
    announcement: { type: Object, default: () => ({}) },
  },
  data() {
    return {
      notification: {
        text: "",
        sentAt: "",
        groups: [],
      },
      savingData: false,
      groups: [],
    };
  },
  computed: {
    froalaConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 400,
        pluginsEnabled: ["align", "lists", "paragraphStyle", "paragraphFormat"],
      };
    },
    itemID() {
      return this.item.id;
    },
  },
  watch: {
    itemID() {
      this.notification = Object.assign(
        {},
        this.itemID ? this.item : { text: "", sentAt: "", groups: [] }
      );
    },
  },

  created() {
    this.getGroupsAnnouncement();
  },

  methods: {
    saveNotification() {
      if (this.savingData) return;

      this.savingData = true;
      this.notificationList = [];
      this.$store
        .dispatch("notificationModule/saveNotifications", {
          idAnnouncement: this.announcement.id,
          idNotification: this.notification.id || 0,
          text: this.notification.text,
          sentAt: this.notification.sentAt,
          groups: this.notification.groups,
        })
        .then(() => {
          this.$toast.success("Notificación guardada correctamente");
          this.$emit("reload");
          this.notification = { text: "", sentAt: "" };
          document.getElementById(this.tag + "notifications_close").click();
        })
        .finally(() => (this.savingData = false));
    },

    async getGroupsAnnouncement() {
      const groups = await this.$store.dispatch(
        "announcementTaskModule/getGroupsAnnouncement",
        this.announcement.id
      );

      this.groups = groups;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.modalNotifications {
  ::v-deep {
    .modal-body {
      background-color: var(--color-neutral-lighter);
    }
  }
}
</style>
