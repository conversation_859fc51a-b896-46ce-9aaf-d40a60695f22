<template>
<div class="modalActivateAnnouncement">
<BaseModal
    :identifier="tag + 'modalActivateAnnouncement'"
    :title="$t('ANNOUNCEMENT.GENERAL.ACTIVATE') || ''"
    padding="2rem"
    size="modal-md"
    modal-icon="fa-exclamation-triangle"
>
  <div class="d-flex flex-column">
    <div class="Errors d-grid w-100" v-if="showErrors">
      <div class="Errors--title d-grid">
        <span><i class="fa fa-exclamation-triangle"></i></span>
        <p>{{ $t('ANNOUNCEMENT.ERROR_TITLES.INFO') }}</p>
      </div>
      <div class="ErrorResultContainer">
        <div class="ErrorResult" v-if="generalErrors.length > 0" @click="displayErrors = generalErrors;">
          <span class="ErrorResult--total">{{ generalErrors.length }}</span>
          <span class="ErrorResult--name">{{ $t('ANNOUNCEMENT.ERROR_TITLES.GENERAL') }}</span>
        </div>
        <div class="ErrorResult" v-if="timingErrors.length > 0" @click="displayErrors = timingErrors;">
          <span class="ErrorResult--total">{{ timingErrors.length }}</span>
          <span class="ErrorResult--name">{{ $t('ANNOUNCEMENT.ERROR_TITLES.TEMPORALIZATION') }}</span>
        </div>
        <div class="ErrorResult" v-if="subsidizedErrors.length > 0" @click="displayErrors = subsidizedErrors;">
          <span class="ErrorResult--total">{{ subsidizedErrors.length }}</span>
          <span class="ErrorResult--name">{{ $t('ANNOUNCEMENT.FORM.STEPS.SUBSIDIZED') }}</span>
        </div>
        <div class="ErrorResult" v-if="groupsErrors.length > 0" @click="displayErrors = groupsErrors;">
          <span class="ErrorResult--total">{{ groupsErrors.length }}</span>
          <span class="ErrorResult--name">{{ $t('ANNOUNCEMENT.FORM.STEPS.GROUPS') }}</span>
        </div>
        <div class="ErrorResult" v-if="profileErrors.length > 0" @click="displayErrors = profileErrors;">
          <span class="ErrorResult--total">{{ profileErrors.length }}</span>
          <span class="ErrorResult--name">{{ $t('ANNOUNCEMENT.ERROR_TITLES.USER_PROFILES') }}</span>
        </div>
      </div>

      <div class="errors Errors--errors mt-3" v-if="displayErrors.length > 0">
        <p v-for="(error, index) in displayErrors" class="Errors--errors--section mb-0">
          <span class="Errors--errors--error">{{ $t(error + '') }}</span>
        </p>
      </div>
      <button @click.stop="errors = undefined" type="button" class="btn btn-primary ml-auto mr-auto mt-2">Aceptar</button>
    </div>
    <div v-if="loading" class="d-flex w-100 align-items-center justify-content-center">
      <spinner />
    </div>
    <div v-if="!loading && !showErrors" v-html="$t('ANNOUNCEMENT.MODALS.ACTIVATE_ANNOUNCEMENT_DESC')"></div>
    <div v-if="!loading && !showErrors" class="d-flex align-items-center justify-content-end">
      <button class="btn btn-sm btn-danger px-4"
              data-bs-dismiss="modal"
              aria-label="Close"
              ref="close-modal"
              >{{ $t('BACK') }}</button>
      <button type="button" class="btn btn-sm btn-primary px-4" @click="activateAnnouncement">{{ $t('CONFIRM') }}</button>
    </div>
  </div>
</BaseModal>
</div>
</template>

<script>
import {get} from "vuex-pathify";
import Spinner from "../../../../admin/components/base/Spinner.vue";
export default {
  name: "modalActivateAnnouncement",
  components: {Spinner},
  props: {
    tag: { type: String, default: '' },
  },
  data() {
    return {
      errors: undefined,
      loading: false,
      displayErrors: []
    };
  },
  computed: {
    announcement: get('announcementModule/announcement'),
    alerts() {
      return (new Array(3)).fill('Aviso Lorem Ipsum')
    },
    showErrors() {
      return this.generalErrors.length > 0  ||
          this.timingErrors.length > 0      ||
          this.subsidizedErrors.length > 0  ||
          this.groupsErrors.length > 0      ||
          this.profileErrors.length > 0;
    },
    generalErrors() {
      return this.errors?.GENERAL?.errors??[];
    },
    timingErrors() {
      return this.errors?.TIMING?.errors??[];
    },
    subsidizedErrors() {
      return this.errors?.SUBSIDIZED?.errors??[];
    },
    groupsErrors(){
      const errors = this.errors?.GROUPS?.errors??[];
      const keys = Object.keys(errors);
      let toReturn = [];
      if (keys.length === 0) return [];
      keys.forEach(k => {
        if(errors[k].length > 0)
        {
          errors[k].forEach(e => {
            toReturn.push(k  + ': ' + this.$t(e));
          })
        }
      })
      return toReturn;
    },
    profileErrors(){
      return this.errors?.INCOMPLETE_PROFILE?.errors??[];
    }
  },
  methods: {
    activateAnnouncement() {
      this.displayErrors = [];
      this.loading = true;
      this.$store.dispatch('announcementModule/activateAnnouncement', {
        idAnnouncement: this.announcement.id,
        notifyAt: new Date()
      }).then(r => {
        const { error, data } = r;
        this.$toast.clear();
        if (error) {
          this.$toast.error(this.$t('ANNOUNCEMENT.ACTIVATE.FAILED') + '');
          if ('exception' in r && r.exception) {
            this.$toast.error(data);
          } else {
            this.errors = data;
          }
        } else {
          this.$toast.success(this.$t('ANNOUNCEMENT.ACTIVATE.SUCCESS') + '');
          this.$store.dispatch('announcementModule/refreshAnnouncement');
          const btnClose = document.getElementById('modalActivateAnnouncement_close');
          if (btnClose) btnClose.click();
        }
      }).finally(() => {
        this.loading = false;
      })

    }
  }
}
</script>

 <style scoped lang="scss"> 
.modalActivateAnnouncement {
  .d-flex {
    gap: 1.5rem 0.5rem;
  }

  ul {
    list-style: none;
    padding: 0;
  }

  ::v-deep {
    .modal-body {
      background-color: var(--color-neutral-light);
    }
  }

  .Errors--errors, .Errors--errors--title, .Errors--errors--error, .Errors--errors--section {
    width: 100%;
  }

  .Errors {
    &--title {
      margin-bottom: 1rem;
      span {
        width: 100%;
        text-align: center;
        font-size: 3rem;
        color: #FA7A7E;
      }
      p {
        width: 100%;
        color: #FA7A7E;
        text-align: center;
      }
    }
  }

  .ErrorResultContainer {
    display: grid;
    grid-template-columns: repeat(auto-fit, 135px);
    gap: .25rem;
    .ErrorResult {
      cursor: pointer;
      background-color: #FFFFFF;
      display: flex;
      width: 135px;
      height: 90px;
      justify-content: center;
      align-items: flex-start;
      flex-flow: column nowrap;
      border: 1px solid var(--color-neutral-mid);
      border-radius: 4px;
      &--name {
        width: 100%;
        text-align: center;
      }
      &--total {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        background-color: $color-secondary-light !important;
        position: relative;
        color: #ffffff !important;
        margin-left: auto;
        margin-right: auto;
        text-align: center;
        &:after {
          content: '!';
          position: absolute;
          color: $color-secondary-light !important;
          right: -0.5rem;
          top: -0.3rem;
        }
      }
    }
  }

  .errors {
    display: grid;
    &--section {
      display: grid;
      .errors--title {
        font-weight: bold;
      }
    }
  }
}
</style>
