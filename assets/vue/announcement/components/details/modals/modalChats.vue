<template>
  <div class="modalChats">
    <div
      class="modal fade"
      :id="tag + 'userChat'"
      tabindex="-1"
      aria-labelledby="baseModalChats"
      aria-hidden="true"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      :data-modal-id="'#' + tag + 'userChat'"
    >
      <div :class="`modal-dialog modal-dialog-centered modal-xl`">
        <div class="modal-content">
          <div class="modal-header">
            <div
              class="modal-title d-flex align-items-center justify-content-between flex-column flex-sm-row w-100"
            >
              <h5 class="text-white m-0">
                <i class="fa fa-comment-alt mr-2"></i
                >{{ $t("ANNOUNCEMENT.INFOTAB.CHAT") }}
              </h5>
              <div class="d-flex align-items-center">
                <img :src="user.avatar || ''" alt="" class="userAvatar mr-3" />
                <h6 class="text-white m-0">{{ user.name }}</h6>
                <button
                  type="button"
                  class="btn-close ml-3 mt-0 mr-auto mb-0"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                  :data-modal-id="'#' + tag + 'userChat'"
                ></button>
              </div>
            </div>
          </div>
          <div class="modal-body">
            <div
              class="chatContent py-2 px-2 px-sm-5 mb-3"
              @scroll.passive="loadOldestMessages"
            >
              <div class="tag" v-if="loadingMessages">
                {{ $t("LOADING") }}...
              </div>
              <template v-for="chunk in messageList">
                <MessageBubble
                  v-for="message in chunk.messageList"
                  :userID="message.userId"
                  :seen="message.seen"
                  :createdAt="message.createdAt"
                  :content="message.message"
                  :tutor-id="tutorId"
                />
                <div class="tag">{{ chunk.date }}</div>
              </template>
              <div class="tag" v-if="loadingOldest">{{ $t("LOADING") }}...</div>
            </div>
            <div
              class="sentOption d-flex align-items-center justify-content-between"
              v-if="!onlyRead"
            >
              <EmojiSelector @clicked="appendEmoji" />
              <input
                type="text"
                class="form-control"
                v-model="textbox"
                :placeholder="$t('FORUM.WRITE_COMMENT')"
              />
              <button
                class="btn btn-sm btn-primary sendButton"
                @click="sendMessage"
                :disabled="sendingMessage || loadingMessages || loadingOldest"
              >
                <i class="fa fa-paper-plane"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import EmojiSelector from "../emojiSelector";
import MessageBubble from "../messageBubble";
import { get } from "vuex-pathify";
import { UtilsMixin } from "../../../mixins/utilsMixin";
export default {
  name: "modalChats",
  components: { MessageBubble, EmojiSelector },
  mixins: [UtilsMixin],
  props: {
    onlyRead: { type: Boolean, default: false },
    tag: { type: String, default: "" },
    tutorId: { type: Number, default: undefined },
  },
  data() {
    return {
      textbox: "",
      messages: [],
      sendingMessage: false,
      loadingMessages: false,
      loadingOldest: false,
    };
  },
  computed: {
    user: get("announcementModule/userSelected"),
    announcement: get("announcementModule/announcement"),
    userID() {
      return this.user.id || 0;
    },
    messageList() {
      let lastChunk = -1;
      let chunks = [];
      let currentName = "";
      const today = this.getTodayAsText();

      this.messages.forEach((message) => {
        const dateName = this.getOnlyDateText(message.createdAt);
        if (dateName !== currentName) {
          currentName = dateName;
          lastChunk += 1;
          chunks[lastChunk] = {
            date: dateName === today ? "Hoy" : dateName,
            messageList: [message],
          };
        } else {
          chunks[lastChunk].messageList = [
            ...chunks[lastChunk].messageList,
            message,
          ];
        }
      });

      return chunks;
    },
  },

  mounted() {
    //cargar nuevos mensajes cada 30 segundos
    // setInterval(() => {
    //   this.loadNewestMessages();
    //   this.loadingMessages = false;
    // }, 10000);
  },

  watch: {
    userID() {
      this.messages = [];
      if (this.userID) {
        if (this.user.channelId) {
          this.loadChat();
        } else {
          this.$store
            .dispatch("chatModule/getUserChat", {
              parentId: this.announcement.chatChannel,
              userId: this.userID,
              name: this.user?.name,
              entityId: this.user?.groupId,
              entityType: 'direct_GROUP_CHAT'
            })
            .then((response) => {
              this.user.channelId = response.data.id;
              this.$store.dispatch(
                "announcementModule/updateUserByID",
                this.user
              );
              this.loadChat();
            });
        }
      }
    },
  },
  methods: {
    appendEmoji(value) {
      this.textbox += value;
    },

    loadChat() {
      if (this.loadingMessages) return;
      if (this.user?.channelId == null) return;

      this.loadingMessages = true;
      this.$store
        .dispatch("chatModule/loadChatData", {
          channelId: this.user.channelId,
          newestId: null,
          oldestId: null,
        })
        .then((response) => {
          this.messages = response.data;
        })
        .finally(() => {
          this.loadingMessages = false;
        });
    },

    loadNewestMessages() {
      if (this.loadingMessages) return;
      if (!this.messages.length) return this.loadChat();

      this.loadingMessages = true;
      const newestId = this.messages[0].id;

      this.$store
        .dispatch("chatModule/loadChatData", {
          channelId: this.user.channelId,
          newestId,
          oldestId: null,
        })
        .then((response) => {
          this.messages = [
            ...response.data.filter((message) => message.id > newestId),
            ...this.messages,
          ];
        })
        .finally(() => {
          this.loadingMessages = false;
        });
    },

    loadOldestMessages(element) {
      if (this.loadingOldest) return;
      if (
        -(element.target.scrollTop - element.target.offsetHeight) >
        element.target.scrollHeight - 2
      ) {
        if (this.messages.length < 100) return;

        this.loadingOldest = true;
        const oldestId = this.messages[this.messages.length - 1].id;

        this.$store
          .dispatch("chatModule/loadChatData", {
            channelId: this.user.channelId,
            newestId: null,
            oldestId,
          })
          .then((response) => {
            this.messages = [
              ...this.messages,
              ...response.data.filter((message) => message.id < oldestId),
            ];
          })
          .finally(() => {
            this.loadingOldest = false;
          });
      }
    },

    sendMessage() {
      this.textbox = this.cleanString(this.textbox);
      if (!this.textbox.length) return;
      if (this.sendingMessage) return;

      this.sendingMessage = true;
      this.$toast.clear();
      this.$toast.info(`${this.$t("ANNOUNCEMENT.MODALS.SENDING_MESSAGE")}`);
      this.$store
        .dispatch("chatModule/sendMessage", {
          channelId: this.user.channelId,
          message: this.textbox,
        })
        .then(() => {
          this.textbox = "";
          this.loadNewestMessages();
          this.sendingMessage = false;
          this.$toast.clear();
          this.$toast.success(
            `${this.$t("ANNOUNCEMENT.MODALS.SUCCESS_SENT_MESSAGE")}`
          );
        })
        .catch(() => {
          this.$toast.clear();
          this.$toast.error(`${this.$t("ANNOUNCEMENT.STUDENTTAB.ERROR")}`);
        });
    },
  },
};
</script>

 <style scoped lang="scss"> 
.modalChats {
  .modal-content {
    overflow: hidden;
    .modal-body {
      padding: 1rem;
      background-color: var(--color-neutral-mid-light);
    }
  }
  .sentOption {
    gap: 0.5rem;
  }
  .userAvatar {
    width: 2rem;
    height: 2rem;
  }
  .chatContent {
    height: clamp(200px, 100vh, 600px);
    overflow: hidden auto;
    display: flex;
    flex-direction: column-reverse;
  }
  .sendButton {
    align-self: stretch;
    width: 2.7rem;
  }

  .writingTag,
  .tag {
    align-self: center;
    font-size: 0.7rem;
    margin: 1rem 0;
    color: var(--color-neutral-mid-darker);
  }

  .tag {
    background-color: var(--color-neutral-mid);
    padding: 0.25rem 1rem;
    border-radius: 3px;
  }
}
</style>
