<template>
<div class="taskProgressBar d-flex justify-content-between align-items-center" :class="{'selected': selected}">
  <span class="font-weight-bold mr-2">{{ title }}</span>
  <div class="d-flex justify-content-between align-items-center">
    <span class="text-primary ml-2 mr-2">{{value}}</span>
    <div class="progress">
      <div class="progress-bar" role="progressbar" :style="{width: value}"
           aria-valuemin="0" aria-valuemax="100">
      </div>
    </div>
  </div>
</div>
</template>

<script>
export default {
  name: "taskProgressBar",
  props: {
    title: {
      type: String,
      default: '',
    },
    avgValue: {
      type: Number,
      default: 0,
    },
    selected: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    value() { return `${Math.min(this.avgValue, 100)}%`; }
  }
}
</script>

 <style scoped lang="scss"> 
.taskProgressBar {
  padding: 0.5rem;
  border: 2px solid var(--color-neutral-mid);
  background-color: var(--color-neutral-lighter);
  border-radius: 5px;

  &.selected {
    border: 2px solid var(--color-primary-light);
    background-color: white;
  }
  .progress {
    width: 80px !important;
    height: 0.6rem;
    background-color: #dbdbdb;
  }
}
</style>
