<template>
<div class="progressBar text-center">
  <div class="progress" :style="{maxWidth: maxSize ? maxSize + 'px' : ''}">
    <div
        class="progress-bar"
        :class="bgClass"
        role="progressbar"
        aria-valuenow="100"
        aria-valuemin="0"
        aria-valuemax="100">
      <span>{{ valuePercent }}</span>
    </div>
  </div>
  <span class="underline cursor-pointer" :class="textColor">{{ text }}</span>
</div>
</template>

<script>
export default {
  name: "progressBar",
  props: {
    value: {
      type: Number,
      default: 0
    },
    showText: {
      type: Boolean,
      default: false
    },
    maxSize: {
      type: Number,
      default: 0
    }
  },
  computed: {
    valuePercent() {
      return `${Math.min(this.value, 100)}%`
    },
    bgClass() {
      if (this.value >= 80) return 'bg-success'
      if (this.value >= 60) return 'bg-primary'
      if (this.value >= 40) return 'bg-warning'
      return 'bg-danger'
    },
    text() {
      if (!this.showText) return '';
      return this.$t((this.value >= 100) ? 'COMPLETED' : 'INCOMPLETED');
    },
    textColor() {
      return (this.value >= 100) ? 'text-success' : 'text-danger';
    }
  }
}
</script>

 <style scoped lang="scss"> 
.progress {
  margin: 0 auto;
  user-select: none;

  .progress-bar {
    width: 100%;
  }
}
</style>
