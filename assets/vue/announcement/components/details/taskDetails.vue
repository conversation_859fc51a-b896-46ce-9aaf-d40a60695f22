<template>
<div class="taskDetails">
  <div class="row mb-4 mx-0" v-show="tasks.length">
    <div class="col-12 pl-5">
      <table class="table-condensed">
        <tbody>
        <tr>
          <td class="font-weight-bold">{{ $t('ANNOUNCEMENT.MODALS.PROGRESS_TASK2') }}:</td>
          <td class="font-weight-bold text-primary">{{ completedAvg || 0  }}%</td>
        </tr>
        <tr>
          <td>{{ $t('ANNOUNCEMENT.MODALS.PROGRESS_TASK3') }}:</td>
          <td class="font-weight-bold">{{ taskCompleted || 0 }} / <span class="text-primary">{{ tasks.length || 0 }}</span></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>

  <TaskAccordion
      v-for="(task, index) in tasks"
      class="py-2"
      :class="{'bg-gray': changeBackground && index % 2 !== 0, 'border-bottom': !changeBackground}"
      :key="'task-'+index"
      :user-id="userId"
      :allow-open="allowOpen"
      :task="task">
  </TaskAccordion>
  <DataNotFound :hide-on="!tasks.length" text="No hay tareas disponibles" icon="fa-tasks" :banner="true"/>
</div>
</template>

<script>
import TaskAccordion from "./taskAccordion";
import DataNotFound  from "./DataNotFound";
export default {
  name: "taskDetails",
  components: {DataNotFound, TaskAccordion},
  props: {
    tasks: {
      type: Array,
      default: () => ([])
    },
    userId: {
      type: Number,
      default: 0
    },
    allowOpen: {
      type: Boolean,
      default: true
    },
    changeBackground: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    completedAvg() {
      return Math.round(this.taskCompleted * 100 / this.tasks.length);
    },
    taskCompleted() {
      return this.tasks.filter((task) => task.state === 4).length
    }
  }
}
</script>

 <style scoped lang="scss"> 
.taskDetails {
  table td {
    width: 200px;
  }
  .progress-title {
    font-size: 1.2rem;
  }
  .bg-gray {
    background-color: var(--color-neutral-light);
  }
}
</style>
