<template>
  <div class="fragmentContent">
    <div class="fragementHeader">
      <i class="fa fa-exclamation" v-if="showAlert"></i>
      <span class="font-weight-bold"><i class="fa" :class="icon"></i> {{title}}</span>
      <span v-show="badge.length" class="badge bg-light text-dark ml-3">{{badge}}</span>
      <hr class="mt-1" v-if="showSeparator">
    </div>
    <div class="row">
      <short-answer-tag
          v-for="(detail, index) in details"
          :key="tag + index"
          class="col-md-4 col-xs-12 mt-2 fragmentDetail"
          :question="detail.question + (detail.question.length ? ':' : '')"
          :answer="detail.answer"
          :href="detail.href"/>
    </div>
    <slot></slot>
  </div>
</template>

<script>
import ShortAnswerTag from "./shortAnswerTag";
export default {
  name: "fragmentContent",
  components: {ShortAnswerTag},
  props: {
    tag: { type: String, default: 'fragment' },
    icon: { type: String, default: 'fa-users' },
    title: { type: String, default: 'Title' },
    badge: { type: String, default: '' },
    details: { type: Array, default: () => ([]) },
    showAlert: { type: Boolean, default: false },
    showSeparator: { type: Boolean, default: true },
  }
}
</script>

 <style scoped lang="scss"> 
.fragmentContent {
  position: relative;
  .fa-exclamation {
    position: absolute;
    left: -0.8rem;
    color: var(--color-dashboard-4);
  }
  .row {
    @media (max-width: 576px) {
      margin: 0 auto;
    }
  }
  hr { height: 0; }
}
</style>
