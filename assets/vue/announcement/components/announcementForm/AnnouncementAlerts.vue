<script>
import { get, sync } from "vuex-pathify";
import ButtonWithDescription from "../../../common/components/ButtonWithDescription";
import { configurationClientAnnouncement } from "../../mixins/configurationClientAnnouncement";


export default {
  name: "AnnouncementAlerts",
  components: {ButtonWithDescription },
  mixins: [configurationClientAnnouncement],

  computed: {
    type: get("announcementFormModule/announcement@type"),// Announcement type
    stepsConfigurations: get("announcementFormModule/stepsConfigurations"),// All steps configurations
    stepInfo() {
      return this.stepsConfigurations[this.type].steps.find(s => s.type === 'AnnouncementAlerts') ?? {};
    },
    announcementConfigurationTypes() {
      return this.stepInfo?.configurations ?? [];
    },

    alertsTypeTutorData: get("announcementFormModule/alertsTypeTutor"),
    alertTypeTutorValues: sync(
      "announcementFormModule/announcement@alertTypeTutorValues"
    ),
    configAnnouncement: sync(
        "announcementFormModule/announcement@configAnnouncement"
    ),
    alertsTypeTutor() {
      return this.alertsTypeTutorData.filter(t => {
        if (t.typeCourses.length === 0) return t;
        return t.typeCourses.find(tc => tc.type === this.type) !== undefined;
      })
    },

    configurationsClientInThisPage() {
      const configurations = [
        {
          type: "ALERT"
        },
      ];

      return this.fetchConfigurationByComponent(configurations);
    },
    alertsEnabled() {
      const conf = this.configurationsClientInThisPage.find(c => c.type === 'ALERT');
      return this.configAnnouncement[`${conf.vModelName}`];
    }
  },
  watch: {
    alertsEnabled(val, oldVal) {
      this.alertsTypeTutor.forEach((alert) => {
        this.$set(this.alertTypeTutorValues, `alert-${alert.id}`, val);
      });
    },
  },

  methods: {
    handleAlertTutor(alertTypeId) {
      const alertTypesIds = this.alertTypeTutorValues.includes(alertTypeId)
        ? this.alertTypeTutorValues.filter((id) => id !== alertTypeId)
        : [...this.alertTypeTutorValues, alertTypeId];

      this.$store.dispatch(
        "announcementFormModule/setAlertTypeTutorValues",
        alertTypesIds
      );
    },
  },
};
</script>

<template>
  <div class="AnnouncementAlerts">
    <button-with-description
      v-for="config in announcementConfigurationTypes"
      :key="config.id"
      :name="`configuration-${config.id}`"
      :title="config.name"
      :description="config.description"
      :image-url="config.image"
      v-model="configAnnouncement[`configuration-${config.id}`]"
    />

    <div class="AnnouncementAlerts--alerts left" v-if="this.alertsEnabled">
      <div v-for="alert in alertsTypeTutor"
           :key="alert.id"
           class="d-flex flex-row flex-nowrap align-items-center">
        <BaseSwitch :tag="`alert-${alert.id}`"
                     v-model="alertTypeTutorValues[`alert-${alert.id}`]"
                     :disabled="!alertsEnabled"
        />
        <span class="ml-3">{{ alert.name }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.AnnouncementAlerts {
  padding: 1rem 3rem;
  margin-top: 2rem;
  display: grid;
  gap: 1rem;

  grid-template-areas:
      "button ."
      "left left";
  @media #{min-medium-screen()} {
    grid-template-columns: repeat(2, 1fr);
  }

  .ButtonWithDescription {
    grid-area: button;
  }

  &--alerts {
    display: flex;
    flex-flow: column;
    gap: 1rem;

    .form-check {
      padding: 0;
    }
  }

  .AnnouncementAlerts--alerts.left {
    grid-area: left;
  }

  .AnnouncementAlerts--alerts.right {
    grid-area: right;
  }
}
</style>
