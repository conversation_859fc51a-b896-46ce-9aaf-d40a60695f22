<template>
  <div class="col-12 form-group">
    <label>{{ $t('COURSE.GENERAL_INFO') }}</label>
    <froala tag="textarea" v-model="data.generalInformation" :config="froalaGeneralInfoConfig"></froala>
  </div>
</template>

<script>
export default {
  name: "AnnouncementLastStep",
  props: {
    announcement: null
  },
  data() {
    return {
      data: {
        generalInformation: this.announcement?.generalInformation ?? '',
      },
      froalaGeneralInfoConfig: {}
    };
  },
  computed: {
    froalaConfig() {
      return {
        ...this.$store.getters['froalaEditorModule/getDefaultConfiguration'],
        height:500,
        pluginsEnabled: ['align', 'link', 'url', 'image', 'lists', 'file', 'paragraphStyle', 'paragraphFormat']
      };
    },
  },
  watch: {
    froalaConfig: {
      handler: function (preVal, newVal) {
        this.setConfig();
      },
      immediate: true// Observer called immediately after the start of the observation
    },
    data: {
      handler: function (old, newVal) {
        this.$emit('updated', this.data);
      },
      deep: true
    }
  },
  created() {
    this.setConfig();
  },
  methods: {
    setConfig() {
      this.froalaGeneralInfoConfig = this.froalaConfig;
    }
  }
}
</script>

 <style scoped lang="scss"> 

</style>
