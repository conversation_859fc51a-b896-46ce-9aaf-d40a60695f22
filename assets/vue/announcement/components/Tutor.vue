<template>
  <div class="Tutor">
    <div
      class="avatar"
      :style="{
        'background-image': `url(/uploads/users/avatars/${
          tutor.avatar ?? 'default.svg'
        })`,
      }"
    />
    <div class="Tutor--data">
      <h1 class="name">{{ tutor.name }}</h1>
      <div class="actions">
        <button
          class="btn btn-info"
          v-if="tutor.filename == null"
          type="button"
          @click="$emit('upload-cv', tutor)"
        >
          <i class="fa fa-upload"></i> {{ $t("UPLOAD_CV") }}
        </button>
        <button
          class="btn btn-info"
          v-if="tutor.filename != null"
          type="button"
          @click="open = true"
        >
          <i class="fa fa-eye"></i> {{ $t("VIEW_CV") }}
        </button>
        <button
          class="btn btn-danger"
          v-if="tutor.filename != null"
          type="button"
          @click="deleteCv()"
        >
          <i class="fa fa-trash"></i> {{ $t("DELETE_CV") }}
        </button>
      </div>
    </div>

    <viewer
      base-path="uploads/users/cv"
      :file="file"
      :modal="true"
      :custom="false"
      :open="open"
      @close="open = false"
    />
  </div>
</template>

<script>
import Viewer from "../../common/components/viewer/Viewer.vue";

export default {
  name: "Tutor",
  components: { Viewer },
  props: {
    tutor: null,
  },
  data() {
    return {
      open: false,
    };
  },
  computed: {
    file() {
      return {
        ...this.tutor,
        ...{
          type: "PDF",
        },
      };
    },
  },
  methods: {
    deleteCv() {
      this.$alertify.confirmWithTitle(
        this.$t("USER.CV.CONFIRM_DELETE.TITLE"),
        this.$t("USER.CV.CONFIRM_DELETE.DESCRIPTION"),
        async () => {
          const { error, data } = await this.$store.dispatch(
            "userModule/deleteUserCV",
            this.tutor.id
          );
          if (error) this.$toast.error(data);
          else {
            this.$toast.success(this.$t("FILE_UPLOAD.DELETE.SUCCESS") + "");
            this.$emit("refresh");
          }
        },
        () => {}
      );
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Tutor {
  display: grid;
  grid-template-columns: auto 1fr;
  background-color: #f1fbff;
  padding: 1rem 2rem;
  align-items: center;
  margin-top: 0.5rem;
  column-gap: 3rem;

  &--data {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;

    .name {
      font-size: 22px;
    }

    .actions {
      display: flex;
      flex-flow: column;
      * {
        margin: 0.15rem;
      }
    }
  }

  .avatar {
    @include avatar;
    width: 60px !important;
    height: 60px !important;
  }
}
</style>
