#user-stats {
  width: clamp(300px, 100%, 1400px);
  margin: 0 auto;

  .user-details-card {
    display: grid;
    grid-template-columns: 4rem auto;
    gap: 1rem;

    .user-image {
      align-self: start;

      img {
        width: 100%;
        aspect-ratio: 1;
        object-fit: cover;
        object-position: center;
        border-radius: 100%;
      }
    }

    .campusIcon {
      color: var(--color-neutral-mid);

      &.checked {
        color: var(--color-primary);
      }
    }
  }

  .nav-tabs-blue {
    .tab-pane.active,
    .nav-link.active,
    .tab-content {
      background-color: var(--color-primary-lighter);
      border-color: var(--color-primary-lighter);
    }
  }

  .badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    padding: 0.35rem 0.7rem;

    &.badge-light {
      color: var(--color-neutral-mid-dark);
    }
  }

  .dates {
    color: var(--color-neutral-mid-darker);
    font-size: 0.8rem;
  }

  .manually {
    background-color: var(--color-primary);
    color: var(--color-neutral-lightest);
    border-radius: 100%;
    width: 1rem;
    height: 1rem;
    display: inline-grid;
    place-items: center;
    font-size: 0.5rem;
  }

  .cards-info-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    flex: 0 0 100%;
    max-width: 100%;

    .cards-info {
      display: flex;
      flex-direction: column;
      width: fit-content;
      border-radius: 5px;
      overflow: hidden;
      border: 1px solid var(--color-neutral-mid);

      .card-info-header {
        color: var(--color-neutral-lightest);
        text-transform: uppercase;

        &.bg-primary {
          background-color: var(--color-primary-dark) !important;
        }
        &.bg-success {
          background-color: #00796b !important;
        }
      }

      .card-info-body {
        display: grid;
        grid-template-columns: 8rem auto;
        align-items: center;
        background-color: white;

        .card-info-legend {
          p {
            display: flex;
            align-items: center;
            gap: 1rem;

            .text-gray {
              color: var(--color-neutral-mid-dark);
            }
          }
        }
      }
    }
  }

  .btn-outline {
    border: 1px solid var(--color-primary) !important;
    color: var(--color-primary) !important;
    filter: grayscale(1);
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;

    &.active {
      filter: grayscale(0);
    }
  }

  .icon-merge {
    position: relative;
    display: inline-grid;
    width: 1.2rem;
    height: 1.2rem;
    color: inherit;
    place-items: center;

    i {
      position: absolute;

      &:first-child {
        color: inherit;
        font-size: 1rem;
      }

      &:last-child {
        color: white;
        font-size: 50%;
      }
    }
  }

  .course-image {
    width: 100%;
    max-width: 320px;
    height: auto;
    aspect-ratio: 1;
  }

  .modal-xl {
    max-width: 1400px !important;

    .modal-header {
      background-color: var(--color-neutral-darker);
      align-items: center;

      .modal-title {
        color: white;
      }
      .btn-close {
        filter: invert(1) grayscale(100%) brightness(200%);
      }
    }
  }

  .bg-gray {
    background-color: var(--color-neutral-mid-light);
  }

  .text-gray {
    color: var(--color-neutral-mid-darker);
  }

  .status-box {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid var(--color-primary);
    margin: auto 0 0;
  }

  .progressbar-text {
    font-size: 0.8rem;
  }

  .progress {
    height: 0.4rem;
  }
}

.form-userFieldsFundae {
  div.flex-fill {
    display: none;
  }
  display: grid;
  &.div {
    width: 100%;
  }

  @media screen and (min-width: 576px) {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  @media screen and (min-width: 768px) {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  @media screen and (min-width: 992px) {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }
}
