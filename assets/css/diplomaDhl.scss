@page {
    margin: 3mm;
    margin-header: 0mm;
    margin-footer: 0mm;
  }

  
.dhl-main-border {
    justify-content: center;
    align-items: center;
    border: 2px solid #1f59de; /* Borde más grueso */
    border-radius: 15px; /* Esquinas redondeadas */
    margin: 20px;
    padding: 15px;
    max-width: 100%;
  
    height: 100%;
    margin-top: 30px;
  }
  
  
  .dhl-title {
    font-size: 28px;
    color: #1f59de;
    text-align: center;
    font-weight: bold;  
  }
  .dhl-title p {
    margin-bottom: -20px;
  }
  .dhl-subtitle1 {
    font-size: 16px;
    text-align: center;
    font-weight: 600;
    
  }
  .dhl-subtitle2 {
    margin-top: 25px;
    font-size: 18px;
    color: black;
    text-align: center;
  }
  .dhl-content {
    margin-top: 10px;
    font-size: 16px;
    color: #333;
    line-height: 1.6; /* Espaciado entre líneas */
    text-align: justify;
    text-justify: inter-word;
    margin-bottom: 10px;
  }
  
  .dhl-subtitle3 {
    font-size: 18px;
    color: black;
    text-align: center;
    font-weight: 600;
  }
  .container-dhl-table{
      text-align: center;
   }
  
  .dhl-table {
    margin-top: 20px;
    width: 100%;
    border-collapse: collapse;
  }
  
  .dhl-table td {
    border: 1px solid #1f59de;
    padding: 5px;
    font-size: 16px;
    color: #333;
    
  }
  
  
  .dhl-td-center {
    text-align: center;
  }
  .dhl-gray-color{
    color: #524f4f;
  }
  .dhl-blue-color{
    color: #1f59de;
  }
  .dhl-footer {
    font-size: 15px;
    color: #1f59de;
    text-align: left;
    font-weight: 600;
  }
  .dhl-button-table{
    margin-top: 10px;
    font-size: 12px;
    color: black;
    text-align: justify;
    text-justify: inter-word;
  }
  