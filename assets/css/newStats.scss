.rauto, .r1fr1fr, .r2fr1fr, .r1fr2fr, .r1fr1frauto, .r3auto {
  display: grid;
  grid-template-rows: auto;
  gap: 1rem;
}

.RatingList, .Card, .Chart, .MasterDetail<PERSON>hart, .SimpleCard, .SmallCard, .StarsGraphics {
  page-break-inside: avoid !important;
}

.rauto { grid-template-columns: auto; }
.r3auto { grid-template-columns: 1fr 1fr 1fr; }
.r1fr1frauto { grid-template-columns: 2fr 1fr 2fr;}
.r1fr1fr { grid-template-columns: 1fr 1fr; }
.r1fr2fr { grid-template-columns: 1fr 2fr; }
.r2fr1fr { grid-template-columns: 2fr 1fr; }

.hc {
  .legend {
    font-size: 0.9rem;
    padding: 0.5rem 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    text-align: left;
    .icon { width: 1rem; aspect-ratio: 1; margin-right: 1rem;}
  }
}

.sorting {
  cursor: pointer;
}

.alignLeft {
  .highcharts-axis-labels.highcharts-xaxis-labels > span {
    left: 0.5rem !important;
  }
}

.hidePoints {
  rect.highcharts-point { opacity: 0 }
}

#print-content {
  width: 750px;
  margin: 0 auto;
  -webkit-print-color-adjust: exact !important;

  .r1fr1fr, .r1fr2fr, .r2fr1fr, .r3auto, .r1fr1frauto {
    grid-template-columns: auto !important;
  }

  .hc {
    margin: 0 auto;
  }

  .hideOnPrint {
    display: none;
  }
}

#general-stats > #print-content{
  display: none;
}

.wrapper {
  //width: clamp(340px, 100%, 1400px);
  .content-header {
    min-height: auto;
  }
}

.filterOptions {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: center;
  gap: 0.5rem;
}

.stats-panel .filters-section {
  margin-bottom: 0;
  padding: 0;

  .filter-tags {
    margin-bottom: 0;
  }
}

.highcharts-label-box.highcharts-tooltip-box {
  fill: rgba(255, 255, 255, 1);
}

.rotationAnimation {
  animation: rotate-center 0.6s linear infinite;
}

@keyframes rotate-center {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@media screen and (max-width: 1100px) {
  .lgAuto { grid-template-columns: auto; }
}

@media screen and (max-width: 800px) {
  .mdAuto { grid-template-columns: auto; }
}

@media screen and (max-width: 600px) {
  .smAuto { grid-template-columns: auto; }
}

@keyframes opacityAnimation {
  0% {
    opacity: 0.2;
  }
  100% {
    opacity: 0.8;
  }
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 1.5rem 0.5rem;
  gap: 1rem;

  i { color: #CFD8DC; }
}

.loading {
  .title-text, .text-block {
    width: clamp(30px, 90%, 300px);
    border-radius: 7px;
    height: 1rem;
    overflow: hidden;
  }

  .title-text {
    color: #90A4AE;
    background-color: #90A4AE;
  }

  .text-block {
    background-color: #37474F;
  }

  .loading-container, .title-text {
    animation: opacityAnimation 1.1s linear infinite alternate;
  }
}
