.view-leave-active {
  transition: opacity 1s ease-in-out, transform 0.5s ease;
}

.view-enter-active {
  transition: opacity 1s ease-in-out, transform 0.5s ease;
  transition-delay: 0.5s;
}

.view-enter, .view-leave-to {
  opacity: 0;
}

.view-enter-to, .view-leave {
  opacity: 1;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}

.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */
{
  opacity: 0;
}

.fa-animated {
  animation: rotationX 1s infinite;
}

@keyframes rotationX {
  from { transform: rotateZ(0); }
  to { transform: rotateZ(360deg); }
}
