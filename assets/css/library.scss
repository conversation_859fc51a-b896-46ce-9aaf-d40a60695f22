.content-body {
  padding: 0 !important;
}
.Library {
  .icon {
    position: relative;
    background: #ffffff;
    border-radius: 50%;
    padding: 15px;
    margin: 10px;
    width: 50px;
    height: 50px;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    .icon-tooltip {
      position: absolute;
      top: 0;
      font-size: 14px;
      background: #ffffff;
      color: #ffffff;
      padding: 5px 8px;
      border-radius: 5px;
      box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);
      opacity: 0;
      pointer-events: none;
      transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

      &::before {
        position: absolute;
        content: "";
        height: 8px;
        width: 8px;
        background: #ffffff;
        bottom: -3px;
        left: 50%;
        transform: translate(-50%) rotate(45deg);
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
      }
    }

    &:hover {
      background: #1877F2;
      color: #ffffff;

      .icon-tooltip {
        top: -45px;
        opacity: 1;
        visibility: visible;
        pointer-events: auto;
        background: #1877F2;
        color: #ffffff;
        text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.1);
        &::before {
          background: #1877F2;
          color: #ffffff;
        }
      }
    }

    &.warning:hover {
      background: #d39e00;
      color: #212529;

      .icon-tooltip {
        background: #d39e00;
        color: #212529;

        &::before {
          background: #d39e00;
          color: #212529;
        }
      }
    }
  }

  .library-category {
    width: 100%;
    padding: 0.5rem;
    max-height: 50vh;

    @media screen and (min-width: 425px) {
      width: 25%;
      max-height: 90vh;
      height: 90vh;
    }

    .category.card {
      cursor: pointer;
      padding: 0.75rem;
      flex-direction: column;
      align-items: center;
      margin-top: 0.5rem;

      .information{
        display: grid;
        flex: 1;
        gap: 0.125rem;
        font-size: .9rem;

        .cat-name{
          font-weight: 500;
        }
      }

      .actions {
        display: none;
      }

      &:hover {
        .actions {
          display: block;
        }
      }
    }
  }

  .library {
    height: 90vh;
    flex-grow: 1;
  }
}
