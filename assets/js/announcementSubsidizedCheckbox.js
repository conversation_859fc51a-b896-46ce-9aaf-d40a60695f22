import jQuery from 'jquery';

jQuery(function () {

    if (jQuery('#Announcement_subsidized').length) {
        jQuery('#Announcement_subsidized').change(announcementMaxUsers);
        announcementMaxUsers();
    }
});

function announcementMaxUsers() {
    let $subsidizedUnlocked = jQuery('#Announcement_subsidized');
    let $panelUnlocked = jQuery('#Announcement_subsidizer').parents('.field-form_panel').first();
    let unlocked = $subsidizedUnlocked.is(':checked');
    $panelUnlocked[unlocked ? 'show' : 'hide']()

    /*
    let fields = ['maxUsers', 'subsidizer'];
    fields.forEach(function (field, index){
        jQuery('#Announcement_' + field).parents('.form-group').first()[unlocked ? 'show' : 'hide']();
    });
     */
}
