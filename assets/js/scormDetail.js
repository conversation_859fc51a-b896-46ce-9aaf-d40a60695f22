import $ from "jquery";

$(function () {
    $("#forceMenuVisualization").on("change", function (){
        const id = $(this).data('scorm');
        const visible = $(this).prop("checked");

        let orderRequest = $.ajax({type: "POST", url: 'admin/scorm/menu-visualization/' + id, data: {visible: (visible) ? 1 : 0}});

        orderRequest.done(function (result) {
        });

        orderRequest.fail(function () {
            location.reload();
        });

    });

    $("#configFlagScore").on("change", function (){
        const id = $(this).data('scorm');
        const active = $(this).prop("checked");
        const rawScore =  $("#minScoreRequired").prop("value");
        let orderRequest = $.ajax({type: "POST", url: 'admin/scorm/activate-raw-score/' + id, data: {active: (active) ? 1 : 0, rawScore: (rawScore) ? rawScore : 50}});

        if (active) {
            $("#minScoreRequired").show();
            $("#minScoreCurrentValue").show();
            $("#minScoreRequired").prop('disabled', false);
            $("#minScoreCurrentValue").html(rawScore)
        }else{
            $("#minScoreRequired").prop('disabled', true);
            $("#minScoreRequired").hide();
            $("#minScoreCurrentValue").html("");
        }

        orderRequest.done(function (result) {
        });

        orderRequest.fail(function () {
            //location.reload();
        });

    });

    $("#minScoreRequired").on("change", function (){

        const id = $(this).data('scorm');
        const rawScore = $(this).prop("value");
        
       let orderRequest = $.ajax({type: "POST", url: 'admin/scorm/set-raw-score/' + id, data: {rawScore: (rawScore) ? rawScore : 50}});

        orderRequest.done(function (result) {
            $("#minScoreCurrentValue").html(rawScore)
        });

        orderRequest.fail(function () {
            //location.reload();
        });
    });

    $("#allowResetAfterCompleted").on("change", function (){
        const id = $(this).data('scorm');
        const allowReset = $(this).prop("checked");
        
       let orderRequest = $.ajax({type: "POST", url: 'admin/scorm/allow-reset/' + id, data: {allowReset: (allowReset) ? 1 : 0}});

        orderRequest.done(function (result) {
        });

        orderRequest.fail(function () {
            //location.reload();
        });
    });
});
