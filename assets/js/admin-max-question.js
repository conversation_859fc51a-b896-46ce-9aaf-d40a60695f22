import $ from 'jquery';

window.onload = function() {
    const maxQuestionsInput = document.getElementById("maxQuestion");
    const doAllQuestionsInput = document.getElementById('doAllQuestions');
    const baseUrl = document.getElementById("urlMaxQuestion").value;

    function sendMaxQuestion(maxQuestions, doAllQuestions = false) {
        console.log(doAllQuestions);
        const url = baseUrl + `&maxQuestion=${maxQuestions}&doAllQuestions=${doAllQuestions}`;
        $.ajax({
            url,
            method: "POST",
        });
    }

    let doAllQuestions = doAllQuestionsInput.checked;
    doAllQuestionsInput.onchange = () => {
        doAllQuestions = doAllQuestionsInput.checked;
        maxQuestionsInput.disabled = doAllQuestions;
        sendMaxQuestion(document.getElementById('maxQuestion').value, doAllQuestions);
    }

    maxQuestionsInput.onclick = function() {
        var rowCount = ($(".datagrid tr").length) - 1;
        document.getElementById("maxQuestion").setAttribute("max",rowCount);
    }

    maxQuestionsInput.onchange = function() {
        if (maxQuestionsInput.value < 1) {
            maxQuestionsInput.value = 1;
        } else if (maxQuestionsInput.value > maxQuestionsInput.max) {
            maxQuestionsInput.value = maxQuestionsInput.max;
        }
        sendMaxQuestion(maxQuestionsInput.value, doAllQuestions);
    }
    sendMaxQuestion(maxQuestionsInput.value, doAllQuestions);
  };
