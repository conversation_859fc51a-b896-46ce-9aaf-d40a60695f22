import '../css/admin.scss';

import $ from 'jquery';
import 'jquery-ui';
import 'jquery-ui/ui/widgets/sortable'

$(document).ready(function() {
    const image =$("img");
    image.attr("onerror","this.onerror=null; this.src='assets/chapters/default-image.svg'");
    $(".sortable").sortable({
        start: function(event, ui) {
            $(ui.helper).css('width', `${ $(ui.item).width() }px`);
        },
        stop: function(event, ui) {

            let posicion = 1;
            const orden = [];

            $(".sortable").children().each(function() {
                orden.push($(this).data('id'));
                $(this).find('.order').html(posicion);
                posicion++;
            });

            const orderUrl = $('.sortable').data('orderurl');

            let orderRequest = $.ajax({ type: "POST", url: orderUrl, data: { orden: orden } });

            orderRequest.done(function(result) {});

            orderRequest.fail(function() {
                location.reload();
            });

        },
    });

  
    // $( ".sortable" ).disableSelection();
});

